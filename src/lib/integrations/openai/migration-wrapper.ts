/**
 * Migration Wrapper for OpenAI Responses API
 * Provides backward compatibility while upgrading to structured outputs
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 2: OpenAI Responses API Upgrade
 */

import type { OpenAIResponse } from '@/lib/types';
import {
  cleanContent,
  cleanTitle,
  getCharacterReport,
} from '@/lib/utils/character-cleaning';
import {
  enhanceAndTranslateDualLanguage,
  enhanceContent,
  optimizeTitle,
  translateContent,
} from './responses-api-client';

/**
 * Note: calculateCanadianRelevance has been removed from this part of the pipeline
 * as relevance filtering is handled earlier in the content processing workflow
 */

/**
 * Backward compatible wrapper for enhanceGermanArticle
 * Migrates from old Chat Completions to new Responses API
 */
export async function enhanceGermanArticle(
  content: string
): Promise<OpenAIResponse> {
  try {
    console.log('🔄 Using new Responses API for German content enhancement...');

    // Enhance the content directly (relevance filtering done earlier in pipeline)
    const result = await enhanceContent('', content);

    // Convert to legacy format for backward compatibility
    return {
      success: true,
      text: result.enhancedContent,
      tokensUsed: 0, // Will be tracked by new metrics system
    };
  } catch (error) {
    console.error('❌ Responses API enhancement failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Backward compatible wrapper for literalTranslateToEnglish
 * Migrates from old Chat Completions to new Responses API
 */
export async function literalTranslateToEnglish(
  content: string
): Promise<OpenAIResponse> {
  try {
    console.log('🔄 Using new Responses API for literal translation...');

    // Check if content looks like a title (short, single line, typically under 200 chars)
    const isLikelyTitle =
      content.length < 200 &&
      !content.includes('\n') &&
      !content.includes('<p>');

    let result: any;
    if (isLikelyTitle) {
      // For title-like content, pass it as the title parameter, not content parameter
      console.log('🎯 Detected title-like content, translating as title...');
      result = await translateContent(content, 'Title translation only', {
        temperature: 0.1, // Low temperature for literal translation
      });

      // Use the translated title from the result
      return {
        success: true,
        text: result.translatedTitle,
        tokensUsed: 0, // Will be tracked by new metrics system
      };
    } else {
      // For longer content, use normal content translation (empty title, content as content)
      result = await translateContent('', content, {
        temperature: 0.1, // Low temperature for literal translation
      });

      // Convert to legacy format for backward compatibility
      return {
        success: true,
        text: result.translatedContent,
        tokensUsed: 0, // Will be tracked by new metrics system
      };
    }
  } catch (error) {
    console.error('❌ Responses API literal translation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Backward compatible wrapper for translateArticleToEnglish
 * Migrates from old Chat Completions to new Responses API
 */
export async function translateArticleToEnglish(
  content: string
): Promise<OpenAIResponse> {
  try {
    console.log('🔄 Using new Responses API for article translation...');

    const result = await translateContent('', content, {
      temperature: 0.3, // Higher temperature for enhanced translation
    });

    // Convert to legacy format for backward compatibility
    return {
      success: true,
      text: result.translatedContent,
      tokensUsed: 0, // Will be tracked by new metrics system
    };
  } catch (error) {
    console.error('❌ Responses API article translation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Backward compatible wrapper for generateArticleMetadata
 * Migrates from old Chat Completions to new Responses API
 */
export async function generateArticleMetadata(
  content: string,
  language: 'de' | 'en'
): Promise<OpenAIResponse> {
  try {
    console.log('🔄 Using new Responses API for metadata generation...');

    // Use title optimization as a simple metadata generation approach
    const titleResult = await optimizeTitle(
      content.substring(0, 200),
      language,
      {
        temperature: 0.2,
      }
    );

    // Convert to legacy format for backward compatibility
    const metadata = {
      title: titleResult.cleanedTitle || 'Generated Title',
      description: titleResult.seoOptimized || 'Generated description',
      keywords: ['financial', 'news', 'market'], // Default keywords
      author: 'BörsenBlick',
      publishDate: new Date().toISOString(),
      category: 'financial',
      sentiment: 'neutral',
      complexity: 'medium',
      wordCount: content.length / 5, // Rough estimate
      readingTime: Math.ceil(content.length / 1000), // Rough estimate
    };

    return {
      success: true,
      text: JSON.stringify(metadata),
      tokensUsed: 0, // Will be tracked by new metrics system
    };
  } catch (error) {
    console.error('❌ Responses API metadata generation failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Enhanced function for comprehensive content processing
 * Uses the new Responses API for optimal performance
 */
export async function processContentComprehensive(
  title: string,
  content: string,
  options: {
    includeRelevance?: boolean;
    includeTranslation?: boolean;
    includeEnhancement?: boolean;
    includeTitleOptimization?: boolean;
    temperature?: number;
  } = {}
): Promise<{
  success: boolean;
  relevance?: any;
  translation?: any;
  enhancement?: any;
  titleOptimization?: any;
  financialContent?: any;
  error?: string;
}> {
  try {
    console.log(
      '🚀 Starting comprehensive content processing with Responses API...'
    );

    const results: {
      success: boolean;
      relevance?: any;
      translation?: any;
      enhancement?: any;
      titleOptimization?: any;
      financialContent?: any;
    } = { success: true };

    // Relevance analysis (disabled - handled earlier in pipeline)
    if (options.includeRelevance !== false) {
      console.log(
        '⚠️ Relevance analysis disabled - handled earlier in content pipeline'
      );
      results.relevance = {
        relevanceScore: 75, // Default score
        reasoning: 'Relevance filtering handled earlier in content pipeline',
        category: 'financial',
      };
    }

    // Translation
    if (options.includeTranslation !== false) {
      results.translation = await translateContent(title, content, {
        temperature: options.temperature || 0.3,
      });
    }

    // Enhancement
    if (options.includeEnhancement !== false && results.relevance) {
      results.enhancement = await enhanceContent(title, content, {
        temperature: options.temperature || 0.3,
      });
    }

    // Title optimization
    if (options.includeTitleOptimization !== false) {
      results.titleOptimization = {
        german: await optimizeTitle(title, 'de', {
          temperature: options.temperature || 0.1,
        }),
      };

      if (results.translation) {
        results.titleOptimization.english = await optimizeTitle(
          results.translation.translatedTitle,
          'en',
          { temperature: options.temperature || 0.1 }
        );
      }
    }

    // Financial content processing (disabled - function not implemented)
    console.log(
      '💼 Financial content processing disabled - not implemented in current version'
    );
    results.financialContent = {
      category: 'financial',
      sentiment: 'neutral',
      complexity: 'medium',
      keywords: ['financial', 'market', 'news'],
    };

    console.log('✅ Comprehensive processing completed successfully');
    return results;
  } catch (error) {
    console.error('❌ Comprehensive processing failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}

/**
 * Enhanced dual-language content enhancement using Unified System
 * Single API call that enhances German content and provides English translation
 * Ensures consistency between German and English versions for multilingual website
 */
export async function newEnhanceAndTranslateContent(
  germanContent: string,
  keyPoints: string[] = [],
  originalGermanTitle?: string
): Promise<{
  success: boolean;
  enhancedGerman?: {
    title: string;
    content: string;
    summary: string;
  };
  enhancedEnglish?: {
    title: string;
    content: string;
    summary: string;
  };
  tokensUsed?: number;
  error?: string;
  characterCleaning?: any;
}> {
  try {
    console.log(
      '🚀 Using UNIFIED ENHANCEMENT SYSTEM for dual-language enhancement...'
    );

    // Import and use the unified enhancement system
    const { unifiedContentEnhancement } = await import('./unified-enhancement');

    const unifiedResult = await unifiedContentEnhancement(
      originalGermanTitle || 'Financial Article',
      germanContent,
      keyPoints,
      {
        temperature: 0.7,
        enableFallback: false, // No fallback - unified system only
        includeProcessingMetadata: true,
      }
    );

    if (!unifiedResult.success) {
      throw new Error(unifiedResult.error || 'Unified enhancement failed');
    }

    let result: any;

    // If fallback was used, we get legacy format directly
    if (unifiedResult.fallbackUsed && unifiedResult.legacyFormat) {
      console.log('⚠️ Fallback to legacy system was used');
      result = unifiedResult.legacyFormat;
    } else if (unifiedResult.legacyFormat) {
      // Use the legacy format transformation from unified result
      console.log('✅ Using transformed legacy format from unified result');
      result = unifiedResult.legacyFormat;
    } else {
      throw new Error('No result format available from unified enhancement');
    }

    // Apply character cleaning to all content
    const cleanedGermanTitle = cleanTitle(result.enhancedGerman.title);
    const cleanedGermanContent = cleanContent(result.enhancedGerman.content);
    const cleanedEnglishTitle = cleanTitle(result.enhancedEnglish.title);
    const cleanedEnglishContent = cleanContent(result.enhancedEnglish.content);

    // Use the summaries from the structured response
    const germanSummary = cleanContent(result.enhancedGerman.summary);
    const englishSummary = cleanContent(result.enhancedEnglish.summary);

    const characterReport = getCharacterReport(
      cleanedGermanTitle +
        cleanedGermanContent +
        cleanedEnglishTitle +
        cleanedEnglishContent
    );

    if (characterReport.hasIssues) {
      console.log(
        '🧹 Character cleaning applied to dual-language enhancement:'
      );
      console.log('   Issues found:', characterReport.issues.join(', '));
    }

    console.log('✅ Dual-language enhancement completed successfully');
    console.log(`   - German title: ${cleanedGermanTitle.substring(0, 50)}...`);
    console.log(
      `   - English title: ${cleanedEnglishTitle.substring(0, 50)}...`
    );
    console.log(
      `   - Translation quality: ${result.consistency?.translationQuality || 'good'}`
    );
    console.log(`   - Tokens used: ${unifiedResult.metrics?.tokenUsage || 0}`);
    console.log(
      `   - Processing time: ${unifiedResult.metrics?.processingTime || 0}ms`
    );

    return {
      success: true,
      enhancedGerman: {
        title: cleanedGermanTitle,
        content: cleanedGermanContent,
        summary: germanSummary,
      },
      enhancedEnglish: {
        title: cleanedEnglishTitle,
        content: cleanedEnglishContent,
        summary: englishSummary,
      },
      tokensUsed: unifiedResult.metrics.tokenUsage || 0,
      characterCleaning: characterReport,
    };
  } catch (error: any) {
    console.error('❌ Unified enhancement failed:', error);
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error',
    };
  }
}
