/**
 * OpenAI-Powered Content Processing Pipeline
 *
 * Provides intelligent content translation and formatting with structure preservation
 * and Lexical format integration for BörsenBlick content pipeline.
 */

import OpenAI from 'openai';
import {
  cleanContent,
  getCharacterReport,
} from '@/lib/utils/character-cleaning';
import { markdownToLexical } from '@/lib/utils/lexical';

const apiKey = process.env.OPENAI_API_KEY;

if (!apiKey) {
  console.warn(
    'OPENAI_API_KEY is not set. Content processor will not function.'
  );
}

// Create client only when API key is available
let client: OpenAI | null = null;
if (apiKey) {
  client = new OpenAI({
    apiKey,
  });
}

/**
 * Structure-aware translation prompt for preserving formatting
 */
const STRUCTURE_AWARE_TRANSLATION_PROMPT = `
You are a professional financial translator and content formatter. Your task is to:

1. **Translate** German financial content to English while preserving meaning
2. **Preserve Structure** - maintain all paragraph breaks, headings, and formatting from the original
3. **Output Structured Markdown** - ensure proper line breaks and paragraph separation

CRITICAL STRUCTURE PRESERVATION:
- PRESERVE ALL paragraph breaks from the original German content
- MAINTAIN all heading levels (# ## ###) exactly as they appear
- KEEP all bullet points and list formatting
- PRESERVE all line breaks between sections
- DO NOT merge paragraphs into one continuous block of text
- EACH PARAGRAPH MUST BE ON ITS OWN LINE
- PUT TWO LINE BREAKS (\\n\\n) BETWEEN EACH PARAGRAPH
- PUT ONE LINE BREAK (\\n) BEFORE AND AFTER EACH HEADING

CONTENT ANALYSIS:
- If content contains multiple announcements or news items, separate them clearly
- Identify section headers like "Ad Hoc Announcements", "Financial News", etc.
- Break up long concatenated text into logical paragraphs
- Detect company names, stock symbols, and financial data for proper formatting

FORMATTING REQUIREMENTS:
- Create clear paragraph breaks between different news items or announcements
- Add appropriate H3 headings for major sections (e.g., "Ad Hoc Announcements", "Market Updates")
- Separate individual company announcements into distinct paragraphs
- Ensure professional, SEO-friendly English
- Remove asterisks, emojis, foreign characters, and special Unicode characters
- Avoid geographical bias and focus on story quality
- Avoid hashtags, semicolons, excessive adjectives/adverbs
- Minimize overused words like 'robust', 'seamless', 'innovative'
- Avoid transition phrases like 'In conclusion', 'In summary'

MARKDOWN OUTPUT REQUIREMENTS:
- Use proper markdown syntax with line breaks (\\n\\n between paragraphs)
- Include descriptive H3 subheadings for different sections
- Use bullet points/lists for key insights when appropriate
- Maintain proper Markdown formatting with clear hierarchy
- Preserve appropriate white space for readability
- If content contains URLs or links, format them properly
- ENSURE the markdown contains actual line break characters (\\n)

OUTPUT FORMAT:
Return your response in this exact format with delimiters:

===TRANSLATION_START===
[Your translated markdown content here with proper line breaks and formatting]
===TRANSLATION_END===

===METADATA_START===
{
  "preservedFormatting": true/false,
  "success": true,
  "wordCount": number,
  "hasProperStructure": true/false
}
===METADATA_END===

CRITICAL: Between the TRANSLATION_START and TRANSLATION_END delimiters, include the actual markdown with real line breaks. Do NOT use JSON format for the markdown content as it strips formatting.

EXAMPLE OF PROPER FORMATTING:
===TRANSLATION_START===
# Main Heading

This is the first paragraph with proper spacing.

## Subheading

This is another paragraph after the subheading.

- Bullet point 1
- Bullet point 2

Final paragraph with conclusion.
===TRANSLATION_END===
`;

/**
 * Enhanced translation result interface
 */
interface TranslationResult {
  translatedMarkdown: string;
  success: boolean;
  preservedFormatting: boolean;
  wordCount: number;
  hasProperStructure: boolean;
  error?: string;
  tokensUsed?: number;
  characterCleaning?: {
    hasIssues: boolean;
    issues: string[];
    cleanedLength: number;
    originalLength: number;
  };
}

/**
 * Translate and format German content to English with structure preservation
 * Uses OpenAI to intelligently translate and format content in one step
 */
export async function translateAndFormatContent(
  germanContent: string
): Promise<TranslationResult> {
  if (!apiKey || !client) {
    return {
      translatedMarkdown: '',
      success: false,
      preservedFormatting: false,
      wordCount: 0,
      hasProperStructure: false,
      error: 'OpenAI API key not configured.',
    };
  }

  if (!germanContent || typeof germanContent !== 'string') {
    return {
      translatedMarkdown: '',
      success: false,
      preservedFormatting: false,
      wordCount: 0,
      hasProperStructure: false,
      error: 'Invalid German content provided.',
    };
  }

  // Add content length limit to prevent oversized AI responses
  const MAX_CONTENT_LENGTH = 50000; // ~50KB limit
  if (germanContent.length > MAX_CONTENT_LENGTH) {
    console.warn(
      `⚠️ Content too long (${germanContent.length} chars), truncating to ${MAX_CONTENT_LENGTH}`
    );
    germanContent = germanContent.substring(0, MAX_CONTENT_LENGTH) + '...';
  }

  try {
    console.log('🔄 Starting structure-aware translation...');

    const response = await client.chat.completions.create({
      model: 'gpt-4o',
      messages: [
        {
          role: 'system',
          content: STRUCTURE_AWARE_TRANSLATION_PROMPT,
        },
        {
          role: 'user',
          content: `Translate and format this German financial content: ${germanContent}`,
        },
      ],
      temperature: 0.3, // Lower temperature for more consistent translation
      max_tokens: 3000,
    });

    const text = response.choices[0]?.message?.content;
    const tokensUsed = response.usage?.total_tokens;

    if (!text) {
      return {
        translatedMarkdown: '',
        success: false,
        preservedFormatting: false,
        wordCount: 0,
        hasProperStructure: false,
        error: 'No response from OpenAI',
      };
    }

    // Parse the delimited response format
    let parsedResult: {
      translatedMarkdown?: string;
      lexicalJson?: object;
      preservedFormatting?: boolean;
      success?: boolean;
      wordCount?: number;
      hasProperStructure?: boolean;
    };
    try {
      // Extract the markdown content between delimiters
      const translationMatch = text.match(
        /===TRANSLATION_START===([\s\S]*?)===TRANSLATION_END===/
      );
      const metadataMatch = text.match(
        /===METADATA_START===([\s\S]*?)===METADATA_END===/
      );

      if (!translationMatch) {
        console.error('❌ No translation content found in delimited response');
        return {
          translatedMarkdown: '',
          success: false,
          preservedFormatting: false,
          wordCount: 0,
          hasProperStructure: false,
          error: 'No translation content found in response',
        };
      }

      const translatedMarkdown = translationMatch[1].trim();
      let metadata = {
        preservedFormatting: true,
        success: true,
        wordCount: 0,
        hasProperStructure: true,
      };

      // Try to parse metadata if present
      if (metadataMatch) {
        try {
          const metadataJson = JSON.parse(metadataMatch[1].trim());
          metadata = { ...metadata, ...metadataJson };
        } catch (metadataError) {
          console.warn('⚠️ Failed to parse metadata, using defaults');
        }
      }

      parsedResult = {
        translatedMarkdown,
        ...metadata,
      };

      console.log('✅ Successfully parsed delimited OpenAI response');
      console.log(`📊 Extracted markdown length: ${translatedMarkdown.length}`);
      console.log(
        `📊 Line breaks in markdown: ${(translatedMarkdown.match(/\n/g) || []).length}`
      );
    } catch (parseError) {
      console.error(
        '❌ Failed to parse delimited OpenAI response:',
        parseError
      );
      console.log('Raw response text:', text.substring(0, 500));

      return {
        translatedMarkdown: '',
        success: false,
        preservedFormatting: false,
        wordCount: 0,
        hasProperStructure: false,
        error: 'Failed to parse delimited response format',
      };
    }

    // Validate the response structure
    if (!parsedResult.translatedMarkdown || !parsedResult.success) {
      return {
        translatedMarkdown: '',
        success: false,
        preservedFormatting: false,
        wordCount: 0,
        hasProperStructure: false,
        error: 'Invalid response structure from OpenAI',
      };
    }

    // Clean the translated content
    const cleanedMarkdown = cleanContent(parsedResult.translatedMarkdown);
    const characterReport = getCharacterReport(parsedResult.translatedMarkdown);

    // Note: Lexical conversion will be handled separately in the calling code

    // Calculate word count
    const wordCount = cleanedMarkdown
      .split(/\s+/)
      .filter(word => word.length > 0).length;

    console.log('✅ Structure-aware translation completed successfully');
    if (characterReport.hasIssues) {
      console.log(
        '🧹 Character cleaning applied:',
        characterReport.issues.join(', ')
      );
    }

    return {
      translatedMarkdown: cleanedMarkdown,
      success: true,
      preservedFormatting: parsedResult.preservedFormatting || true,
      wordCount,
      hasProperStructure: parsedResult.hasProperStructure || true,
      tokensUsed,
      characterCleaning: characterReport,
    };
  } catch (error: unknown) {
    console.error('OpenAI structure-aware translation error:', error);
    return {
      translatedMarkdown: '',
      success: false,
      preservedFormatting: false,
      wordCount: 0,
      hasProperStructure: false,
      error: error instanceof Error ? error.message : 'OpenAI API error',
    };
  }
}

/**
 * Enhanced markdown to Lexical conversion with AI assistance
 * Handles complex formatting that basic conversion might miss
 */
export async function markdownToLexicalWithAI(
  markdown: string
): Promise<object> {
  if (!markdown || typeof markdown !== 'string') {
    return {};
  }

  try {
    // First try the standard conversion
    const standardResult = await markdownToLexical(markdown);

    // For now, return the standard result
    // In the future, we could add AI enhancement here if needed
    return standardResult;
  } catch (error) {
    console.error(
      'Error in AI-enhanced markdown to Lexical conversion:',
      error
    );
    // Fallback to empty Lexical document
    return {
      root: {
        children: [],
        direction: 'ltr',
        format: '',
        indent: 0,
        type: 'root',
        version: 1,
      },
    };
  }
}

/**
 * Validate translation quality and structure
 */
export function validateTranslationResult(result: TranslationResult): {
  isValid: boolean;
  issues: string[];
  recommendations: string[];
} {
  const issues: string[] = [];
  const recommendations: string[] = [];

  // Check basic success
  if (!result.success) {
    issues.push('Translation failed');
  }

  // Check content length
  if (result.wordCount < 50) {
    issues.push('Content too short (less than 50 words)');
    recommendations.push('Consider providing more detailed source content');
  }

  // Check formatting preservation
  if (!result.preservedFormatting) {
    issues.push('Formatting not properly preserved');
    recommendations.push('Review source content structure');
  }

  // Check structure
  if (!result.hasProperStructure) {
    issues.push('Content lacks proper structure');
    recommendations.push(
      'Ensure source content has clear paragraphs and sections'
    );
  }

  // Check character cleaning
  if (result.characterCleaning?.hasIssues) {
    recommendations.push(
      `Character cleaning applied: ${result.characterCleaning.issues.join(', ')}`
    );
  }

  return {
    isValid: issues.length === 0,
    issues,
    recommendations,
  };
}
