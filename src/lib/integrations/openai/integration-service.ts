/**
 * OpenAI Integration Service
 * High-level service for integrating Responses API with content processing pipeline
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 2: OpenAI Responses API Upgrade
 */

import {
  translateContent,
  enhanceContent,
  optimizeTitle,
  getPerformanceStats,
  healthCheck,
} from './responses-api-client';

import type {
  RelevanceAnalysis,
  ContentTranslation,
  ContentEnhancement,
  TitleOptimization,
  FinancialContent,
} from './schemas';

/**
 * Content processing pipeline configuration
 */
interface ProcessingConfig {
  enableRelevanceAnalysis: boolean;
  enableTranslation: boolean;
  enableEnhancement: boolean;
  enableTitleOptimization: boolean;
  relevanceThreshold: number;
  temperature: number;
  targetAudience: 'retail' | 'professional' | 'institutional';
}

/**
 * Default processing configuration
 */
const DEFAULT_CONFIG: ProcessingConfig = {
  enableRelevanceAnalysis: false, // Disabled - relevance filtering handled earlier in pipeline
  enableTranslation: true,
  enableEnhancement: true,
  enableTitleOptimization: true,
  relevanceThreshold: 30,
  temperature: 0.3,
  targetAudience: 'professional',
};

/**
 * Complete content processing pipeline result
 */
export interface ProcessingResult {
  success: boolean;
  originalContent: {
    title: string;
    content: string;
  };
  relevanceAnalysis?: RelevanceAnalysis;
  translation?: ContentTranslation;
  enhancement?: ContentEnhancement;
  titleOptimization?: {
    german: TitleOptimization;
    english?: TitleOptimization;
  };
  financialContent?: FinancialContent;
  processingTime: number;
  tokensUsed: number;
  estimatedCost: number;
  error?: string;
}

/**
 * Process German financial content through complete pipeline
 */
export async function processContentPipeline(
  title: string,
  content: string,
  config: Partial<ProcessingConfig> = {}
): Promise<ProcessingResult> {
  const startTime = Date.now();
  const finalConfig = { ...DEFAULT_CONFIG, ...config };

  const result: ProcessingResult = {
    success: false,
    originalContent: { title, content },
    processingTime: 0,
    tokensUsed: 0,
    estimatedCost: 0,
  };

  try {
    console.log('🚀 Starting content processing pipeline...');

    // Step 1: Relevance Analysis (Disabled - handled earlier in pipeline)
    if (finalConfig.enableRelevanceAnalysis) {
      console.log(
        '📊 Relevance analysis disabled - handled earlier in content pipeline'
      );
      // Note: Relevance filtering is now handled during RSS processing
      // This step is kept for backward compatibility but does nothing
    }

    // Step 2: Title Optimization (German)
    if (finalConfig.enableTitleOptimization) {
      console.log('🎯 Optimizing German title...');
      result.titleOptimization = {
        german: await optimizeTitle(title, 'de', {
          temperature: finalConfig.temperature,
        }),
      };
    }

    // Step 3: Content Translation
    if (finalConfig.enableTranslation) {
      console.log('🌐 Translating content to English...');
      result.translation = await translateContent(title, content, {
        temperature: finalConfig.temperature,
      });

      // Optimize English title if translation was successful
      if (finalConfig.enableTitleOptimization && result.translation) {
        console.log('🎯 Optimizing English title...');
        result.titleOptimization!.english = await optimizeTitle(
          result.translation.translatedTitle,
          'en',
          { temperature: finalConfig.temperature }
        );
      }
    }

    // Step 4: Content Enhancement (German)
    if (finalConfig.enableEnhancement && result.relevanceAnalysis) {
      console.log('✨ Enhancing German content for Canadian market...');
      result.enhancement = await enhanceContent(title, content, {
        temperature: finalConfig.temperature,
      });
    }

    // Step 5: Comprehensive Financial Processing (Disabled - function not implemented)
    console.log(
      '💼 Financial content processing disabled - not implemented in current version'
    );
    // Note: processFinancialContent function is not available in responses-api-client
    // This step is kept for future implementation

    // Calculate final metrics
    const endTime = Date.now();
    result.processingTime = endTime - startTime;

    const stats = getPerformanceStats();
    result.tokensUsed = stats.totalTokens;
    result.estimatedCost = stats.estimatedCost;

    result.success = true;
    console.log(`✅ Pipeline completed in ${result.processingTime}ms`);

    return result;
  } catch (error) {
    const endTime = Date.now();
    result.processingTime = endTime - startTime;
    result.error = error instanceof Error ? error.message : 'Unknown error';
    result.success = false;

    console.error('❌ Pipeline failed:', result.error);
    return result;
  }
}

/**
 * Quick relevance check for filtering content
 * Note: This function is deprecated - relevance filtering is now handled earlier in the pipeline
 */
export async function quickRelevanceCheck(
  title: string,
  content: string
): Promise<{ relevant: boolean; score: number; reasoning: string }> {
  console.log(
    '⚠️ quickRelevanceCheck is deprecated - relevance filtering handled earlier in pipeline'
  );

  // Return default "relevant" to maintain backward compatibility
  return {
    relevant: true,
    score: 100,
    reasoning: 'Relevance filtering handled earlier in content pipeline',
  };
}

/**
 * Batch process multiple articles with progress tracking
 */
export async function batchProcessArticles(
  articles: Array<{ id: string; title: string; content: string }>,
  config: Partial<ProcessingConfig> = {},
  onProgress?: (processed: number, total: number) => void
): Promise<Array<ProcessingResult & { id: string }>> {
  const results: Array<ProcessingResult & { id: string }> = [];

  for (let i = 0; i < articles.length; i++) {
    const article = articles[i];
    console.log(
      `Processing article ${i + 1}/${articles.length}: ${article.title.substring(0, 50)}...`
    );

    try {
      const result = await processContentPipeline(
        article.title,
        article.content,
        config
      );
      results.push({ ...result, id: article.id });
    } catch (error) {
      results.push({
        id: article.id,
        success: false,
        originalContent: { title: article.title, content: article.content },
        processingTime: 0,
        tokensUsed: 0,
        estimatedCost: 0,
        error: error instanceof Error ? error.message : 'Unknown error',
      });
    }

    if (onProgress) {
      onProgress(i + 1, articles.length);
    }
  }

  return results;
}

/**
 * Get service health and performance metrics
 */
export async function getServiceStatus() {
  const health = await healthCheck();
  const stats = getPerformanceStats();

  return {
    health: health.status,
    performance: stats,
    capabilities: {
      relevanceAnalysis: true,
      contentTranslation: true,
      contentEnhancement: true,
      titleOptimization: true,
      financialProcessing: true,
      batchProcessing: true,
    },
    lastUpdated: new Date().toISOString(),
  };
}

/**
 * Legacy compatibility wrapper for existing code
 */
export async function enhanceArticleContent(
  title: string,
  content: string,
  options: { temperature?: number } = {}
): Promise<{
  enhancedTitle: string;
  enhancedContent: string;
  translatedTitle: string;
  translatedContent: string;
  relevanceScore: number;
}> {
  const result = await processContentPipeline(title, content, {
    temperature: options.temperature || 0.3,
  });

  if (!result.success) {
    throw new Error(result.error || 'Content enhancement failed');
  }

  return {
    enhancedTitle: result.titleOptimization?.german.cleanedTitle || title,
    enhancedContent: result.enhancement?.enhancedContent || content,
    translatedTitle:
      result.titleOptimization?.english?.cleanedTitle ||
      result.translation?.translatedTitle ||
      title,
    translatedContent: result.translation?.translatedContent || content,
    relevanceScore: result.relevanceAnalysis?.relevanceScore || 0,
  };
}
