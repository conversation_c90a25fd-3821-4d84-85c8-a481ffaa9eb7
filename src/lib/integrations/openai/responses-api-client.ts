/**
 * OpenAI Responses API Client
 * High-performance structured output client for German financial content processing
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

import OpenAI from 'openai';
import { zodTextFormat } from 'openai/helpers/zod';
import type { z } from 'zod';
import {
  schemas,
  type ContentTranslation,
  type ContentEnhancement,
  type TitleOptimization,
  type DualLanguageEnhancement,
} from './schemas';

/**
 * Check if we're in build time (should not initialize clients)
 */
function isBuildTime(): boolean {
  return (
    (process.env.NODE_ENV === 'production' &&
      process.env.NEXT_PHASE === 'phase-production-build') ||
    process.argv.includes('build') ||
    process.env.BUILDING === 'true'
  );
}

// Initialize OpenAI client with error handling (only during runtime)
let openai: OpenAI | null = null;

if (!isBuildTime()) {
  try {
    if (process.env.OPENAI_API_KEY) {
      openai = new OpenAI({
        apiKey: process.env.OPENAI_API_KEY,
      });
      console.log('✅ OpenAI Responses API client initialized');
    } else {
      console.warn(
        '⚠️ OPENAI_API_KEY not found - Responses API will not function'
      );
    }
  } catch (error) {
    console.error('❌ Failed to initialize OpenAI client:', error);
  }
}

/**
 * Performance metrics tracking
 */
interface ProcessingMetrics {
  startTime: number;
  endTime?: number;
  tokensUsed?: number;
  cost?: number;
  success: boolean;
  operation: string;
}

const metrics: ProcessingMetrics[] = [];

/**
 * Track API call performance
 */
function trackMetrics(
  operation: string,
  startTime: number,
  success: boolean,
  tokensUsed?: number
): void {
  const endTime = Date.now();
  const cost = tokensUsed ? tokensUsed * 0.00002 : undefined; // Approximate cost calculation

  metrics.push({
    startTime,
    endTime,
    tokensUsed,
    cost,
    success,
    operation,
  });

  // Keep only last 100 metrics
  if (metrics.length > 100) {
    metrics.shift();
  }
}

/**
 * Get performance statistics
 */
export function getPerformanceStats() {
  const recent = metrics.slice(-20); // Last 20 calls
  const successful = recent.filter(m => m.success);

  return {
    totalCalls: recent.length,
    successRate: (successful.length / recent.length) * 100,
    averageLatency:
      successful.reduce(
        (acc, m) => acc + (m.endTime! - m.startTime) / 1000,
        0
      ) / successful.length,
    totalTokens: successful.reduce((acc, m) => acc + (m.tokensUsed || 0), 0),
    estimatedCost: successful.reduce((acc, m) => acc + (m.cost || 0), 0),
  };
}

/**
 * Helper function to make structured API calls using the Responses API
 */
async function makeStructuredCall<T>(
  schema: z.ZodSchema<T>,
  schemaName: string,
  systemPrompt: string,
  userPrompt: string,
  options: { temperature?: number; model?: string } = {}
): Promise<T> {
  if (!openai) {
    throw new Error('OpenAI client not initialized - check OPENAI_API_KEY');
  }

  const response = await openai.responses.parse({
    model: options.model || 'gpt-4o-2024-08-06',
    input: [
      { role: 'system', content: systemPrompt },
      { role: 'user', content: userPrompt },
    ],
    text: {
      format: zodTextFormat(schema, schemaName),
    },
  });

  if (!response.output_parsed) {
    throw new Error(`Failed to parse response for ${schemaName}`);
  }

  return response.output_parsed;
}

/**
 * Translate German content to English with structure preservation
 */
export async function translateContent(
  title: string,
  content: string,
  options: { temperature?: number } = {}
): Promise<ContentTranslation> {
  const startTime = Date.now();

  try {
    const systemPrompt = `You are a professional financial translator specializing in German to English translation.
    
    Translation Guidelines:
    - Maintain original structure and formatting
    - Preserve financial terminology accuracy
    - Remove emojis, asterisks, and special Unicode characters
    - Ensure clean, SEO-friendly English output
    - Maintain paragraph structure (3-5 sentences per paragraph)
    - Use proper financial terminology
    
    Focus on clarity and readability for Canadian financial audiences.`;

    const userPrompt = `Translate this German financial content to English:

    Title: ${title}

    Content: ${content}
    
    ${content === 'Title translation only' ? 'Note: This is a title-only translation request. Provide a direct literal translation of the title, and use minimal placeholder content for other required fields.' : ''}`;

    const result = await makeStructuredCall(
      schemas.contentTranslation,
      'content_translation',
      systemPrompt,
      userPrompt,
      { temperature: options.temperature }
    );

    trackMetrics('translateContent', startTime, true);
    return result;
  } catch (error) {
    trackMetrics('translateContent', startTime, false);
    console.error('Error translating content:', error);
    throw error;
  }
}

/**
 * Enhance German content for Canadian market context
 */
export async function enhanceContent(
  title: string,
  content: string,
  options: { temperature?: number } = {}
): Promise<ContentEnhancement> {
  const startTime = Date.now();

  try {
    const systemPrompt = `You are a financial content editor specializing in German financial content enhancement for Canadian markets.
    
    Enhancement Guidelines:
    - Add Canadian market context and implications
    - Maintain original German language
    - Enhance with Canadian investor perspective
    - Add relevant Canadian company comparisons where appropriate
    - Include market implications for Canadian investors
    - Maintain professional financial journalism tone
    - Avoid excessive adjectives and marketing language
    - Focus on factual analysis and insights`;

    const userPrompt = `Enhance this German financial content for Canadian market context:
    
    Title: ${title}
    
    Content: ${content}`;

    const result = await makeStructuredCall(
      schemas.contentEnhancement,
      'content_enhancement',
      systemPrompt,
      userPrompt,
      { temperature: options.temperature }
    );

    trackMetrics('enhanceContent', startTime, true);
    return result;
  } catch (error) {
    trackMetrics('enhanceContent', startTime, false);
    console.error('Error enhancing content:', error);
    throw error;
  }
}

/**
 * Optimize titles for SEO and clean formatting
 */
export async function optimizeTitle(
  title: string,
  language: 'de' | 'en' = 'de',
  options: { temperature?: number } = {}
): Promise<TitleOptimization> {
  const startTime = Date.now();

  try {
    const systemPrompt = `You are an SEO specialist focusing on financial content title optimization.
    
    Optimization Guidelines:
    - Remove emojis, asterisks, and special Unicode characters
    - Clean foreign characters that don't belong in ${language} text
    - Optimize for SEO (30-60 characters ideal)
    - Maintain financial terminology accuracy
    - Ensure readability and professional appearance
    - Preserve meaning while improving clarity
    
    Language: ${language === 'de' ? 'German' : 'English'}`;

    const userPrompt = `Optimize this ${language === 'de' ? 'German' : 'English'} financial title:
    
    Original Title: ${title}`;

    const result = await makeStructuredCall(
      schemas.titleOptimization,
      'title_optimization',
      systemPrompt,
      userPrompt,
      { temperature: options.temperature }
    );

    trackMetrics('optimizeTitle', startTime, true);
    return result;
  } catch (error) {
    trackMetrics('optimizeTitle', startTime, false);
    console.error('Error optimizing title:', error);
    throw error;
  }
}

/**
 * Health check for the Responses API
 */
export async function healthCheck(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  details: any;
}> {
  try {
    const testResult = await optimizeTitle(
      'Test Title für Health Check',
      'de',
      { temperature: 0.1 }
    );
    const stats = getPerformanceStats();

    return {
      status:
        stats.successRate > 90
          ? 'healthy'
          : stats.successRate > 70
            ? 'degraded'
            : 'unhealthy',
      details: {
        ...stats,
        lastTestSuccess: !!testResult.cleanedTitle,
        timestamp: new Date().toISOString(),
      },
    };
  } catch (error) {
    return {
      status: 'unhealthy',
      details: {
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
    };
  }
}

/**
 * Enhanced dual-language content enhancement using Responses API
 * Single API call that enhances German content and provides English translation
 * Ensures consistency between German and English versions for multilingual website
 */
export async function enhanceAndTranslateDualLanguage(
  title: string,
  content: string,
  keyPoints: string[] = [],
  options: { temperature?: number } = {}
): Promise<DualLanguageEnhancement> {
  const startTime = Date.now();

  try {
    const keyPointsText =
      keyPoints.length > 0
        ? `\n\nKey Points to Emphasize:\n${keyPoints.map(point => `- ${point}`).join('\n')}`
        : '';

    const systemPrompt = `You are a financial content editor specializing in enhancing German financial content for Canadian markets and providing consistent English translations.

Enhancement Guidelines:
- Add Canadian market context and implications
- Include relevant Canadian company comparisons where appropriate
- Improve readability with better paragraph structure and subheadings
- Maintain professional financial journalism tone
- Use clean HTML formatting suitable for Lexical editor conversion (use <h3>, <p>, <strong>, <em>, <ul>, <li> tags)
- Integrate the key points naturally into the content
- Ensure German and English versions have identical structure and content, just in different languages
- Focus on factual analysis and insights for Canadian investors`;

    const userPrompt = `Enhance this German financial content for Canadian market context and provide English translation:

Title: ${title}
Content: ${content}${keyPointsText}

Requirements:
1. Enhance the German content with Canadian market context
2. Translate the enhanced German content to English (maintaining identical structure)
3. Use clean HTML formatting (not Markdown) suitable for Lexical rich text editor
4. Ensure both versions are translations of each other, not separate enhancements
5. Include Canadian market implications and relevant company comparisons
6. Maintain professional financial journalism standards`;

    const result = await makeStructuredCall(
      schemas.dualLanguageEnhancement,
      'dual_language_enhancement',
      systemPrompt,
      userPrompt,
      { temperature: options.temperature || 0.7 }
    );

    trackMetrics('enhanceAndTranslateDualLanguage', startTime, true);
    return result;
  } catch (error) {
    trackMetrics('enhanceAndTranslateDualLanguage', startTime, false);
    console.error('Error in dual-language enhancement:', error);
    throw error;
  }
}
