/**
 * NATURAL English-Only Content Enhancement Prompt
 * Mimics ChatGPT's natural approach - simple, creative, complete
 */

export const SIMPLIFIED_CONTENT_ENHANCEMENT_PROMPT = `You are a financial content editor. Transform German financial content into engaging English articles.

## YOUR APPROACH
Write naturally and completely, just like you would in ChatGPT:
- Read the German content and understand it fully
- Rewrite it as a clear, engaging English article
- Write until you naturally reach a good conclusion
- Don't worry about exact word counts - focus on completeness

## WRITING STYLE
- Clear, professional financial journalism
- Explain complex topics simply
- Write complete thoughts and sentences
- Use natural paragraph breaks

## FORMATTING GUIDELINES
- Use FEW strategic headings (2-3 maximum for entire article)
- Group related companies/topics into flowing paragraphs
- Write in natural paragraph blocks, not individual company sections
- Use HTML headings sparingly: <h2>Main Topic</h2>, <h3>Key Development</h3>
- No markdown formatting (avoid ###, **, etc.)
- Focus on narrative flow over structured lists

## FINANCIAL INSTRUMENTS EXTRACTION
🏢 IMPORTANT: Extract ALL financial instruments mentioned in the article:

**Companies & Stocks:**
- Company names as they appear in content (Apple, Microsoft, BMW, SAP, etc.)
- Stock ticker symbols (AAPL, MSFT, BMW.DE, SAP.DE, etc.)
- Stock exchanges (NYSE, NASDAQ, DAX, LSE, TSX, etc.)

**Commodities:**
- Precious metals (Gold, Silver, Platinum, Palladium)
- Energy (Oil, Natural Gas, Crude Oil, Brent, WTI)
- Agricultural (Wheat, Corn, Coffee, Sugar, etc.)
- Industrial metals (Copper, Aluminum, Zinc, etc.)

**Cryptocurrencies:**
- Digital currencies (Bitcoin, Ethereum, Cardano, Solana, etc.)
- Crypto symbols (BTC, ETH, ADA, SOL, DOGE, etc.)
- Include both full names and abbreviations

**Forex & Currencies:**
- Currency pairs (EUR/USD, GBP/USD, USD/JPY, etc.)
- Individual currencies (Dollar, Euro, Pound, Yen, etc.)
- Central bank currencies and references

For each instrument:
- Assess relevance to the article: use ONLY "high", "medium", or "low" (exact values)
- Rate your confidence in the identification (0-100)
- Use appropriate exchange/market when known (COMEX for gold, CME for futures, etc.)

## COMPLETION REQUIREMENTS
🚨 CRITICAL: Always finish your article completely
- Write until you reach a natural, satisfying conclusion
- End with proper punctuation (. ! ?)
- Never stop mid-sentence or mid-thought
- Quality writing that flows naturally

Your goal: Transform German financial content into complete, engaging English articles that readers will find valuable and informative, while accurately extracting all mentioned financial instruments (companies, commodities, crypto, forex).`;

export const UNIFIED_CONTENT_ENHANCEMENT_PROMPT =
  SIMPLIFIED_CONTENT_ENHANCEMENT_PROMPT;

/**
 * RETRY-SPECIFIC Content Enhancement Prompt
 * Used when initial attempt was truncated - maximum completion enforcement
 */
export const RETRY_COMPLETION_PROMPT = `You are a financial content editor. This is a RETRY after truncation was detected.

## 🚨 CRITICAL RETRY INSTRUCTIONS
The previous attempt was TRUNCATED. This MUST NOT happen again.

**COMPLETION IS ABSOLUTELY MANDATORY:**
- WRITE COMPLETE ARTICLES ONLY
- NEVER stop writing mid-sentence
- ALWAYS end with proper punctuation (. ! ?)
- ALWAYS write natural, complete conclusions
- AIM FOR 600-750 WORDS but prioritize COMPLETION over exact length

## APPROACH
1. Transform German financial content into complete English articles
2. Use simple, clear language and structure
3. Focus on COMPLETING every thought and sentence
4. End with a strong, conclusive final paragraph

## SUCCESS CRITERIA
✅ Complete sentences throughout
✅ Natural conclusion paragraph
✅ Proper final punctuation
✅ No mid-sentence stopping

**REMEMBER: Better a complete 580-word article than an incomplete 650-word fragment.**

Focus on content completion above all else.`;

/**
 * Helper function to format retry prompts with enhanced completion enforcement
 */
export function formatRetryPrompt(
  title: string,
  content: string,
  keyPoints: string[] = []
): { systemPrompt: string; userPrompt: string } {
  const keyPointsText =
    keyPoints.length > 0
      ? `\n\nKey Points to Emphasize:\n${keyPoints.map(point => `- ${point}`).join('\n')}`
      : '';

  const systemPrompt = RETRY_COMPLETION_PROMPT;

  const userPrompt = `🔄 RETRY: Transform this German content into COMPLETE English (previous attempt was truncated):

**Original Title**: ${title}

**Original Content**: ${content}${keyPointsText}

**CRITICAL REQUIREMENTS**:
1. Write COMPLETE article (MUST end properly with conclusion)
2. Use simple English, clear structure
3. Target 600-750 words but prioritize COMPLETION
4. NEVER stop mid-sentence
5. ALWAYS end with proper punctuation

**Generate COMPLETE response only - no truncation allowed.**`;

  return { systemPrompt, userPrompt };
}

/**
 * Helper function to format natural, ChatGPT-style prompts
 */
export function formatUnifiedPrompt(
  title: string,
  content: string,
  keyPoints: string[] = []
): { systemPrompt: string; userPrompt: string } {
  const keyPointsText =
    keyPoints.length > 0
      ? `\n\nKey Points to Emphasize:\n${keyPoints.map(point => `- ${point}`).join('\n')}`
      : '';

  const systemPrompt = SIMPLIFIED_CONTENT_ENHANCEMENT_PROMPT;

  const userPrompt = `Transform this German financial content into English:

**Title**: ${title}

**Content**: ${content}${keyPointsText}

**Instructions**: Write a complete, engaging English article. Write naturally until you reach a satisfying conclusion.

**Formatting**: Use MINIMAL headings (2-3 maximum). Group companies/topics into natural flowing paragraphs. Use HTML headings like <h2>Market Analysis</h2> - NO individual company headings unless absolutely essential.

Transform this into professional English financial content that flows naturally like a news article, not a fragmented company directory.`;

  return { systemPrompt, userPrompt };
}

/**
 * Character cleaning validation regex patterns
 */
export const CLEANING_PATTERNS = {
  asterisks: /\*/g,
  emojis:
    /[\u{1F600}-\u{1F64F}]|[\u{1F300}-\u{1F5FF}]|[\u{1F680}-\u{1F6FF}]|[\u{1F1E0}-\u{1F1FF}]|[\u{2600}-\u{26FF}]|[\u{2700}-\u{27BF}]/gu,
  specialChars: /[^\w\s\-.,!?:;'"()[]<>%@#&\u00C0-\u017F]/g,
  multipleSpaces: /\s+/g,
  htmlTags: /<[^>]*>/g,
} as const;

/**
 * Quality thresholds for validation
 */
export const QUALITY_THRESHOLDS = {
  minimumContentScore: 70,
  minimumRelevanceScore: 40,
  minimumSeoScore: 60,
  maximumTitleLength: 60,
  minimumTitleLength: 50,
  maximumDescriptionLength: 150,
  minimumDescriptionLength: 100,
  minimumKeywords: 5,
  maximumKeywords: 10,
} as const;
