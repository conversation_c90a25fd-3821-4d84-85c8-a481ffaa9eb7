import Parser from 'rss-parser';

export interface RSSItem {
  title?: string;
  link?: string;
  description?: string;
  pubDate?: string;
  author?: string;
  guid?: string;
  categories?: string[];
  content?: string;
  contentSnippet?: string;
}

export interface RSSFeedData {
  title?: string;
  description?: string;
  link?: string;
  language?: string;
  lastBuildDate?: string;
  items: RSSItem[];
}

const parser = new Parser({
  timeout: 10000, // 10 second timeout
  headers: {
    'User-Agent': 'BörsenBlick RSS Reader/1.0 (+https://boersenblick.com)',
    Accept: 'application/rss+xml, application/xml, text/xml',
  },
  customFields: {
    item: [
      ['media:content', 'mediaContent'],
      ['content:encoded', 'contentEncoded'],
      ['dc:creator', 'creator'],
    ],
  },
});

/**
 * Parse an RSS feed from a URL
 */
export async function parseRSSFeed(url: string): Promise<RSSItem[]> {
  try {
    console.log(`📡 Parsing RSS feed: ${url}`);

    const feed = await parser.parseURL(url);

    if (!feed.items || feed.items.length === 0) {
      console.warn(`⚠️ No items found in RSS feed: ${url}`);
      return [];
    }

    console.log(
      `📄 Successfully parsed ${feed.items.length} items from ${url}`
    );

    // Transform and clean the items
    const items: RSSItem[] = feed.items.map((item: any) => ({
      title: item.title?.trim(),
      link: item.link?.trim(),
      description: item.contentSnippet?.trim() || item.description?.trim(),
      pubDate: item.pubDate || item.isoDate,
      author: item.creator || item.author,
      guid: item.guid,
      categories: item.categories,
      content: item.contentEncoded || item.content,
      contentSnippet: item.contentSnippet,
    }));

    // Filter out items without essential data
    const validItems = items.filter(
      item =>
        item.title &&
        item.link &&
        item.title.length > 10 && // Minimum title length
        item.link.startsWith('http') // Valid URL
    );

    console.log(`✅ Filtered to ${validItems.length} valid items`);
    return validItems;
  } catch (error: any) {
    console.error(`❌ Failed to parse RSS feed ${url}:`, error);
    throw new Error(`RSS parsing failed: ${error.message}`);
  }
}

/**
 * Parse RSS feed from XML string
 */
export async function parseRSSString(xmlString: string): Promise<RSSItem[]> {
  try {
    const feed = await parser.parseString(xmlString);

    if (!feed.items || feed.items.length === 0) {
      return [];
    }

    const items: RSSItem[] = feed.items.map((item: any) => ({
      title: item.title?.trim(),
      link: item.link?.trim(),
      description: item.contentSnippet?.trim() || item.description?.trim(),
      pubDate: item.pubDate || item.isoDate,
      author: item.creator || item.author,
      guid: item.guid,
      categories: item.categories,
      content: item.contentEncoded || item.content,
      contentSnippet: item.contentSnippet,
    }));

    return items.filter(
      item =>
        item.title &&
        item.link &&
        item.title.length > 10 &&
        item.link.startsWith('http')
    );
  } catch (error: any) {
    console.error('❌ Failed to parse RSS string:', error);
    throw new Error(`RSS string parsing failed: ${error.message}`);
  }
}

/**
 * Validate RSS feed URL
 */
export function isValidRSSUrl(url: string): boolean {
  try {
    const urlObj = new URL(url);
    return urlObj.protocol === 'http:' || urlObj.protocol === 'https:';
  } catch {
    return false;
  }
}

/**
 * Test RSS feed connectivity
 */
export async function testRSSFeed(
  url: string
): Promise<{ success: boolean; error?: string; itemCount?: number }> {
  try {
    if (!isValidRSSUrl(url)) {
      return { success: false, error: 'Invalid URL format' };
    }

    const items = await parseRSSFeed(url);
    return {
      success: true,
      itemCount: items.length,
    };
  } catch (error: any) {
    return {
      success: false,
      error: error.message,
    };
  }
}
