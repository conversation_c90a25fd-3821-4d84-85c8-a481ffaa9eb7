/**
 * Firecrawl Alerting and Monitoring System
 * Provides comprehensive alerting for Firecrawl failures and performance issues
 *
 * <AUTHOR> Development Team
 * @created 2025-06-30
 */

export interface FirecrawlAlert {
  id: string;
  timestamp: Date;
  severity: 'low' | 'medium' | 'high' | 'critical';
  type:
    | 'api_key_missing'
    | 'extraction_failed'
    | 'rate_limited'
    | 'timeout'
    | 'configuration_error'
    | 'performance_degraded';
  url?: string;
  message: string;
  details: {
    error?: string;
    strategy?: string;
    retryCount?: number;
    processingTime?: number;
    fallbackUsed?: boolean;
    siteConfig?: string;
  };
  context: {
    userAgent?: string;
    requestId?: string;
    feedId?: string;
    articleId?: string;
  };
}

export interface AlertingConfig {
  enableConsoleLogging: boolean;
  enableFileLogging: boolean;
  enableWebhookAlerts: boolean;
  webhookUrl?: string;
  logFilePath?: string;
  alertThresholds: {
    failureRate: number; // Percentage (0-100)
    responseTime: number; // Milliseconds
    consecutiveFailures: number;
  };
}

class FirecrawlAlertingService {
  private alerts: FirecrawlAlert[] = [];
  private config: AlertingConfig;
  private consecutiveFailures = 0;
  private totalRequests = 0;
  private failedRequests = 0;
  private lastResetTime = Date.now();

  constructor(config: Partial<AlertingConfig> = {}) {
    this.config = {
      enableConsoleLogging: true,
      enableFileLogging: false,
      enableWebhookAlerts: false,
      alertThresholds: {
        failureRate: 25, // Alert if >25% failure rate
        responseTime: 30000, // Alert if >30s response time
        consecutiveFailures: 3, // Alert after 3 consecutive failures
      },
      ...config,
    };
  }

  /**
   * Record a Firecrawl operation result
   */
  recordOperation(
    success: boolean,
    url: string,
    processingTime: number,
    details: {
      error?: string;
      strategy?: string;
      retryCount?: number;
      fallbackUsed?: boolean;
      siteConfig?: string;
    } = {},
    context: FirecrawlAlert['context'] = {}
  ): void {
    this.totalRequests++;

    if (success) {
      this.consecutiveFailures = 0;

      // Check for performance degradation
      if (processingTime > this.config.alertThresholds.responseTime) {
        this.createAlert({
          severity: 'medium',
          type: 'performance_degraded',
          url,
          message: `Firecrawl extraction took ${processingTime}ms (threshold: ${this.config.alertThresholds.responseTime}ms)`,
          details: { ...details, processingTime },
          context,
        });
      }
    } else {
      this.failedRequests++;
      this.consecutiveFailures++;

      // Determine alert severity based on failure type and count
      const severity = this.determineSeverity(
        details.error,
        this.consecutiveFailures
      );
      const alertType = this.determineAlertType(details.error);

      this.createAlert({
        severity,
        type: alertType,
        url,
        message: `Firecrawl extraction failed: ${details.error || 'Unknown error'}`,
        details: { ...details, processingTime },
        context,
      });

      // Check for consecutive failure threshold
      if (
        this.consecutiveFailures >=
        this.config.alertThresholds.consecutiveFailures
      ) {
        this.createAlert({
          severity: 'high',
          type: 'extraction_failed',
          url,
          message: `${this.consecutiveFailures} consecutive Firecrawl failures detected`,
          details: { ...details, processingTime },
          context,
        });
      }
    }

    // Check overall failure rate (reset every hour)
    const hoursSinceReset =
      (Date.now() - this.lastResetTime) / (1000 * 60 * 60);
    if (hoursSinceReset >= 1) {
      this.checkFailureRate();
      this.resetCounters();
    }
  }

  /**
   * Record API key missing error
   */
  recordApiKeyMissing(): void {
    this.createAlert({
      severity: 'critical',
      type: 'api_key_missing',
      message: 'FIRECRAWL_API_KEY environment variable is not set',
      details: {},
      context: {},
    });
  }

  /**
   * Record configuration error
   */
  recordConfigurationError(error: string, siteConfig?: string): void {
    this.createAlert({
      severity: 'high',
      type: 'configuration_error',
      message: `Firecrawl configuration error: ${error}`,
      details: { error, siteConfig },
      context: {},
    });
  }

  /**
   * Get recent alerts
   */
  getRecentAlerts(hours: number = 24): FirecrawlAlert[] {
    const cutoff = new Date(Date.now() - hours * 60 * 60 * 1000);
    return this.alerts.filter(alert => alert.timestamp >= cutoff);
  }

  /**
   * Get alert summary
   */
  getAlertSummary(): {
    total: number;
    bySeverity: Record<string, number>;
    byType: Record<string, number>;
    consecutiveFailures: number;
    failureRate: number;
  } {
    const recentAlerts = this.getRecentAlerts();

    const bySeverity = recentAlerts.reduce(
      (acc, alert) => {
        acc[alert.severity] = (acc[alert.severity] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const byType = recentAlerts.reduce(
      (acc, alert) => {
        acc[alert.type] = (acc[alert.type] || 0) + 1;
        return acc;
      },
      {} as Record<string, number>
    );

    const failureRate =
      this.totalRequests > 0
        ? (this.failedRequests / this.totalRequests) * 100
        : 0;

    return {
      total: recentAlerts.length,
      bySeverity,
      byType,
      consecutiveFailures: this.consecutiveFailures,
      failureRate: Math.round(failureRate * 100) / 100,
    };
  }

  /**
   * Get actual Firecrawl request statistics (not alert counts)
   * This fixes the bug where alert counts were being reported as request counts
   */
  getRequestStats(): {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    consecutiveFailures: number;
    failureRate: number;
  } {
    const successfulRequests = Math.max(
      0,
      this.totalRequests - this.failedRequests
    );
    const successRate =
      this.totalRequests > 0
        ? Math.round((successfulRequests / this.totalRequests) * 100)
        : 100;
    const failureRate =
      this.totalRequests > 0
        ? Math.round((this.failedRequests / this.totalRequests) * 100)
        : 0;

    return {
      totalRequests: this.totalRequests,
      successfulRequests,
      failedRequests: this.failedRequests,
      successRate,
      consecutiveFailures: this.consecutiveFailures,
      failureRate,
    };
  }

  private createAlert(
    alertData: Omit<FirecrawlAlert, 'id' | 'timestamp'>
  ): void {
    const alert: FirecrawlAlert = {
      id: `firecrawl_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      timestamp: new Date(),
      ...alertData,
    };

    this.alerts.push(alert);

    // Keep only last 1000 alerts to prevent memory issues
    if (this.alerts.length > 1000) {
      this.alerts = this.alerts.slice(-1000);
    }

    this.processAlert(alert);
  }

  private processAlert(alert: FirecrawlAlert): void {
    if (this.config.enableConsoleLogging) {
      this.logToConsole(alert);
    }

    if (this.config.enableFileLogging && this.config.logFilePath) {
      this.logToFile(alert);
    }

    if (this.config.enableWebhookAlerts && this.config.webhookUrl) {
      this.sendWebhookAlert(alert);
    }
  }

  private logToConsole(alert: FirecrawlAlert): void {
    const emoji = this.getSeverityEmoji(alert.severity);
    const timestamp = alert.timestamp.toISOString();

    console.error(`${emoji} [FIRECRAWL ALERT] ${timestamp}`);
    console.error(`   Severity: ${alert.severity.toUpperCase()}`);
    console.error(`   Type: ${alert.type}`);
    console.error(`   Message: ${alert.message}`);

    if (alert.url) {
      console.error(`   URL: ${alert.url}`);
    }

    if (Object.keys(alert.details).length > 0) {
      console.error(`   Details:`, alert.details);
    }

    if (Object.keys(alert.context).length > 0) {
      console.error(`   Context:`, alert.context);
    }
  }

  private async logToFile(alert: FirecrawlAlert): Promise<void> {
    try {
      const fs = await import('fs/promises');
      const logEntry = JSON.stringify(alert) + '\n';
      await fs.appendFile(this.config.logFilePath!, logEntry);
    } catch (error) {
      console.error('Failed to write Firecrawl alert to file:', error);
    }
  }

  private async sendWebhookAlert(alert: FirecrawlAlert): Promise<void> {
    try {
      const response = await fetch(this.config.webhookUrl!, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: `🚨 Firecrawl Alert: ${alert.message}`,
          alert,
        }),
      });

      if (!response.ok) {
        console.error('Failed to send webhook alert:', response.statusText);
      }
    } catch (error) {
      console.error('Failed to send webhook alert:', error);
    }
  }

  private determineSeverity(
    error?: string,
    consecutiveFailures: number = 0
  ): FirecrawlAlert['severity'] {
    if (!error) return 'medium';

    // Critical errors
    if (error.includes('API key') || error.includes('authentication')) {
      return 'critical';
    }

    // High severity errors
    if (error.includes('Rate limited') || consecutiveFailures >= 3) {
      return 'high';
    }

    // Medium severity errors
    if (
      error.includes('timeout') ||
      error.includes('502') ||
      error.includes('503')
    ) {
      return 'medium';
    }

    // Default to low severity
    return 'low';
  }

  private determineAlertType(error?: string): FirecrawlAlert['type'] {
    if (!error) return 'extraction_failed';

    if (error.includes('API key') || error.includes('authentication')) {
      return 'api_key_missing';
    }

    if (error.includes('Rate limited') || error.includes('429')) {
      return 'rate_limited';
    }

    if (error.includes('timeout')) {
      return 'timeout';
    }

    if (error.includes('Bad Request') || error.includes('configuration')) {
      return 'configuration_error';
    }

    return 'extraction_failed';
  }

  private getSeverityEmoji(severity: FirecrawlAlert['severity']): string {
    switch (severity) {
      case 'critical':
        return '🔴';
      case 'high':
        return '🟠';
      case 'medium':
        return '🟡';
      case 'low':
        return '🔵';
      default:
        return '⚪';
    }
  }

  private checkFailureRate(): void {
    if (this.totalRequests === 0) return;

    const failureRate = (this.failedRequests / this.totalRequests) * 100;

    if (failureRate > this.config.alertThresholds.failureRate) {
      this.createAlert({
        severity: 'high',
        type: 'extraction_failed',
        message: `High failure rate detected: ${failureRate.toFixed(1)}% (threshold: ${this.config.alertThresholds.failureRate}%)`,
        details: {
          error: `${this.failedRequests}/${this.totalRequests} requests failed`,
        },
        context: {},
      });
    }
  }

  private resetCounters(): void {
    this.totalRequests = 0;
    this.failedRequests = 0;
    this.lastResetTime = Date.now();
  }
}

// Global alerting service instance
export const firecrawlAlerting = new FirecrawlAlertingService({
  enableConsoleLogging: true,
  enableFileLogging: process.env.NODE_ENV === 'production',
  logFilePath: './logs/firecrawl-alerts.log',
  alertThresholds: {
    failureRate: 25,
    responseTime: 30000,
    consecutiveFailures: 3,
  },
});

// Convenience functions
export const recordFirecrawlSuccess = (
  url: string,
  processingTime: number,
  details: any = {},
  context: any = {}
) => {
  firecrawlAlerting.recordOperation(
    true,
    url,
    processingTime,
    details,
    context
  );
};

export const recordFirecrawlFailure = (
  url: string,
  processingTime: number,
  error: string,
  details: any = {},
  context: any = {}
) => {
  firecrawlAlerting.recordOperation(
    false,
    url,
    processingTime,
    { ...details, error },
    context
  );
};

export const recordFirecrawlApiKeyMissing = () => {
  firecrawlAlerting.recordApiKeyMissing();
};

export const recordFirecrawlConfigError = (
  error: string,
  siteConfig?: string
) => {
  firecrawlAlerting.recordConfigurationError(error, siteConfig);
};
