/**
 * Enhanced Firecrawl Client Tests
 *
 * Test suite for the enhanced Firecrawl client with multi-format extraction,
 * site-specific configurations, and German financial site optimization.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 3: Enhanced Firecrawl Client
 */

import { describe, test, expect, beforeAll } from '@jest/globals';
import {
  extractContentEnhanced,
  getSiteConfiguration,
  type EnhancedExtractionResult,
  type SiteConfiguration,
} from '../enhanced-client';

// Test URLs for German financial sites
const TEST_URLS = {
  finanzenNet: 'https://www.finanzen.net/nachricht/aktien/dax-aktuell-12345678',
  handelsblatt:
    'https://www.handelsblatt.com/finanzen/maerkte/aktien/dax-analyse-12345678.html',
  wallstreetOnline:
    'https://www.wallstreet-online.de/nachricht/dax-prognose-12345678',
  boerse: 'https://www.boerse.de/nachrichten/DAX-Analyse-12345678',
  managerMagazin:
    'https://www.manager-magazin.de/finanzen/boerse/dax-entwicklung-12345678',
  unknown: 'https://example.com/test-article',
};

describe('Enhanced Firecrawl Client', () => {
  beforeAll(() => {
    // Ensure API key is available for tests
    if (!process.env.FIRECRAWL_API_KEY) {
      console.warn('FIRECRAWL_API_KEY not set - some tests may be skipped');
    }
  });

  describe('Site Configuration', () => {
    test('should return correct configuration for finanzen.net', () => {
      const config = getSiteConfiguration(TEST_URLS.finanzenNet);
      expect(config).toBeTruthy();
      expect(config?.domain).toBe('finanzen.net');
      expect(config?.name).toBe('Finanzen.net');
      expect(config?.timeout).toBe(45000);
      expect(config?.waitFor).toBe(4000);
      expect(config?.features?.adBlocking).toBe(true);
    });

    test('should return correct configuration for handelsblatt.com', () => {
      const config = getSiteConfiguration(TEST_URLS.handelsblatt);
      expect(config).toBeTruthy();
      expect(config?.domain).toBe('handelsblatt.com');
      expect(config?.name).toBe('Handelsblatt');
      expect(config?.features?.stealth).toBe(true);
      expect(config?.cookieHandling?.acceptSelectors).toContain(
        '.sp_choice_type_11'
      );
    });

    test('should return correct configuration for wallstreet-online.de', () => {
      const config = getSiteConfiguration(TEST_URLS.wallstreetOnline);
      expect(config).toBeTruthy();
      expect(config?.domain).toBe('wallstreet-online.de');
      expect(config?.name).toBe('Wallstreet Online');
      expect(config?.timeout).toBe(40000);
    });

    test('should return correct configuration for boerse.de', () => {
      const config = getSiteConfiguration(TEST_URLS.boerse);
      expect(config).toBeTruthy();
      expect(config?.domain).toBe('boerse.de');
      expect(config?.name).toBe('Börse.de');
      expect(config?.cookieHandling?.acceptSelectors).toContain('.gdpr-accept');
    });

    test('should return correct configuration for manager-magazin.de', () => {
      const config = getSiteConfiguration(TEST_URLS.managerMagazin);
      expect(config).toBeTruthy();
      expect(config?.domain).toBe('manager-magazin.de');
      expect(config?.name).toBe('Manager Magazin');
      expect(config?.waitFor).toBe(4000);
    });

    test('should return null for unknown domains', () => {
      const config = getSiteConfiguration(TEST_URLS.unknown);
      expect(config).toBeNull();
    });

    test('should handle invalid URLs gracefully', () => {
      const config = getSiteConfiguration('not-a-url');
      expect(config).toBeNull();
    });
  });

  describe('Enhanced Content Extraction', () => {
    // Skip actual API tests if no API key is available
    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should extract content with enhanced features',
      async () => {
        const result = await extractContentEnhanced(TEST_URLS.finanzenNet);

        expect(result).toBeTruthy();
        expect(result.url).toBe(TEST_URLS.finanzenNet);
        expect(result.timestamp).toBeInstanceOf(Date);

        // Check metadata structure
        expect(result.metadata).toBeTruthy();
        expect(typeof result.metadata.wordCount).toBe('number');
        expect(typeof result.metadata.readingTime).toBe('number');
        expect(typeof result.metadata.isFinancialContent).toBe('boolean');
        expect(typeof result.metadata.hasGermanContent).toBe('boolean');
        expect(['low', 'medium', 'high']).toContain(
          result.metadata.contentComplexity
        );
        expect(result.metadata.qualityScore).toBeGreaterThanOrEqual(0);
        expect(result.metadata.qualityScore).toBeLessThanOrEqual(100);

        // Check performance metrics
        expect(result.performance).toBeTruthy();
        expect(typeof result.performance.totalTime).toBe('number');
        expect(typeof result.performance.memoryUsage).toBe('number');

        // Check arrays
        expect(Array.isArray(result.warnings)).toBe(true);
        expect(Array.isArray(result.errors)).toBe(true);
      },
      30000
    ); // 30 second timeout for API calls

    skipIfNoApiKey(
      'should handle stealth mode for protected sites',
      async () => {
        const result = await extractContentEnhanced(TEST_URLS.handelsblatt);

        expect(result).toBeTruthy();
        expect(result.url).toBe(TEST_URLS.handelsblatt);

        // Handelsblatt should use stealth mode
        if (result.success) {
          expect(['stealth', 'structured']).toContain(
            result.metadata.extractionMethod
          );
        }
      },
      45000
    ); // Longer timeout for stealth mode

    skipIfNoApiKey(
      'should provide fallback for unknown sites',
      async () => {
        const result = await extractContentEnhanced(TEST_URLS.unknown);

        expect(result).toBeTruthy();
        expect(result.url).toBe(TEST_URLS.unknown);

        // Unknown sites should use standard strategy
        if (result.success) {
          expect(result.metadata.extractionMethod).toBe('standard');
        }
      },
      30000
    );

    test('should handle missing API key gracefully', async () => {
      // Temporarily remove API key
      const originalApiKey = process.env.FIRECRAWL_API_KEY;
      delete process.env.FIRECRAWL_API_KEY;

      const result = await extractContentEnhanced(TEST_URLS.finanzenNet);

      expect(result.success).toBe(false);
      expect(result.errors).toContain('Firecrawl API key not configured');

      // Restore API key
      if (originalApiKey) {
        process.env.FIRECRAWL_API_KEY = originalApiKey;
      }
    });
  });

  describe('Content Analysis', () => {
    test('should detect financial content correctly', () => {
      // This would test the internal detectFinancialContent function
      // For now, we test through the main extraction function
      expect(true).toBe(true); // Placeholder
    });

    test('should detect German content correctly', () => {
      // This would test the internal detectGermanContent function
      // For now, we test through the main extraction function
      expect(true).toBe(true); // Placeholder
    });

    test('should calculate quality scores appropriately', () => {
      // This would test the internal calculateQualityScore function
      // For now, we test through the main extraction function
      expect(true).toBe(true); // Placeholder
    });
  });

  describe('Error Handling', () => {
    test('should handle network errors gracefully', async () => {
      const result = await extractContentEnhanced(
        'https://nonexistent-domain-12345.com'
      );

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.performance.totalTime).toBeGreaterThan(0);
    });

    test('should handle malformed URLs', async () => {
      const result = await extractContentEnhanced('not-a-valid-url');

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('Performance', () => {
    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should complete extraction within reasonable time',
      async () => {
        const startTime = Date.now();
        const result = await extractContentEnhanced(TEST_URLS.finanzenNet);
        const endTime = Date.now();

        const totalTime = endTime - startTime;

        // Should complete within 15 seconds (target is < 10 seconds)
        expect(totalTime).toBeLessThan(15000);

        if (result.success) {
          expect(result.performance.totalTime).toBeLessThan(15000);
        }
      },
      20000
    );

    skipIfNoApiKey(
      'should provide memory usage information',
      async () => {
        const result = await extractContentEnhanced(TEST_URLS.finanzenNet);

        if (result.success) {
          expect(typeof result.performance.memoryUsage).toBe('number');
          // Memory usage should be reasonable (less than 100MB)
          expect(result.performance.memoryUsage).toBeLessThan(
            100 * 1024 * 1024
          );
        }
      },
      30000
    );
  });
});

/**
 * Integration test helper functions
 */
export function createMockExtractionResult(): EnhancedExtractionResult {
  return {
    success: true,
    url: 'https://example.com/test',
    timestamp: new Date(),
    formats: {
      html: '<h1>Test</h1><p>Test content</p>',
      markdown: '# Test\n\nTest content',
    },
    metadata: {
      title: 'Test Article',
      wordCount: 100,
      readingTime: 1,
      isFinancialContent: true,
      hasGermanContent: true,
      contentComplexity: 'medium',
      qualityScore: 85,
      extractionMethod: 'standard',
      processingTime: 1000,
      retryCount: 0,
    },
    performance: {
      totalTime: 1000,
      firecrawlTime: 800,
      processingTime: 200,
      memoryUsage: 1024 * 1024, // 1MB
    },
    warnings: [],
    errors: [],
    fallbackUsed: false,
  };
}

export function createMockSiteConfiguration(): SiteConfiguration {
  return {
    domain: 'test.com',
    name: 'Test Site',
    timeout: 30000,
    waitFor: 2000,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: ['.cookie-accept'],
      waitAfterAction: 1000,
    },
    actions: [
      {
        type: 'click',
        selector: '.cookie-accept',
        delay: 1000,
        description: 'Accept cookies',
      },
    ],
    features: {
      adBlocking: true,
      javascriptEnabled: true,
    },
  };
}
