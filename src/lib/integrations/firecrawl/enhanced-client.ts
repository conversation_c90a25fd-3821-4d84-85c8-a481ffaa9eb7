/**
 * Enhanced Firecrawl Client
 *
 * Advanced Firecrawl client with multi-format extraction, structured data capabilities,
 * and German financial site optimization for dramatically improved content extraction.
 *
 * Features:
 * - Multi-format extraction (HTML, Markdown, JSON, Raw HTML)
 * - Site-specific configurations for German financial sites
 * - Advanced actions system (cookie handling, dynamic content, stealth mode)
 * - Structured data extraction with financial content schema
 * - Intelligent format selection and fallback mechanisms
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 3: Enhanced Firecrawl Client
 */

import FirecrawlApp from '@mendable/firecrawl-js';
import {
  firecrawlAlerting,
  recordFirecrawlApiKeyMissing,
  recordFirecrawlConfigError,
  recordFirecrawlFailure,
  recordFirecrawlSuccess,
} from './alerting';

const apiKey = process.env.FIRECRAWL_API_KEY;

if (!apiKey) {
  console.warn(
    'FIRECRAWL_API_KEY is not set. Enhanced Firecrawl client will not function.'
  );
  recordFirecrawlApiKeyMissing();
}

// Create enhanced client only when API key is available
let enhancedClient: FirecrawlApp | null = null;
if (apiKey) {
  enhancedClient = new FirecrawlApp({ apiKey });
}

/**
 * Enhanced extraction result with multiple formats and metadata
 */
export interface EnhancedExtractionResult {
  success: boolean;
  url: string;
  timestamp: Date;

  // Content in multiple formats
  formats: {
    html?: string;
    markdown?: string;
    rawHtml?: string;
    structured?: FinancialContentData;
  };

  // Enhanced metadata
  metadata: {
    title?: string;
    author?: string;
    publishDate?: string;
    description?: string;
    language?: string;
    wordCount: number;
    readingTime: number;

    // Content analysis
    isFinancialContent: boolean;
    hasGermanContent: boolean;
    contentComplexity: 'low' | 'medium' | 'high';
    qualityScore: number; // 0-100

    // Technical metadata
    extractionMethod: 'standard' | 'structured' | 'stealth';
    processingTime: number;
    retryCount: number;
    siteConfig?: string;
  };

  // Performance and debugging
  performance: {
    totalTime: number;
    firecrawlTime: number;
    processingTime: number;
    memoryUsage: number;
  };

  // Warnings and errors
  warnings: string[];
  errors: string[];

  // Fallback information
  fallbackUsed: boolean;
  originalFormat?: string;
}

/**
 * Structured financial content data schema
 */
export interface FinancialContentData {
  title: string;
  summary?: string;
  content: string;
  author?: string;
  publishDate?: string;

  // Financial-specific data
  financialData?: {
    stocks?: Array<{
      symbol: string;
      name: string;
      price?: number;
      change?: number;
      changePercent?: number;
    }>;
    indices?: Array<{
      name: string;
      value?: number;
      change?: number;
      changePercent?: number;
    }>;
    currencies?: Array<{
      pair: string;
      rate?: number;
      change?: number;
    }>;
    keyFigures?: Array<{
      label: string;
      value: string | number;
      unit?: string;
    }>;
  };

  // Content structure
  sections?: Array<{
    heading: string;
    content: string;
    type: 'introduction' | 'analysis' | 'outlook' | 'data' | 'conclusion';
  }>;

  // SEO and categorization
  tags?: string[];
  categories?: string[];
  sentiment?: 'positive' | 'negative' | 'neutral';
}

/**
 * Site-specific configuration for optimized extraction
 */
export interface SiteConfiguration {
  domain: string;
  name: string;

  // Basic settings
  timeout: number;
  waitFor: number;

  // Advanced settings
  location?: {
    country: string;
    languages: string[];
  };

  // Actions for site-specific handling
  actions?: Array<{
    type: 'click' | 'wait' | 'scroll' | 'executeJavascript';
    selector?: string;
    script?: string;
    delay?: number; // For click actions
    milliseconds?: number; // For wait actions
    description: string;
  }>;

  // Cookie and consent handling
  cookieHandling?: {
    acceptSelectors: string[];
    rejectSelectors?: string[];
    waitAfterAction: number;
  };

  // Content extraction hints
  contentSelectors?: {
    article?: string;
    title?: string;
    author?: string;
    publishDate?: string;
    content?: string;
  };

  // Special features
  features?: {
    stealth?: boolean;
    mobileEmulation?: boolean;
    adBlocking?: boolean;
    javascriptEnabled?: boolean;
  };

  // Enhanced Firecrawl parameters
  firecrawlOptions?: {
    removeBase64Images?: boolean;
    blockAds?: boolean;
    includeTags?: string[];
    excludeTags?: string[];
  };
}

/**
 * Extraction strategy configuration
 */
export interface ExtractionStrategy {
  name: string;
  priority: number;
  formats: ('html' | 'markdown' | 'rawHtml')[];
  useStructured: boolean;
  useStealth: boolean;
  maxRetries: number;
  timeoutMultiplier: number;
}

/**
 * Site configurations for major German financial sites
 */
const SITE_CONFIGURATIONS: Record<string, SiteConfiguration> = {
  'finanzen.net': {
    domain: 'finanzen.net',
    name: 'Finanzen.net',
    timeout: 50000,
    waitFor: 5000,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        'button[data-testid="uc-accept-all-button"]',
        '.sp_choice_type_11',
        'button[id*="accept"]',
        'button[class*="accept"]',
        '[data-consent="accept"]',
        '.cookie-accept',
        '.consent-accept',
        'button[aria-label*="akzeptieren"]',
        'button[title*="Akzeptieren"]',
      ],
      waitAfterAction: 3000,
    },
    actions: [
      {
        type: 'wait',
        milliseconds: 4000,
        description: 'Wait for page and consent dialog to load',
      },
    ],
    features: {
      adBlocking: true,
      mobileEmulation: false,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for better date extraction
    contentSelectors: {
      article: 'article, .article, .content, main .content',
      title: 'h1, .headline, .article-title',
      author: '.author, .byline, [class*="author"]',
      publishDate:
        'time[datetime], .date, .publish-date, .article-date, [class*="datum"], [class*="date"]',
      content: '.article-content, .content, .text, main p',
    },
    firecrawlOptions: {
      removeBase64Images: true,
      blockAds: true,
      includeTags: [],
      excludeTags: [
        '#ov-instrument-chart--full-screen',
        '.advertisement',
        '.ad-banner',
        '.consent-banner',
        '#usercentrics-root',
        '.sp_message_container',
      ],
    },
  },

  'handelsblatt.com': {
    domain: 'handelsblatt.com',
    name: 'Handelsblatt',
    timeout: 60000,
    waitFor: 5000,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        'button[data-testid="uc-accept-all-button"]',
        '.sp_choice_type_11',
        '[title*="Akzeptieren"]',
        'button[data-choice="1,1,1,1"]',
        '#usercentrics-root button[data-testid*="accept"]',
        '.uc-list-button__accept-all',
      ],
      waitAfterAction: 3000,
    },
    actions: [
      {
        type: 'wait',
        milliseconds: 4000,
        description: 'Wait for page load and consent dialog',
      },
    ],
    features: {
      stealth: true,
      adBlocking: true,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for Handelsblatt
    contentSelectors: {
      article: 'article, .vhb-article, .article-content',
      title: 'h1, .vhb-article__headline, .article-headline',
      author: '.vhb-article__author, .author, .byline',
      publishDate:
        'time[datetime], .vhb-article__date, .article-date, .date, [data-testid="article-date"]',
      content: '.vhb-article__body, .article-content, .content-main p',
    },
    firecrawlOptions: {
      removeBase64Images: true,
      blockAds: true,
      // Solution 3: Ultra-aggressive targeting for Handelsblatt
      includeTags: [
        'article',
        'article p',
        'article h1',
        'article h2',
        'article h3',
        '.article-content',
        '.article-content p',
        '.content-main',
        '.content-main p',
        // Add date-specific selectors
        'time[datetime]',
        '.vhb-article__date',
        '.article-date',
        '.date',
      ],
      excludeTags: [
        // Navigation and menus
        'nav',
        '.navigation',
        '.nav',
        'header',
        '.header',
        'footer',
        '.footer',
        '.breadcrumb',
        '.menu',
        '.main-menu',

        // Handelsblatt specific elements
        '.social-share',
        '.share-buttons',
        '.sharing',
        '.article-meta',
        '.author-bio',
        '.author-info',
        '.tags',
        '.tag-list',
        '.related-articles',
        '.related-content',
        '.recommendations',
        '.teaser',
        '.promo',
        '.promotion',

        // Subscription and premium content
        '.subscription',
        '.abo',
        '.premium',
        '.paywall',
        '.paywall-overlay',
        '.subscription-wall',
        '.premium-content-overlay',
        '.premium-barrier',
        '.paywall-prompt',
        '.newsletter',
        '.newsletter-signup',

        // Advertising
        '.advertisement',
        '.ad-container',
        '.ads',
        '.banner',
        '.sponsored',

        // Interactive elements
        '.comments',
        '.comment-section',
        '.feedback',
        '.poll',
        '.survey',

        // Tracking and analytics
        '.tracking',
        '.analytics',
        '.pixel',
        '#usercentrics-root',
        '.sp_message_container',

        // Popups and overlays
        '.popup',
        '.modal',
        '.overlay',
        '.dialog',
        '.cookie-banner',
        '.consent',

        // Sidebar content
        '.sidebar',
        '.aside',
        '.widget',
        '.widgets',

        // Media controls
        '.video-controls',
        '.audio-controls',
        '.media-controls',

        // Print and sharing
        '.print-button',
        '.print-version',
        '.back-to-top',

        // App promotions
        '.app-download',
        '.mobile-app',

        // Search and filters
        '.search',
        '.filter',
        '.sort',

        // Original exclusions
        '#ov-instrument-chart--full-screen',
      ],
    },
  },

  'deraktionaer.de': {
    domain: 'deraktionaer.de',
    name: 'Der Aktionär',
    timeout: 45000,
    waitFor: 4000,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        'button[data-testid="uc-accept-all-button"]',
        '.sp_choice_type_11',
        'button[id*="accept"]',
        'button[class*="accept"]',
        '[data-consent="accept"]',
        '.cookie-accept',
        '.consent-accept',
      ],
      waitAfterAction: 3000,
    },
    actions: [
      {
        type: 'wait',
        milliseconds: 3000,
        description: 'Wait for page and ads to load',
      },
    ],
    features: {
      adBlocking: true,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for Der Aktionär
    contentSelectors: {
      article: 'article, .article, .post-content',
      title: 'h1, .article-title, .post-title',
      author: '.author, .article-author, .post-author',
      publishDate:
        'time[datetime], .article-date, .post-date, .date, [class*="datum"]',
      content: '.article-content, .post-content, .content p',
    },
    firecrawlOptions: {
      removeBase64Images: true,
      blockAds: true,
      includeTags: [
        'article',
        'main .content',
        '.article-content',
        '.post-content',
        'h1',
        'h2',
        'h3',
        'p',
        // Add date-specific selectors
        'time[datetime]',
        '.article-date',
        '.post-date',
        '.date',
      ],
      excludeTags: [
        // Core exclusions
        '#ov-instrument-chart--full-screen',
        '.advertisement',
        '.ad-banner',
        '.ad-container',
        '.consent-banner',
        '#usercentrics-root',
        '.sp_message_container',

        // Navigation and structure
        '.sidebar',
        '.navigation',
        '.nav',
        'header',
        'footer',
        'contentinfo',
        '.breadcrumb',

        // Interactive content
        '.comments',
        '.related-articles',
        '.social-share',
        '.newsletter',
        '.subscription',
        '.paywall',
        '.discussion',
        '.carousel',
        '.slider',
        '.podcast',
        '.spotify',
        '.teaser',
        '.recommendation',

        // Der Aktionär specific exclusions
        '.breaking-news-container',
        '.header-banner',
        '.quickquotes',
        '.ticker',

        // Financial widgets and data
        '.widget',
        '.chart',
        '.quote',
        '.instrument',
        '.performance',
        'table',
        '.table',
        '.data-table',
        '.market-data',
        '.stock-data',
        '.price-data',
        '.trading-data',
        '[class*="chart"]',
        '[id*="chart"]',
        '[class*="ticker"]',
        '[id*="ticker"]',
        '[class*="quote"]',
        '[id*="quote"]',
        '[class*="market"]',
        '[id*="market"]',

        // Premium and subscription content
        '[class*="premium"]',
        '[id*="premium"]',
        '[class*="abo"]',
        '[id*="abo"]',
        '[class*="subscription"]',
        '[id*="subscription"]',
        '[class*="newsletter"]',
        '[id*="newsletter"]',

        // Related content sections
        '[class*="related"]',
        '[id*="related"]',
        '[class*="weitere"]',
        '[id*="weitere"]',
        '[class*="mehr"]',
        '[id*="mehr"]',
        '[class*="auch"]',
        '[id*="auch"]',
        '[class*="empfehlung"]',
        '[id*="empfehlung"]',

        // Author and metadata sections
        '[class*="author"]',
        '[id*="author"]',
        '[class*="autor"]',
        '[id*="autor"]',
        '[class*="meta"]',
        '[id*="meta"]',

        // Social and sharing
        '[class*="social"]',
        '[id*="social"]',
        '[class*="share"]',
        '[id*="share"]',
        '[class*="follow"]',
        '[id*="follow"]',
      ],
    },
  },

  'wallstreet-online.de': {
    domain: 'wallstreet-online.de',
    name: 'Wallstreet Online',
    timeout: 40000,
    waitFor: 3000,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        '.privacy-accept',
        '[data-accept="all"]',
        'button[onclick*="acceptAll"]',
      ],
      waitAfterAction: 2000,
    },
    actions: [
      {
        type: 'wait',
        milliseconds: 2000,
        description: 'Wait for page to load before scrolling',
      },
      {
        type: 'scroll',
        delay: 2000,
        description: 'Scroll to load dynamic content',
      },
    ],
    features: {
      adBlocking: true,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for Wallstreet Online
    contentSelectors: {
      article: 'article, #newsArticle, #newsArticleBlock',
      title: 'h1, .headline, .article-title',
      author: '.author, .byline, [class*="author"]',
      publishDate:
        'time[datetime], .date, .article-date, [class*="datum"], .timestamp',
      content: '.content, .postingText, .article-content p',
    },
    firecrawlOptions: {
      removeBase64Images: true,
      blockAds: true,
      includeTags: [
        'article',
        '#newsArticle',
        '#newsArticleBlock',
        '.content',
        '.postingText',
        'h1',
        'h2',
        'h3',
        'p',
        // Add date-specific selectors
        'time[datetime]',
        '.date',
        '.article-date',
        '.timestamp',
      ],
      excludeTags: [
        '#ov-instrument-chart--full-screen',
        '#quickquotesHead',
        '#mooTickerContainer',
        '#mooTickerContainerMobile',
        '#dsky1',
        '#dsky2',
        '#headOut',
        '#afterhead',
        '#dban1',
        '#breadcrumb',
        '#kosocgen',
        '.advertisement',
        '.ad-banner',
        '.ad-container',
        '.consent-banner',
        '#usercentrics-root',
        '.sp_message_container',
        '.sidebar',
        '.navigation',
        '.nav',
        '.header',
        '.footer',
        '.comments',
        '.related-articles',
        '.social-share',
        '.newsletter',
        '.subscription',
        '.paywall',
        '.discussionCarousel',
        '.carousel',
        '.slider',
        '.podcast',
        '.spotify',
        '.teaser',
        '.recommendation',
        '.reportsbox',
        '.inlinetip',
        '.boxContent',
        '.emittentFooter',
        '.boxFooter',
        '.menuPlaceholder',
        '.textTicker',
        '.tickerContainer',
      ],
    },
  },

  'onvista.de': {
    domain: 'onvista.de',
    name: 'OnVista',
    timeout: 50000,
    waitFor: 4000,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        'button[data-testid="uc-accept-all-button"]',
        '.sp_choice_type_11',
        'button[id*="accept"]',
        'button[class*="accept"]',
        '[data-consent="accept"]',
        '.cookie-accept',
        '.consent-accept',
      ],
      waitAfterAction: 3000,
    },
    actions: [
      {
        type: 'wait',
        milliseconds: 4000,
        description: 'Wait for page and dynamic content to load',
      },
    ],
    features: {
      adBlocking: true,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for OnVista
    contentSelectors: {
      article: 'article, .article, main .content',
      title: 'h1, .article-title, .headline',
      author: '.author, .byline, [class*="author"]',
      publishDate:
        'time[datetime], .date, .article-date, [class*="datum"], .publish-date',
      content: '.article-content, .content, main .content p',
    },
    firecrawlOptions: {
      removeBase64Images: true,
      blockAds: true,
      includeTags: [
        'article',
        'main .content',
        'h1',
        'h2',
        'h3',
        'p',
        'blockquote',
        // Add date-specific selectors
        'time[datetime]',
        '.date',
        '.article-date',
        '.publish-date',
      ],
      excludeTags: [
        // Core exclusions for massive content reduction
        '#ov-instrument-chart--full-screen',
        '.advertisement',
        '.ad-banner',
        '.ad-container',
        '.consent-banner',
        '#usercentrics-root',
        '.sp_message_container',

        // Navigation and structure
        '.sidebar',
        '.navigation',
        '.nav',
        'header',
        'footer',
        'contentinfo',
        '.breadcrumb',

        // Interactive content
        '.comments',
        '.related-articles',
        '.social-share',
        '.newsletter',
        '.subscription',
        '.paywall',
        '.discussion',
        '.carousel',
        '.slider',
        '.podcast',
        '.spotify',
        '.teaser',
        '.recommendation',

        // Financial widgets and charts (AGGRESSIVE)
        '.widget',
        '.chart',
        '.quote',
        '.instrument',
        '.performance',
        'table',
        '.table',
        '.data-table',
        '.market-data',
        '.stock-data',
        '.price-data',
        '.trading-data',
        '.ticker',
        '.tickerContainer',
        '[class*="chart"]',
        '[id*="chart"]',
        '[class*="ticker"]',
        '[id*="ticker"]',
        '[class*="quote"]',
        '[id*="quote"]',
        '[class*="instrument"]',
        '[id*="instrument"]',
        '[class*="market"]',
        '[id*="market"]',
        '[class*="trading"]',
        '[id*="trading"]',
        '[class*="performance"]',
        '[id*="performance"]',

        // OnVista-specific exclusions
        '[class*="premium"]',
        '[id*="premium"]',
        '[class*="werbung"]',
        '[id*="werbung"]',
        '[class*="broker"]',
        '[id*="broker"]',
        '[class*="depot"]',
        '[id*="depot"]',
        '[class*="ratgeber"]',
        '[id*="ratgeber"]',
        '[class*="app"]',
        '[id*="app"]',
        '[class*="social"]',
        '[id*="social"]',

        // Related content sections
        '[class*="related"]',
        '[id*="related"]',
        '[class*="weitere"]',
        '[id*="weitere"]',
        '[class*="mehr"]',
        '[id*="mehr"]',
        '[class*="auch"]',
        '[id*="auch"]',

        // Legal and footer content
        '[class*="legal"]',
        '[id*="legal"]',
        '[class*="impressum"]',
        '[id*="impressum"]',
        '[class*="datenschutz"]',
        '[id*="datenschutz"]',
        '[class*="nutzung"]',
        '[id*="nutzung"]',
        '[class*="agb"]',
        '[id*="agb"]',
      ],
    },
  },

  'boerse.de': {
    domain: 'boerse.de',
    name: 'Börse.de',
    timeout: 50000,
    waitFor: 3500,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        '.gdpr-accept',
        '[data-gdpr="accept"]',
        'button[title*="Alle akzeptieren"]',
      ],
      waitAfterAction: 2000,
    },
    actions: [
      {
        type: 'click',
        selector: '.gdpr-accept',
        delay: 1000,
        description: 'Accept GDPR compliance',
      },
      {
        type: 'wait',
        milliseconds: 3000,
        description: 'Wait for real-time data loading',
      },
    ],
    features: {
      adBlocking: true,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for Börse.de
    contentSelectors: {
      article: 'article, .article, .content',
      title: 'h1, .headline, .article-title',
      author: '.author, .byline, [class*="author"]',
      publishDate: 'time[datetime], .date, .article-date, [class*="datum"]',
      content: '.article-content, .content p',
    },
  },

  'manager-magazin.de': {
    domain: 'manager-magazin.de',
    name: 'Manager Magazin',
    timeout: 45000,
    waitFor: 4000,
    location: {
      country: 'DE',
      languages: ['de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        '.sp_choice_type_11',
        '[data-choice="1,1,1,1"]',
        'button[title*="Akzeptieren und weiter"]',
      ],
      waitAfterAction: 2500,
    },
    actions: [
      {
        type: 'click',
        selector: '.sp_choice_type_11',
        delay: 1500,
        description: 'Accept consent management',
      },
      {
        type: 'wait',
        milliseconds: 3500,
        description: 'Wait for rich media loading',
      },
    ],
    features: {
      adBlocking: true,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for Manager Magazin
    contentSelectors: {
      article: 'article, .article, .content-main',
      title: 'h1, .headline, .article-headline',
      author: '.author, .byline, .article-author',
      publishDate:
        'time[datetime], .date, .article-date, [class*="datum"], .publish-info',
      content: '.article-content, .content-main p',
    },
  },

  'dw.com': {
    domain: 'dw.com',
    name: 'Deutsche Welle',
    timeout: 45000,
    waitFor: 3000,
    location: {
      country: 'DE',
      languages: ['en-US', 'de-DE'],
    },
    cookieHandling: {
      acceptSelectors: [
        'button[data-testid="accept-all"]',
        'button[id*="accept"]',
        'button[class*="accept"]',
        '[data-consent="accept"]',
        '.cookie-accept',
        '.consent-accept',
      ],
      waitAfterAction: 2000,
    },
    actions: [
      {
        type: 'wait',
        milliseconds: 3000,
        description: 'Wait for page and live updates to load',
      },
    ],
    features: {
      adBlocking: true,
      javascriptEnabled: true,
    },
    // Enhanced content selectors for Deutsche Welle
    contentSelectors: {
      article: 'article, .article-content, .main-content',
      title: 'h1, .article-title, .headline',
      author: '.author, .byline, [class*="author"]',
      publishDate:
        'time[datetime], .date, .article-date, [class*="datum"], .timestamp',
      content: '.article-content, .main-content p',
    },
    firecrawlOptions: {
      removeBase64Images: true,
      blockAds: true,
      // Solution 3: Ultra-aggressive targeting for DW live update pages
      includeTags: [
        // Focus only on the main article content, not live updates
        'article h1',
        'article h2:first-of-type',
        'article p:first-of-type',
        'article p:nth-of-type(2)',
        'article p:nth-of-type(3)',
        'article p:nth-of-type(4)',
        'article p:nth-of-type(5)',
        '.article-content',
        '.main-content',
        '.content-main',
        // Add date-specific selectors
        'time[datetime]',
        '.date',
        '.article-date',
        '.timestamp',
      ],
      excludeTags: [
        // Live update specific elements (these are what make the page huge)
        '.live-blog',
        '.liveblog',
        '.live-updates',
        '.live-ticker',
        '.updates',
        '.timeline',
        '[id*="post-liveblog"]',
        '[id*="liveblog-post"]',
        '.live-post',
        '.update-post',
        '.news-update',

        // Navigation and structure
        'nav',
        '.navigation',
        '.nav',
        'header',
        '.header',
        'footer',
        '.footer',
        '.breadcrumb',
        '.menu',

        // DW specific elements
        '.social-share',
        '.share-buttons',
        '.sharing',
        '.article-meta',
        '.author-bio',
        '.author-info',
        '.byline',
        '.tags',
        '.tag-list',
        '.related-articles',
        '.related-content',
        '.recommendations',
        '.teaser',
        '.more-stories',

        // Time stamps and metadata
        'time',
        '.timestamp',
        '.date',
        '.published',
        '.updated',

        // Media and embeds
        '.embedded',
        '.embed',
        '.slideshow',
        '.gallery',
        '.video-embed',
        '.audio-embed',

        // Advertising
        '.advertisement',
        '.ad-container',
        '.ads',
        '.banner',
        '.sponsored',

        // Interactive elements
        '.comments',
        '.comment-section',
        '.feedback',
        '.poll',
        '.survey',

        // Tracking and analytics
        '.tracking',
        '.analytics',
        '.pixel',

        // Popups and overlays
        '.popup',
        '.modal',
        '.overlay',
        '.dialog',
        '.cookie-banner',
        '.consent',

        // Sidebar content
        '.sidebar',
        '.aside',
        '.widget',
        '.widgets',

        // Skip links and accessibility
        '.skip-link',
        '.skip-to',
        '.accessibility',

        // Language and region selectors
        '.language-selector',
        '.region-selector',
        '.locale',

        // Newsletter and subscriptions
        '.newsletter',
        '.subscription',
        '.signup',

        // Print and sharing
        '.print-button',
        '.print-version',
        '.back-to-top',

        // Search and filters
        '.search',
        '.filter',
        '.sort',

        // Original exclusions
        '#ov-instrument-chart--full-screen',
      ],
    },
  },
};

/**
 * Get site configuration for a given URL
 */
export function getSiteConfiguration(url: string): SiteConfiguration | null {
  try {
    const domain = new URL(url).hostname.replace('www.', '');
    return SITE_CONFIGURATIONS[domain] || null;
  } catch {
    console.warn(`Failed to parse URL for site configuration: ${url}`);
    return null;
  }
}

/**
 * Merge feed-specific options with site configuration
 * Feed-specific options take priority over site configuration
 */
function mergeFeedOptionsWithSiteConfig(
  siteConfig: SiteConfiguration | null,
  feedOptions?: any
): SiteConfiguration | null {
  // Start with site config as base, or create default if none exists
  const merged: SiteConfiguration = siteConfig
    ? { ...siteConfig }
    : {
        domain: '',
        name: 'Feed-specific configuration',
        timeout: 30000,
        waitFor: 2000,
        location: { country: 'DE', languages: ['de-DE'] },
      };

  // Initialize firecrawl options if not present
  if (!merged.firecrawlOptions) {
    merged.firecrawlOptions = {};
  }

  // Apply feed-specific options with proper defaults
  if (feedOptions && Object.keys(feedOptions).length > 0) {
    // Use feed-specific removeBase64Images setting, default to true if not specified
    if (feedOptions.removeBase64Images !== undefined) {
      merged.firecrawlOptions.removeBase64Images =
        feedOptions.removeBase64Images;
    } else {
      merged.firecrawlOptions.removeBase64Images = true; // Default enabled
    }

    // Use feed-specific blockAds setting, default to true if not specified
    if (feedOptions.blockAds !== undefined) {
      merged.firecrawlOptions.blockAds = feedOptions.blockAds;
    } else {
      merged.firecrawlOptions.blockAds = true; // Default enabled
    }

    // Merge exclude tags (feed-specific + site-specific)
    if (feedOptions.excludeTags && feedOptions.excludeTags.length > 0) {
      const feedExcludeTags = feedOptions.excludeTags;
      const siteExcludeTags = merged.firecrawlOptions.excludeTags || [];
      merged.firecrawlOptions.excludeTags = [
        ...siteExcludeTags,
        ...feedExcludeTags,
      ];
    }

    // Use feed-specific include tags (override site config if specified)
    if (feedOptions.includeTags && feedOptions.includeTags.length > 0) {
      merged.firecrawlOptions.includeTags = feedOptions.includeTags;
    }

    // Apply processing options
    if (feedOptions.timeout) {
      merged.timeout = feedOptions.timeout;
    }
    if (feedOptions.forceStealth) {
      if (!merged.features) merged.features = {};
      merged.features.stealth = true;
    }
  } else {
    // No feed options provided, use defaults
    merged.firecrawlOptions.removeBase64Images =
      merged.firecrawlOptions.removeBase64Images ?? true;
    merged.firecrawlOptions.blockAds = merged.firecrawlOptions.blockAds ?? true;
  }

  return merged;
}

/**
 * Main enhanced content extraction function
 * Implements intelligent strategy selection and fallback mechanisms
 */
export async function extractContentEnhanced(
  url: string,
  feedSpecificOptions?: any
): Promise<EnhancedExtractionResult> {
  const startTime = Date.now();
  const memoryBefore = process.memoryUsage();

  if (!apiKey || !enhancedClient) {
    return createErrorResult(
      url,
      'Firecrawl API key not configured',
      startTime,
      memoryBefore
    );
  }

  console.log(`🚀 Enhanced extraction starting for: ${url}`);

  // Get site-specific configuration
  const siteConfig = getSiteConfiguration(url);
  if (siteConfig) {
    console.log(`📋 Using site configuration for: ${siteConfig.name}`);
  }

  // Merge feed-specific options with site configuration
  const mergedSiteConfig = mergeFeedOptionsWithSiteConfig(
    siteConfig,
    feedSpecificOptions
  );
  if (feedSpecificOptions && Object.keys(feedSpecificOptions).length > 0) {
    console.log(`🎛️ Applying feed-specific options:`, feedSpecificOptions);
  }

  // Determine extraction strategy
  const strategy = selectExtractionStrategy(url, mergedSiteConfig);
  console.log(`🎯 Selected strategy: ${strategy.name}`);

  // Attempt extraction with selected strategy
  let result = await attemptExtraction(
    url,
    strategy,
    mergedSiteConfig,
    startTime,
    memoryBefore
  );

  // If failed and retries available, try fallback strategies
  if (!result.success && strategy.maxRetries > 0) {
    console.log(`🔄 Primary strategy failed, attempting fallbacks...`);
    result = await attemptFallbackStrategies(
      url,
      mergedSiteConfig,
      startTime,
      memoryBefore
    );
  }

  const totalTime = Date.now() - startTime;
  console.log(`✅ Enhanced extraction completed in ${totalTime}ms`);

  // Record operation result for alerting
  if (result.success) {
    recordFirecrawlSuccess(url, totalTime, {
      strategy: result.metadata?.extractionMethod,
      fallbackUsed: result.fallbackUsed,
      siteConfig: result.metadata?.siteConfig,
    });
  } else {
    recordFirecrawlFailure(
      url,
      totalTime,
      result.errors.length > 0 ? result.errors.join(', ') : 'Unknown error',
      {
        strategy: result.metadata?.extractionMethod,
        fallbackUsed: result.fallbackUsed,
        siteConfig: result.metadata?.siteConfig,
      }
    );
  }

  return result;
}

/**
 * Get Firecrawl usage statistics from the alerting system
 *
 * BUGFIX: Previously returned alert counts instead of actual request counts.
 * Now uses getRequestStats() for accurate Firecrawl API usage tracking.
 */
export function getFirecrawlStats() {
  const requestStats = firecrawlAlerting.getRequestStats();
  const alertSummary = firecrawlAlerting.getAlertSummary();

  return {
    totalRequests: requestStats.totalRequests,
    successfulRequests: requestStats.successfulRequests,
    failedRequests: requestStats.failedRequests,
    successRate: requestStats.successRate,
    rateLimitErrors: alertSummary.byType.rate_limited || 0,
    configErrors: alertSummary.byType.configuration_error || 0,
    timeoutErrors: alertSummary.byType.timeout || 0,
    authErrors: alertSummary.byType.api_key_missing || 0,
  };
}

/**
 * Log Firecrawl usage summary (replacement for legacy client function)
 */
export function logFirecrawlSummary(): void {
  const stats = getFirecrawlStats();
  const summary = firecrawlAlerting.getAlertSummary();

  console.log('\n📊 Enhanced Firecrawl Usage Summary:');
  console.log(`   📈 Recent operations: ${summary.total}`);
  console.log(`   ✅ Success rate: ${(100 - summary.failureRate).toFixed(1)}%`);
  console.log(
    `   ❌ Failed requests: ${summary.byType.extraction_failed || 0}`
  );
  console.log(`   🔄 Consecutive failures: ${summary.consecutiveFailures}`);

  if (summary.total > 0) {
    console.log('   📋 Alert breakdown:');
    if (summary.byType.rate_limited > 0)
      console.log(`      ⏳ Rate limits: ${summary.byType.rate_limited}`);
    if (summary.byType.configuration_error > 0)
      console.log(
        `      ⚙️ Config errors: ${summary.byType.configuration_error}`
      );
    if (summary.byType.timeout > 0)
      console.log(`      ⏰ Timeouts: ${summary.byType.timeout}`);
    if (summary.byType.api_key_missing > 0)
      console.log(`      🔐 Auth errors: ${summary.byType.api_key_missing}`);
    if (summary.byType.performance_degraded > 0)
      console.log(
        `      🐌 Performance issues: ${summary.byType.performance_degraded}`
      );
  }

  console.log(
    `   📊 View detailed alerts: /api/firecrawl-alerts?action=dashboard`
  );
}

/**
 * Predefined extraction strategies
 */
const EXTRACTION_STRATEGIES: Record<string, ExtractionStrategy> = {
  standard: {
    name: 'Standard Multi-Format',
    priority: 1,
    formats: ['html', 'markdown'],
    useStructured: false,
    useStealth: false,
    maxRetries: 2,
    timeoutMultiplier: 1.0,
  },
  structured: {
    name: 'Structured Data Enhanced',
    priority: 2,
    formats: ['html'],
    useStructured: true,
    useStealth: false,
    maxRetries: 1,
    timeoutMultiplier: 1.2,
  },
  stealth: {
    name: 'Stealth Mode',
    priority: 3,
    formats: ['html', 'markdown'],
    useStructured: false,
    useStealth: true,
    maxRetries: 1,
    timeoutMultiplier: 1.5,
  },
  simple: {
    name: 'Simple Extraction',
    priority: 4,
    formats: ['markdown'],
    useStructured: false,
    useStealth: false,
    maxRetries: 1,
    timeoutMultiplier: 0.8,
  },
  comprehensive: {
    name: 'Comprehensive Extraction',
    priority: 4,
    formats: ['html', 'markdown', 'rawHtml'],
    useStructured: true,
    useStealth: false,
    maxRetries: 3,
    timeoutMultiplier: 1.3,
  },
};

/**
 * Select optimal extraction strategy based on URL and site configuration
 */
function selectExtractionStrategy(
  url: string,
  siteConfig: SiteConfiguration | null
): ExtractionStrategy {
  // If site requires stealth mode, use stealth strategy
  if (siteConfig?.features?.stealth) {
    console.log(`🥷 Site requires stealth mode: ${siteConfig.name}`);
    return EXTRACTION_STRATEGIES.stealth;
  }

  // For complex financial sites, use comprehensive strategy
  if (siteConfig && isComplexFinancialSite(siteConfig)) {
    console.log(`📊 Complex financial site detected: ${siteConfig.name}`);
    return EXTRACTION_STRATEGIES.comprehensive;
  }

  // For known German financial sites, use structured strategy
  if (siteConfig) {
    console.log(`🎯 Known German financial site: ${siteConfig.name}`);
    return EXTRACTION_STRATEGIES.structured;
  }

  // Default to standard strategy
  console.log(`📄 Using standard strategy for: ${url}`);
  return EXTRACTION_STRATEGIES.standard;
}

/**
 * Check if site is a complex financial site requiring comprehensive extraction
 */
function isComplexFinancialSite(siteConfig: SiteConfiguration): boolean {
  const complexSites = ['handelsblatt.com', 'manager-magazin.de', 'boerse.de'];
  return complexSites.includes(siteConfig.domain);
}

/**
 * Attempt extraction with given strategy
 */
async function attemptExtraction(
  url: string,
  strategy: ExtractionStrategy,
  siteConfig: SiteConfiguration | null,
  startTime: number,
  memoryBefore: NodeJS.MemoryUsage
): Promise<EnhancedExtractionResult> {
  console.log(`🎯 Attempting ${strategy.name} extraction for: ${url}`);

  try {
    // Standard extraction attempt
    if (!strategy.useStealth) {
      const result = await attemptStandardExtraction(url, strategy, siteConfig);
      if (result.success) {
        return enhanceExtractionResult(
          result,
          url,
          strategy,
          startTime,
          memoryBefore,
          false
        );
      }
    }

    // Structured extraction attempt
    if (strategy.useStructured) {
      console.log(`📊 Attempting structured extraction...`);
      const structuredResult = await attemptStructuredExtraction(
        url,
        siteConfig
      );
      if (structuredResult) {
        // Try to get HTML content as well, but don't fail if it doesn't work
        const htmlResult = await attemptStandardExtraction(
          url,
          strategy,
          siteConfig
        );

        if (htmlResult.success) {
          // Both structured and HTML succeeded
          const enhanced = enhanceExtractionResult(
            htmlResult,
            url,
            strategy,
            startTime,
            memoryBefore,
            false
          );
          enhanced.formats.structured = structuredResult;
          enhanced.metadata.extractionMethod = 'structured';
          return enhanced;
        } else {
          // Structured succeeded but HTML failed - still return success
          console.log(`✅ Using structured data only (HTML extraction failed)`);
          const mockResult = {
            success: true,
            data: {
              html: `<div>Content extracted via structured data</div>`,
              markdown: `# ${structuredResult.title || 'Article'}\n\n${structuredResult.summary || 'Content extracted via structured data'}`,
              rawHtml: '',
            },
          };
          const enhanced = enhanceExtractionResult(
            mockResult,
            url,
            strategy,
            startTime,
            memoryBefore,
            false
          );
          enhanced.formats.structured = structuredResult;
          enhanced.metadata.extractionMethod = 'structured';
          return enhanced;
        }
      }
    }

    // Stealth extraction attempt
    if (strategy.useStealth) {
      console.log(`🥷 Attempting stealth extraction...`);
      const result = await attemptStealthExtraction(url, siteConfig);
      if (result.success) {
        return enhanceExtractionResult(
          result,
          url,
          strategy,
          startTime,
          memoryBefore,
          false
        );
      }
    }

    // Simple extraction attempt (minimal options, markdown only)
    if (strategy.name === 'Simple Extraction') {
      console.log(`🔧 Attempting simple extraction...`);
      const result = await attemptSimpleExtraction(url);
      if (result.success) {
        return enhanceExtractionResult(
          result,
          url,
          strategy,
          startTime,
          memoryBefore,
          false
        );
      }
    }

    // If all attempts failed
    return createErrorResult(
      url,
      `All ${strategy.name} extraction attempts failed`,
      startTime,
      memoryBefore
    );
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Strategy ${strategy.name} failed:`, errorMessage);
    return createErrorResult(
      url,
      `Strategy error: ${errorMessage}`,
      startTime,
      memoryBefore
    );
  }
}

/**
 * Attempt fallback strategies when primary strategy fails
 */
async function attemptFallbackStrategies(
  url: string,
  siteConfig: SiteConfiguration | null,
  startTime: number,
  memoryBefore: NodeJS.MemoryUsage
): Promise<EnhancedExtractionResult> {
  const fallbackOrder = ['standard', 'stealth', 'structured', 'simple'];

  for (const strategyName of fallbackOrder) {
    const strategy = EXTRACTION_STRATEGIES[strategyName];
    console.log(`🔄 Trying fallback strategy: ${strategy.name}`);

    const result = await attemptExtraction(
      url,
      strategy,
      siteConfig,
      startTime,
      memoryBefore
    );
    if (result.success) {
      result.fallbackUsed = true;
      result.warnings.push(
        `Primary strategy failed, used fallback: ${strategy.name}`
      );
      return result;
    }
  }

  return createErrorResult(
    url,
    'All fallback strategies failed',
    startTime,
    memoryBefore
  );
}

/**
 * Create error result structure
 */
function createErrorResult(
  url: string,
  error: string,
  startTime: number,
  memoryBefore: NodeJS.MemoryUsage
): EnhancedExtractionResult {
  const memoryAfter = process.memoryUsage();

  return {
    success: false,
    url,
    timestamp: new Date(),
    formats: {},
    metadata: {
      wordCount: 0,
      readingTime: 0,
      isFinancialContent: false,
      hasGermanContent: false,
      contentComplexity: 'low',
      qualityScore: 0,
      extractionMethod: 'standard',
      processingTime: Date.now() - startTime,
      retryCount: 0,
    },
    performance: {
      totalTime: Date.now() - startTime,
      firecrawlTime: 0,
      processingTime: 0,
      memoryUsage: memoryAfter.heapUsed - memoryBefore.heapUsed,
    },
    warnings: [],
    errors: [error],
    fallbackUsed: false,
  };
}

/**
 * Analyze Firecrawl errors to provide better user messaging
 * @exported for use in RSS processing and other components
 */
export function analyzeFirecrawlError(
  errorMessage: string,
  url: string
): {
  userMessage: string;
  isTemporary: boolean;
  suggestion?: string;
  errorType: string;
} {
  const lowerError = errorMessage.toLowerCase();

  // HTTP Status Code Errors
  if (errorMessage.includes('Status code: 502')) {
    return {
      userMessage: 'Website temporarily unavailable (502 Bad Gateway)',
      isTemporary: true,
      suggestion:
        'The target website or its gateway is having issues. This usually resolves automatically within minutes.',
      errorType: 'http_502',
    };
  }

  if (errorMessage.includes('Status code: 503')) {
    return {
      userMessage: 'Website service unavailable (503)',
      isTemporary: true,
      suggestion:
        'The website is temporarily down for maintenance or overloaded. Try again in a few minutes.',
      errorType: 'http_503',
    };
  }

  if (errorMessage.includes('Status code: 504')) {
    return {
      userMessage: 'Website timeout (504 Gateway Timeout)',
      isTemporary: true,
      suggestion:
        'The website took too long to respond. This is usually a temporary issue.',
      errorType: 'http_504',
    };
  }

  if (errorMessage.includes('Status code: 404')) {
    return {
      userMessage: 'Article not found (404)',
      isTemporary: false,
      suggestion:
        'This article may have been moved or deleted. Check if the URL is still valid.',
      errorType: 'http_404',
    };
  }

  if (errorMessage.includes('Status code: 403')) {
    return {
      userMessage: 'Access blocked by website (403 Forbidden)',
      isTemporary: false,
      suggestion:
        'The website is blocking scraping requests. This may require site-specific configuration.',
      errorType: 'http_403',
    };
  }

  if (errorMessage.includes('Status code: 429')) {
    return {
      userMessage: 'Rate limited by website',
      isTemporary: true,
      suggestion:
        'Too many requests to this website. The system will automatically retry later.',
      errorType: 'rate_limited',
    };
  }

  // Network/Connection Errors
  if (lowerError.includes('timeout') || lowerError.includes('timed out')) {
    return {
      userMessage: 'Request timeout while scraping website',
      isTemporary: true,
      suggestion:
        'The website took too long to respond. This is often temporary.',
      errorType: 'timeout',
    };
  }

  if (lowerError.includes('network') || lowerError.includes('connection')) {
    return {
      userMessage: 'Network connection error',
      isTemporary: true,
      suggestion:
        'Temporary network issue. The system will retry automatically.',
      errorType: 'network_error',
    };
  }

  // Firecrawl API Errors
  if (lowerError.includes('api key') || lowerError.includes('authentication')) {
    return {
      userMessage: 'Firecrawl API authentication error',
      isTemporary: false,
      suggestion: 'Check Firecrawl API key configuration.',
      errorType: 'auth_error',
    };
  }

  if (lowerError.includes('rate limit') || lowerError.includes('quota')) {
    return {
      userMessage: 'Firecrawl API rate limit exceeded',
      isTemporary: true,
      suggestion:
        'Too many API requests. The system will automatically retry later.',
      errorType: 'api_rate_limit',
    };
  }

  // Site-specific issues
  if (lowerError.includes('cloudflare') || lowerError.includes('ddos')) {
    return {
      userMessage: 'Website protected by anti-bot system',
      isTemporary: true,
      suggestion:
        'The website is using protection against automated requests. May resolve with retries.',
      errorType: 'anti_bot',
    };
  }

  // Content/parsing errors
  if (lowerError.includes('parse') || lowerError.includes('invalid')) {
    return {
      userMessage: 'Website content parsing error',
      isTemporary: false,
      suggestion:
        'The website structure may have changed or contains invalid content.',
      errorType: 'parsing_error',
    };
  }

  // Generic/Unknown errors
  return {
    userMessage: `Extraction error: ${errorMessage}`,
    isTemporary: false,
    suggestion: 'Unknown error type. May need manual investigation.',
    errorType: 'unknown',
  };
}

/**
 * Standard extraction with site-specific configuration
 */
async function attemptStandardExtraction(
  url: string,
  strategy: ExtractionStrategy,
  siteConfig: SiteConfiguration | null
): Promise<{ success: boolean; data?: any; error?: string }> {
  if (!enhancedClient) {
    return { success: false, error: 'Enhanced client not available' };
  }

  try {
    const extractionOptions: any = {
      formats: strategy.formats,
      onlyMainContent: true,
      timeout: siteConfig
        ? siteConfig.timeout * strategy.timeoutMultiplier
        : 30000,
      waitFor: siteConfig?.waitFor || 2000,

      // Enhanced Firecrawl parameters (use site config if available, otherwise defaults)
      removeBase64Images:
        siteConfig?.firecrawlOptions?.removeBase64Images ?? true,
      blockAds: siteConfig?.firecrawlOptions?.blockAds ?? true,
      includeTags: siteConfig?.firecrawlOptions?.includeTags ?? [],
      excludeTags: siteConfig?.firecrawlOptions?.excludeTags ?? [
        '#ov-instrument-chart--full-screen',
      ],
    };

    // Add location settings if available
    if (siteConfig?.location) {
      extractionOptions.location = siteConfig.location;
    }

    // Add actions if configured
    if (siteConfig?.actions) {
      extractionOptions.actions = siteConfig.actions.map(action => {
        const mappedAction: any = {
          type: action.type,
        };

        // Only add fields that are defined for this action type
        if (action.selector !== undefined) {
          mappedAction.selector = action.selector;
        }
        if (action.script !== undefined) {
          mappedAction.script = action.script;
        }
        if (action.delay !== undefined) {
          mappedAction.delay = action.delay;
        }
        if (action.milliseconds !== undefined) {
          mappedAction.milliseconds = action.milliseconds;
        }
        if (action.description !== undefined) {
          mappedAction.description = action.description;
        }

        return mappedAction;
      });
    }

    console.log(`📡 Standard extraction with options:`, {
      formats: extractionOptions.formats,
      timeout: extractionOptions.timeout,
      waitFor: extractionOptions.waitFor,
      actionsCount: extractionOptions.actions?.length || 0,
      removeBase64Images: extractionOptions.removeBase64Images,
      blockAds: extractionOptions.blockAds,
      excludeTagsCount: extractionOptions.excludeTags?.length || 0,
    });

    const response = await enhancedClient.scrapeUrl(url, extractionOptions);

    if (response.success) {
      console.log(`✅ Standard extraction successful`);
      return { success: true, data: response };
    } else {
      console.log(`❌ Standard extraction failed`);
      return { success: false, error: 'Standard extraction failed' };
    }
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';

    // Analyze error type for better user messaging
    const errorAnalysis = analyzeFirecrawlError(errorMessage, url);

    // Log with appropriate level based on error type
    if (errorAnalysis.isTemporary) {
      console.warn(`⚠️ ${errorAnalysis.userMessage}`);
      console.warn(`   URL: ${url}`);
      if (errorAnalysis.suggestion) {
        console.warn(`   💡 ${errorAnalysis.suggestion}`);
      }
    } else {
      console.error(`❌ ${errorAnalysis.userMessage}`);
      console.error(`   URL: ${url}`);
      if (errorAnalysis.suggestion) {
        console.error(`   💡 ${errorAnalysis.suggestion}`);
      }
    }

    // Record configuration errors for alerting
    if (
      errorMessage.includes('Bad Request') &&
      errorMessage.includes('actions')
    ) {
      recordFirecrawlConfigError(errorMessage, siteConfig?.name);
    }

    return { success: false, error: errorAnalysis.userMessage };
  }
}

/**
 * Structured data extraction using JSON schema
 */
async function attemptStructuredExtraction(
  url: string,
  siteConfig: SiteConfiguration | null
): Promise<FinancialContentData | null> {
  if (!enhancedClient) {
    return null;
  }

  try {
    const financialSchema = {
      type: 'object',
      properties: {
        title: { type: 'string', description: 'Article title' },
        summary: { type: 'string', description: 'Brief article summary' },
        content: { type: 'string', description: 'Main article content' },
        author: { type: 'string', description: 'Article author' },
        publishDate: { type: 'string', description: 'Publication date' },
        financialData: {
          type: 'object',
          properties: {
            stocks: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  symbol: { type: 'string' },
                  name: { type: 'string' },
                  price: { type: 'number' },
                  change: { type: 'number' },
                  changePercent: { type: 'number' },
                },
              },
            },
            indices: {
              type: 'array',
              items: {
                type: 'object',
                properties: {
                  name: { type: 'string' },
                  value: { type: 'number' },
                  change: { type: 'number' },
                  changePercent: { type: 'number' },
                },
              },
            },
          },
        },
        tags: { type: 'array', items: { type: 'string' } },
        sentiment: {
          type: 'string',
          enum: ['positive', 'negative', 'neutral'],
        },
      },
      required: ['title', 'content'],
    };

    const extractionOptions: any = {
      formats: ['extract'],
      extract: {
        schema: financialSchema,
        systemPrompt: `You are extracting financial content from German financial websites.
				Focus on extracting structured data including stock prices, market indices, and financial metrics.
				Preserve the original German content and identify key financial data points.`,
      },
      timeout: siteConfig ? siteConfig.timeout * 1.2 : 36000,
      waitFor: siteConfig?.waitFor || 3000,

      // Enhanced Firecrawl parameters (use site config if available, otherwise defaults)
      removeBase64Images:
        siteConfig?.firecrawlOptions?.removeBase64Images ?? true,
      blockAds: siteConfig?.firecrawlOptions?.blockAds ?? true,
      includeTags: siteConfig?.firecrawlOptions?.includeTags ?? [],
      excludeTags: siteConfig?.firecrawlOptions?.excludeTags ?? [
        '#ov-instrument-chart--full-screen',
      ],
    };

    console.log(`📊 Attempting structured extraction...`);

    const response = await enhancedClient.scrapeUrl(url, extractionOptions);

    if (response.success && (response as any).extract) {
      console.log(`✅ Structured extraction successful`);
      return (response as any).extract as FinancialContentData;
    } else {
      console.log(`❌ Structured extraction failed or no data`);
      return null;
    }
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Structured extraction error:`, errorMessage);
    return null;
  }
}

/**
 * Stealth extraction for protected sites
 */
async function attemptStealthExtraction(
  url: string,
  siteConfig: SiteConfiguration | null
): Promise<{ success: boolean; data?: any; error?: string }> {
  if (!enhancedClient) {
    return { success: false, error: 'Enhanced client not available' };
  }

  try {
    const stealthOptions: any = {
      formats: ['html', 'markdown'],
      onlyMainContent: true,
      timeout: siteConfig ? siteConfig.timeout * 1.5 : 45000,
      waitFor: siteConfig?.waitFor ? siteConfig.waitFor + 2000 : 4000,

      // Stealth mode settings
      proxy: 'auto', // Use automatic proxy selection
      mobile: false,
      skipTlsVerification: false,

      // Enhanced Firecrawl parameters (use site config if available, otherwise defaults)
      removeBase64Images:
        siteConfig?.firecrawlOptions?.removeBase64Images ?? true,
      blockAds: siteConfig?.firecrawlOptions?.blockAds ?? true,
      includeTags: siteConfig?.firecrawlOptions?.includeTags ?? [],
      excludeTags: siteConfig?.firecrawlOptions?.excludeTags ?? [
        '#ov-instrument-chart--full-screen',
      ],

      // Enhanced actions for stealth mode
      actions: [
        ...(siteConfig?.actions || []),
        {
          type: 'wait',
          milliseconds: 3000,
          description: 'Additional wait for stealth mode',
        },
      ],
    };

    // Add location settings
    if (siteConfig?.location) {
      stealthOptions.location = siteConfig.location;
    }

    console.log(`🥷 Stealth extraction with enhanced options:`, {
      formats: stealthOptions.formats,
      timeout: stealthOptions.timeout,
      waitFor: stealthOptions.waitFor,
      removeBase64Images: stealthOptions.removeBase64Images,
      blockAds: stealthOptions.blockAds,
      excludeTagsCount: stealthOptions.excludeTags?.length || 0,
    });

    const response = await enhancedClient.scrapeUrl(url, stealthOptions);

    if (response.success) {
      console.log(`✅ Stealth extraction successful`);
      return { success: true, data: response };
    } else {
      console.log(`❌ Stealth extraction failed`);
      return { success: false, error: 'Stealth extraction failed' };
    }
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Stealth extraction error:`, errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Simple extraction with minimal options (fallback method)
 */
async function attemptSimpleExtraction(
  url: string
): Promise<{ success: boolean; data?: any; error?: string }> {
  if (!enhancedClient) {
    return { success: false, error: 'Enhanced client not available' };
  }

  try {
    console.log(`🔧 Simple extraction with minimal options for: ${url}`);

    const simpleOptions: any = {
      formats: ['markdown'],
      onlyMainContent: true,
      timeout: 20000,
      waitFor: 1000,
      removeBase64Images: true,
      blockAds: false, // Disable ad blocking to avoid conflicts
      includeTags: [],
      excludeTags: [],
    };

    console.log(`🔧 Simple extraction options:`, {
      formats: simpleOptions.formats,
      timeout: simpleOptions.timeout,
      waitFor: simpleOptions.waitFor,
    });

    const response = await enhancedClient.scrapeUrl(url, simpleOptions);

    if (response.success) {
      console.log(`✅ Simple extraction successful`);
      return { success: true, data: response };
    } else {
      console.log(`❌ Simple extraction failed`);
      return { success: false, error: 'Simple extraction failed' };
    }
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown error';
    console.error(`❌ Simple extraction error:`, errorMessage);
    return { success: false, error: errorMessage };
  }
}

/**
 * Enhance extraction result with metadata and analysis
 */
function enhanceExtractionResult(
  rawResult: { success: boolean; data?: any; error?: string },
  url: string,
  strategy: ExtractionStrategy,
  startTime: number,
  memoryBefore: NodeJS.MemoryUsage,
  fallbackUsed: boolean
): EnhancedExtractionResult {
  const memoryAfter = process.memoryUsage();
  const totalTime = Date.now() - startTime;

  if (!rawResult.success || !rawResult.data) {
    return createErrorResult(
      url,
      rawResult.error || 'Extraction failed',
      startTime,
      memoryBefore
    );
  }

  const data = rawResult.data;

  // Extract content from different formats
  const htmlContent = data.html || '';
  const markdownContent = data.markdown || '';
  const rawHtmlContent = data.rawHtml || '';

  // Analyze content
  const textContent = markdownContent || htmlContent.replace(/<[^>]*>/g, '');
  const wordCount = textContent
    .split(/\s+/)
    .filter((word: string) => word.length > 0).length;
  const readingTime = Math.ceil(wordCount / 200); // 200 words per minute

  // Content analysis
  const isFinancialContent = detectFinancialContent(textContent);
  const hasGermanContent = detectGermanContent(textContent);
  const contentComplexity = analyzeContentComplexity(htmlContent, textContent);
  const qualityScore = calculateQualityScore(
    textContent,
    htmlContent,
    isFinancialContent,
    hasGermanContent
  );

  // Attempt to extract date from structured data if available
  let extractedPublishDate: Date | null = null;
  if (data.structured && data.structured.publishDate) {
    extractedPublishDate = parseGermanDate(data.structured.publishDate);
    if (extractedPublishDate) {
      console.log(
        `📅 Extracted date from structured data: ${extractedPublishDate.toISOString()}`
      );
    }
  }

  // Attempt to extract date from HTML content if structured data failed
  if (!extractedPublishDate && htmlContent) {
    extractedPublishDate = extractDateFromGermanContent(
      htmlContent,
      textContent,
      getSiteConfiguration(url)
    );
    if (extractedPublishDate) {
      console.log(
        `📅 Extracted date from HTML content: ${extractedPublishDate.toISOString()}`
      );
    }
  }

  // Fallback to text-based date extraction if HTML failed
  if (!extractedPublishDate && textContent) {
    extractedPublishDate = parseGermanDateFromText(textContent);
    if (extractedPublishDate) {
      console.log(
        `📅 Extracted date from text content: ${extractedPublishDate.toISOString()}`
      );
    }
  }

  return {
    success: true,
    url,
    timestamp: new Date(),
    formats: {
      html: htmlContent || undefined,
      markdown: markdownContent || undefined,
      rawHtml: rawHtmlContent || undefined,
    },
    metadata: {
      title: data.metadata?.title,
      author: data.metadata?.author,
      publishDate: extractedPublishDate
        ? extractedPublishDate.toISOString()
        : data.metadata?.publishDate,
      description: data.metadata?.description,
      language: hasGermanContent ? 'de' : 'en',
      wordCount,
      readingTime,
      isFinancialContent,
      hasGermanContent,
      contentComplexity,
      qualityScore,
      extractionMethod: strategy.useStealth
        ? 'stealth'
        : strategy.useStructured
          ? 'structured'
          : 'standard',
      processingTime: totalTime,
      retryCount: 0,
      siteConfig: getSiteConfiguration(url)?.name,
    },
    performance: {
      totalTime,
      firecrawlTime: totalTime * 0.8, // Estimate
      processingTime: totalTime * 0.2, // Estimate
      memoryUsage: memoryAfter.heapUsed - memoryBefore.heapUsed,
    },
    warnings: [],
    errors: [],
    fallbackUsed,
  };
}

/**
 * Detect financial content in text
 */
function detectFinancialContent(text: string): boolean {
  // Financial terms detection has been removed from the content processing pipeline
  // This function now returns false to maintain compatibility while removing financial terms tracking
  return false;
}

/**
 * Detect German content in text
 */
function detectGermanContent(text: string): boolean {
  const germanIndicators = [
    'der',
    'die',
    'das',
    'und',
    'oder',
    'aber',
    'mit',
    'von',
    'zu',
    'auf',
    'für',
    'bei',
    'nach',
    'über',
    'unter',
    'ä',
    'ö',
    'ü',
    'ß',
    'auch',
    'noch',
    'schon',
    'nur',
    'mehr',
    'sehr',
    'gut',
    'neue',
    'deutschen',
  ];

  const lowerText = text.toLowerCase();
  const matches = germanIndicators.filter(indicator =>
    lowerText.includes(indicator.toLowerCase())
  ).length;

  return matches >= 3;
}

/**
 * Analyze content complexity
 */
function analyzeContentComplexity(
  html: string,
  text: string
): 'low' | 'medium' | 'high' {
  const htmlElementCount = (html.match(/<[^>]+>/g) || []).length;
  const wordCount = text.split(/\s+/).length;

  if (htmlElementCount > 100 || wordCount > 2000) return 'high';
  if (htmlElementCount > 50 || wordCount > 1000) return 'medium';
  return 'low';
}

/**
 * Calculate content quality score (0-100)
 */
function calculateQualityScore(
  text: string,
  html: string,
  isFinancial: boolean,
  isGerman: boolean
): number {
  let score = 50; // Base score

  const wordCount = text.split(/\s+/).length;
  if (wordCount > 500) score += 20;
  else if (wordCount > 200) score += 10;

  if (isFinancial) score += 15;
  if (isGerman) score += 10;

  // Check for structured content
  if (html.includes('<h1>') || html.includes('<h2>')) score += 10;
  if (html.includes('<table>') || html.includes('<ul>')) score += 5;

  return Math.min(100, Math.max(0, score));
}

/**
 * Extract publication date from German article content when metadata fails
 * Supports common German date formats and publication patterns
 */
function extractDateFromGermanContent(
  html: string,
  text: string,
  siteConfig?: SiteConfiguration | null
): Date | null {
  // Try content selectors first if available
  if (siteConfig?.contentSelectors?.publishDate && html) {
    try {
      // Create a simple DOM parser simulation using regex
      const publishDateSelectors =
        siteConfig.contentSelectors.publishDate.split(', ');

      for (const selector of publishDateSelectors) {
        // Look for time elements with datetime attribute
        if (selector.includes('time[datetime]')) {
          const timeMatch = html.match(/<time[^>]*datetime="([^"]+)"[^>]*>/i);
          if (timeMatch) {
            const dateStr = timeMatch[1];
            const parsedDate = new Date(dateStr);
            if (
              !isNaN(parsedDate.getTime()) &&
              parsedDate.getFullYear() > 2000
            ) {
              console.log(`📅 Extracted date from HTML datetime: ${dateStr}`);
              return parsedDate;
            }
          }
        }

        // Look for class-based date elements
        const classMatch = selector.match(/\.([a-zA-Z0-9_-]+)/);
        if (classMatch) {
          const className = classMatch[1];
          const classRegex = new RegExp(
            `class="[^"]*${className}[^"]*"[^>]*>([^<]+)<`,
            'i'
          );
          const match = html.match(classRegex);
          if (match) {
            const dateStr = match[1].trim();
            const parsedDate = parseGermanDate(dateStr);
            if (parsedDate) {
              console.log(
                `📅 Extracted date from HTML class ${className}: ${dateStr}`
              );
              return parsedDate;
            }
          }
        }
      }
    } catch (error) {
      console.warn('Error parsing date from HTML selectors:', error);
    }
  }

  // Fallback to text-based date extraction
  return parseGermanDateFromText(text);
}

/**
 * Parse German date from text content using regex patterns
 */
function parseGermanDateFromText(text: string): Date | null {
  // German date patterns (ordered by specificity and reliability)
  const patterns = [
    // ISO format with time (highest priority)
    /(\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2})/,
    /(\d{4}-\d{2}-\d{2})/,

    // German format with full month names
    /(\d{1,2})\.\s*(Januar|Februar|März|April|Mai|Juni|Juli|August|September|Oktober|November|Dezember)\s*(\d{4})/i,

    // German format with abbreviated month names
    /(\d{1,2})\.\s*(Jan|Feb|Mär|Apr|Mai|Jun|Jul|Aug|Sep|Okt|Nov|Dez)\.?\s*(\d{4})/i,

    // Standard German date format DD.MM.YYYY
    /(\d{1,2})\.(\d{1,2})\.(\d{4})/,

    // German date with time DD.MM.YYYY HH:MM
    /(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{2})/,

    // Publication date indicators in German
    /(?:veröffentlicht|publiziert|erstellt|aktualisiert).*?(\d{1,2}\.?\s*(?:Januar|Februar|März|April|Mai|Juni|Juli|August|September|Oktober|November|Dezember|Jan|Feb|Mär|Apr|Mai|Jun|Jul|Aug|Sep|Okt|Nov|Dez)\.?\s*\d{4})/i,
    /(?:veröffentlicht|publiziert|erstellt|aktualisiert).*?(\d{1,2}\.\d{1,2}\.\d{4})/i,

    // Common publication patterns
    /(?:vom|am)\s+(\d{1,2}\.?\s*(?:Januar|Februar|März|April|Mai|Juni|Juli|August|September|Oktober|November|Dezember|Jan|Feb|Mär|Apr|Mai|Jun|Jul|Aug|Sep|Okt|Nov|Dez)\.?\s*\d{4})/i,
    /(?:vom|am)\s+(\d{1,2}\.\d{1,2}\.\d{4})/i,
  ];

  // German month name mappings
  const germanMonths: Record<string, number> = {
    januar: 0,
    jan: 0,
    februar: 1,
    feb: 1,
    märz: 2,
    mär: 2,
    april: 3,
    apr: 3,
    mai: 4,
    juni: 5,
    jun: 5,
    juli: 6,
    jul: 6,
    august: 7,
    aug: 7,
    september: 8,
    sep: 8,
    oktober: 9,
    okt: 9,
    november: 10,
    nov: 10,
    dezember: 11,
    dez: 11,
  };

  for (const pattern of patterns) {
    const match = text.match(pattern);
    if (match) {
      try {
        const fullMatch = match[0];
        const parsedDate = parseGermanDate(fullMatch, germanMonths);
        if (parsedDate) {
          console.log(`📅 Extracted date from text pattern: ${fullMatch}`);
          return parsedDate;
        }
      } catch (error) {
        console.warn(`Error parsing date from pattern ${pattern}:`, error);
      }
    }
  }

  return null;
}

/**
 * Parse a German date string into a Date object
 */
function parseGermanDate(
  dateStr: string,
  germanMonths?: Record<string, number>
): Date | null {
  if (!dateStr) return null;

  // Clean the date string
  const cleaned = dateStr.trim().replace(/\s+/g, ' ');

  try {
    // Try ISO format first
    if (/^\d{4}-\d{2}-\d{2}/.test(cleaned)) {
      const parsed = new Date(cleaned);
      if (!isNaN(parsed.getTime()) && parsed.getFullYear() > 2000) {
        return parsed;
      }
    }

    // Try German format with month names
    if (germanMonths) {
      const monthNameMatch = cleaned.match(
        /(\d{1,2})\.?\s*([a-zA-ZäöüÄÖÜ]+)\.?\s*(\d{4})/i
      );
      if (monthNameMatch) {
        const day = parseInt(monthNameMatch[1]);
        const monthName = monthNameMatch[2].toLowerCase();
        const year = parseInt(monthNameMatch[3]);

        if (germanMonths[monthName] !== undefined) {
          const date = new Date(year, germanMonths[monthName], day);
          if (!isNaN(date.getTime()) && date.getFullYear() === year) {
            return date;
          }
        }
      }
    }

    // Try DD.MM.YYYY format
    const ddmmyyyyMatch = cleaned.match(/(\d{1,2})\.(\d{1,2})\.(\d{4})/);
    if (ddmmyyyyMatch) {
      const day = parseInt(ddmmyyyyMatch[1]);
      const month = parseInt(ddmmyyyyMatch[2]) - 1; // JavaScript months are 0-based
      const year = parseInt(ddmmyyyyMatch[3]);

      const date = new Date(year, month, day);
      if (
        !isNaN(date.getTime()) &&
        date.getFullYear() === year &&
        date.getMonth() === month &&
        date.getDate() === day &&
        year > 2000 &&
        year <= new Date().getFullYear()
      ) {
        return date;
      }
    }

    // Try DD.MM.YYYY HH:MM format
    const dateTimeMatch = cleaned.match(
      /(\d{1,2})\.(\d{1,2})\.(\d{4})\s+(\d{1,2}):(\d{2})/
    );
    if (dateTimeMatch) {
      const day = parseInt(dateTimeMatch[1]);
      const month = parseInt(dateTimeMatch[2]) - 1;
      const year = parseInt(dateTimeMatch[3]);
      const hour = parseInt(dateTimeMatch[4]);
      const minute = parseInt(dateTimeMatch[5]);

      const date = new Date(year, month, day, hour, minute);
      if (
        !isNaN(date.getTime()) &&
        date.getFullYear() === year &&
        year > 2000
      ) {
        return date;
      }
    }

    return null;
  } catch (error) {
    console.warn(`Error parsing German date "${dateStr}":`, error);
    return null;
  }
}
