/**
 * Performance Monitoring System
 *
 * Comprehensive performance tracking and metrics collection for the enhanced
 * content processing pipeline. Provides real-time monitoring, alerting, and
 * performance baseline comparison for production reliability.
 *
 * Features:
 * - Processing time tracking with detailed breakdowns
 * - Success/failure rate monitoring with trend analysis
 * - Memory usage tracking and leak detection
 * - Performance baseline comparison and degradation alerts
 * - Configurable alert thresholds and notification triggers
 * - Metrics aggregation and historical data retention
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 6: Monitoring & Production Readiness
 */

/**
 * Performance metrics interface
 */
export interface PerformanceMetrics {
  // Processing metrics
  processingTime: number;
  extractionTime: number;
  conversionTime: number;
  titleProcessingTime: number;

  // Success metrics
  success: boolean;
  errorType?: string;
  fallbacksUsed: number;

  // Resource metrics
  memoryUsage: number;
  memoryDelta: number;
  cpuUsage?: number;

  // Content metrics
  contentQuality: number;
  wordCount: number;
  isFinancialContent: boolean;
  isGermanContent: boolean;

  // Metadata
  url: string;
  timestamp: Date;
  cacheHit: boolean;
  extractionMethod: string;
  conversionMethod: string;
}

/**
 * Aggregated performance statistics
 */
export interface PerformanceStats {
  // Time period
  startTime: Date;
  endTime: Date;
  sampleCount: number;

  // Processing performance
  avgProcessingTime: number;
  medianProcessingTime: number;
  p95ProcessingTime: number;
  p99ProcessingTime: number;

  // Success rates
  successRate: number;
  errorRate: number;
  fallbackRate: number;
  cacheHitRate: number;

  // Resource usage
  avgMemoryUsage: number;
  maxMemoryUsage: number;
  memoryLeakDetected: boolean;

  // Content quality
  avgContentQuality: number;
  financialContentRate: number;
  germanContentRate: number;

  // Performance trends
  performanceTrend: 'improving' | 'stable' | 'degrading';
  trendConfidence: number;
}

/**
 * Alert configuration
 */
export interface AlertConfig {
  // Processing time alerts
  maxProcessingTime: number;
  processingTimeP95Threshold: number;

  // Error rate alerts
  maxErrorRate: number;
  consecutiveErrorThreshold: number;

  // Resource alerts
  maxMemoryUsage: number;
  memoryLeakThreshold: number;

  // Quality alerts
  minContentQuality: number;
  qualityDegradationThreshold: number;

  // Alert settings
  alertCooldown: number;
  enableEmailAlerts: boolean;
  enableSlackAlerts: boolean;
}

/**
 * Performance monitor class
 */
class PerformanceMonitor {
  private metrics: PerformanceMetrics[] = [];
  private alerts: AlertConfig;
  private lastAlertTime: Map<string, Date> = new Map();
  private baselineStats: PerformanceStats | null = null;

  constructor(alertConfig?: Partial<AlertConfig>) {
    this.alerts = {
      maxProcessingTime: 15000, // 15 seconds
      processingTimeP95Threshold: 12000, // 12 seconds
      maxErrorRate: 0.1, // 10%
      consecutiveErrorThreshold: 5,
      maxMemoryUsage: 500 * 1024 * 1024, // 500MB
      memoryLeakThreshold: 100 * 1024 * 1024, // 100MB increase
      minContentQuality: 50,
      qualityDegradationThreshold: 20, // 20 point drop
      alertCooldown: 5 * 60 * 1000, // 5 minutes
      enableEmailAlerts: false,
      enableSlackAlerts: false,
      ...alertConfig,
    };
  }

  /**
   * Record performance metrics for a processing operation
   */
  recordMetrics(metrics: PerformanceMetrics): void {
    // Add timestamp if not provided
    if (!metrics.timestamp) {
      metrics.timestamp = new Date();
    }

    // Store metrics
    this.metrics.push(metrics);

    // Limit stored metrics to last 1000 entries
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Check for alerts
    this.checkAlerts(metrics);

    // Log metrics for debugging
    console.log(
      `📊 Performance recorded: ${metrics.processingTime}ms, success: ${metrics.success}, quality: ${metrics.contentQuality}`
    );
  }

  /**
   * Get current performance statistics
   */
  getStats(timeWindow?: number): PerformanceStats {
    const now = new Date();
    const windowStart = timeWindow
      ? new Date(now.getTime() - timeWindow)
      : new Date(now.getTime() - 24 * 60 * 60 * 1000); // Default 24 hours

    const relevantMetrics = this.metrics.filter(
      m => m.timestamp >= windowStart
    );

    if (relevantMetrics.length === 0) {
      return this.createEmptyStats(windowStart, now);
    }

    // Calculate processing time statistics
    const processingTimes = relevantMetrics
      .map(m => m.processingTime)
      .sort((a, b) => a - b);
    const avgProcessingTime =
      processingTimes.reduce((sum, time) => sum + time, 0) /
      processingTimes.length;
    const medianProcessingTime =
      processingTimes[Math.floor(processingTimes.length / 2)];
    const p95ProcessingTime =
      processingTimes[Math.floor(processingTimes.length * 0.95)];
    const p99ProcessingTime =
      processingTimes[Math.floor(processingTimes.length * 0.99)];

    // Calculate success rates
    const successCount = relevantMetrics.filter(m => m.success).length;
    const successRate = successCount / relevantMetrics.length;
    const errorRate = 1 - successRate;
    const fallbackCount = relevantMetrics.filter(
      m => m.fallbacksUsed > 0
    ).length;
    const fallbackRate = fallbackCount / relevantMetrics.length;
    const cacheHitCount = relevantMetrics.filter(m => m.cacheHit).length;
    const cacheHitRate = cacheHitCount / relevantMetrics.length;

    // Calculate resource usage
    const memoryUsages = relevantMetrics.map(m => m.memoryUsage);
    const avgMemoryUsage =
      memoryUsages.reduce((sum, mem) => sum + mem, 0) / memoryUsages.length;
    const maxMemoryUsage = Math.max(...memoryUsages);
    const memoryLeakDetected = this.detectMemoryLeak(relevantMetrics);

    // Calculate content quality
    const qualityScores = relevantMetrics.map(m => m.contentQuality);
    const avgContentQuality =
      qualityScores.reduce((sum, quality) => sum + quality, 0) /
      qualityScores.length;
    const financialContentCount = relevantMetrics.filter(
      m => m.isFinancialContent
    ).length;
    const financialContentRate = financialContentCount / relevantMetrics.length;
    const germanContentCount = relevantMetrics.filter(
      m => m.isGermanContent
    ).length;
    const germanContentRate = germanContentCount / relevantMetrics.length;

    // Calculate performance trend
    const { trend, confidence } =
      this.calculatePerformanceTrend(relevantMetrics);

    return {
      startTime: windowStart,
      endTime: now,
      sampleCount: relevantMetrics.length,
      avgProcessingTime,
      medianProcessingTime,
      p95ProcessingTime,
      p99ProcessingTime,
      successRate,
      errorRate,
      fallbackRate,
      cacheHitRate,
      avgMemoryUsage,
      maxMemoryUsage,
      memoryLeakDetected,
      avgContentQuality,
      financialContentRate,
      germanContentRate,
      performanceTrend: trend,
      trendConfidence: confidence,
    };
  }

  /**
   * Set performance baseline for comparison
   */
  setBaseline(stats?: PerformanceStats): void {
    this.baselineStats = stats || this.getStats();
    console.log(
      `📈 Performance baseline set: ${this.baselineStats.avgProcessingTime}ms avg, ${this.baselineStats.successRate * 100}% success`
    );
  }

  /**
   * Compare current performance to baseline
   */
  compareToBaseline(): {
    improvement: number;
    degradation: number;
    summary: string;
  } | null {
    if (!this.baselineStats) {
      return null;
    }

    const currentStats = this.getStats();

    // Calculate improvements and degradations
    const processingTimeChange =
      (this.baselineStats.avgProcessingTime - currentStats.avgProcessingTime) /
      this.baselineStats.avgProcessingTime;
    const successRateChange =
      currentStats.successRate - this.baselineStats.successRate;
    const qualityChange =
      currentStats.avgContentQuality - this.baselineStats.avgContentQuality;

    const improvement =
      Math.max(0, processingTimeChange * 100) +
      Math.max(0, successRateChange * 100) +
      Math.max(0, qualityChange);
    const degradation =
      Math.max(0, -processingTimeChange * 100) +
      Math.max(0, -successRateChange * 100) +
      Math.max(0, -qualityChange);

    let summary = '';
    if (improvement > degradation) {
      summary = `Performance improved by ${improvement.toFixed(1)}%`;
    } else if (degradation > improvement) {
      summary = `Performance degraded by ${degradation.toFixed(1)}%`;
    } else {
      summary = 'Performance stable compared to baseline';
    }

    return { improvement, degradation, summary };
  }

  /**
   * Get recent error summary
   */
  getErrorSummary(timeWindow: number = 60 * 60 * 1000): {
    errorCount: number;
    errorTypes: Record<string, number>;
    recentErrors: string[];
  } {
    const now = new Date();
    const windowStart = new Date(now.getTime() - timeWindow);

    const recentMetrics = this.metrics.filter(
      m => m.timestamp >= windowStart && !m.success
    );

    const errorTypes: Record<string, number> = {};
    const recentErrors: string[] = [];

    recentMetrics.forEach(metric => {
      const errorType = metric.errorType || 'unknown';
      errorTypes[errorType] = (errorTypes[errorType] || 0) + 1;
      recentErrors.push(
        `${metric.timestamp.toISOString()}: ${errorType} - ${metric.url}`
      );
    });

    return {
      errorCount: recentMetrics.length,
      errorTypes,
      recentErrors: recentErrors.slice(-10), // Last 10 errors
    };
  }

  /**
   * Clear stored metrics (useful for testing)
   */
  clearMetrics(): void {
    this.metrics = [];
    this.lastAlertTime.clear();
    console.log('🧹 Performance metrics cleared');
  }

  /**
   * Check for alert conditions
   */
  private checkAlerts(metrics: PerformanceMetrics): void {
    const now = new Date();

    // Processing time alert
    if (metrics.processingTime > this.alerts.maxProcessingTime) {
      this.triggerAlert(
        'processing_time',
        `Processing time exceeded threshold: ${metrics.processingTime}ms > ${this.alerts.maxProcessingTime}ms`,
        now
      );
    }

    // Memory usage alert
    if (metrics.memoryUsage > this.alerts.maxMemoryUsage) {
      this.triggerAlert(
        'memory_usage',
        `Memory usage exceeded threshold: ${Math.round(metrics.memoryUsage / 1024 / 1024)}MB > ${Math.round(this.alerts.maxMemoryUsage / 1024 / 1024)}MB`,
        now
      );
    }

    // Content quality alert
    if (metrics.contentQuality < this.alerts.minContentQuality) {
      this.triggerAlert(
        'content_quality',
        `Content quality below threshold: ${metrics.contentQuality} < ${this.alerts.minContentQuality}`,
        now
      );
    }

    // Check for consecutive errors
    const recentMetrics = this.metrics.slice(
      -this.alerts.consecutiveErrorThreshold
    );
    if (
      recentMetrics.length === this.alerts.consecutiveErrorThreshold &&
      recentMetrics.every(m => !m.success)
    ) {
      this.triggerAlert(
        'consecutive_errors',
        `${this.alerts.consecutiveErrorThreshold} consecutive errors detected`,
        now
      );
    }
  }

  /**
   * Trigger an alert with cooldown
   */
  private triggerAlert(
    alertType: string,
    message: string,
    timestamp: Date
  ): void {
    const lastAlert = this.lastAlertTime.get(alertType);
    if (
      lastAlert &&
      timestamp.getTime() - lastAlert.getTime() < this.alerts.alertCooldown
    ) {
      return; // Still in cooldown period
    }

    this.lastAlertTime.set(alertType, timestamp);

    console.warn(`🚨 ALERT [${alertType}]: ${message}`);

    // Here you would integrate with external alerting systems
    // if (this.alerts.enableEmailAlerts) { sendEmailAlert(alertType, message); }
    // if (this.alerts.enableSlackAlerts) { sendSlackAlert(alertType, message); }
  }

  /**
   * Detect memory leaks based on memory usage trends
   */
  private detectMemoryLeak(metrics: PerformanceMetrics[]): boolean {
    if (metrics.length < 10) return false;

    // Check if memory usage is consistently increasing
    const recentMetrics = metrics.slice(-10);
    const memoryTrend = recentMetrics.map((m, i) =>
      i > 0 ? m.memoryUsage - recentMetrics[i - 1].memoryUsage : 0
    );
    const positiveIncreases = memoryTrend.filter(delta => delta > 0).length;

    return positiveIncreases >= 7; // 70% of recent samples show memory increase
  }

  /**
   * Calculate performance trend
   */
  private calculatePerformanceTrend(metrics: PerformanceMetrics[]): {
    trend: 'improving' | 'stable' | 'degrading';
    confidence: number;
  } {
    if (metrics.length < 5) {
      return { trend: 'stable', confidence: 0 };
    }

    // Calculate trend based on processing times
    const times = metrics.map(m => m.processingTime);
    const midpoint = Math.floor(times.length / 2);
    const firstHalf = times.slice(0, midpoint);
    const secondHalf = times.slice(midpoint);

    const firstAvg =
      firstHalf.reduce((sum, time) => sum + time, 0) / firstHalf.length;
    const secondAvg =
      secondHalf.reduce((sum, time) => sum + time, 0) / secondHalf.length;

    const change = (firstAvg - secondAvg) / firstAvg;
    const confidence = Math.min(1, Math.abs(change) * 10); // Higher confidence for larger changes

    if (change > 0.1) return { trend: 'improving', confidence };
    if (change < -0.1) return { trend: 'degrading', confidence };
    return { trend: 'stable', confidence };
  }

  /**
   * Create empty stats structure
   */
  private createEmptyStats(startTime: Date, endTime: Date): PerformanceStats {
    return {
      startTime,
      endTime,
      sampleCount: 0,
      avgProcessingTime: 0,
      medianProcessingTime: 0,
      p95ProcessingTime: 0,
      p99ProcessingTime: 0,
      successRate: 0,
      errorRate: 0,
      fallbackRate: 0,
      cacheHitRate: 0,
      avgMemoryUsage: 0,
      maxMemoryUsage: 0,
      memoryLeakDetected: false,
      avgContentQuality: 0,
      financialContentRate: 0,
      germanContentRate: 0,
      performanceTrend: 'stable',
      trendConfidence: 0,
    };
  }
}

// Global performance monitor instance
const performanceMonitor = new PerformanceMonitor();

/**
 * Record performance metrics (convenience function)
 */
export function recordPerformanceMetrics(metrics: PerformanceMetrics): void {
  performanceMonitor.recordMetrics(metrics);
}

/**
 * Get current performance statistics (convenience function)
 */
export function getPerformanceStats(timeWindow?: number): PerformanceStats {
  return performanceMonitor.getStats(timeWindow);
}

/**
 * Set performance baseline (convenience function)
 */
export function setPerformanceBaseline(stats?: PerformanceStats): void {
  performanceMonitor.setBaseline(stats);
}

/**
 * Compare to baseline (convenience function)
 */
export function compareToBaseline(): {
  improvement: number;
  degradation: number;
  summary: string;
} | null {
  return performanceMonitor.compareToBaseline();
}

/**
 * Get error summary (convenience function)
 */
export function getErrorSummary(timeWindow?: number): {
  errorCount: number;
  errorTypes: Record<string, number>;
  recentErrors: string[];
} {
  return performanceMonitor.getErrorSummary(timeWindow);
}

/**
 * Clear metrics (convenience function)
 */
export function clearPerformanceMetrics(): void {
  performanceMonitor.clearMetrics();
}

export { PerformanceMonitor };
