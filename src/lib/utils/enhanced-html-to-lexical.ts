/**
 * Enhanced HTML-to-Lexical Converter with Upload Node Preservation
 *
 * Solves the critical issue where PayloadCMS UploadFeature nodes are lost
 * during translation by properly reconstructing upload nodes from img tags.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 * @purpose Fix image translation preservation issue
 */

import {
  convertHTMLToLexical,
  editorConfigFactory,
} from '@payloadcms/richtext-lexical';
import { getPayload } from 'payload';
import config from '../../payload.config';

import {
  DecoratorNode,
  NodeKey,
  LexicalNode,
  DOMConversionMap,
  DOMConversionOutput,
  SerializedLexicalNode,
  Spread,
} from 'lexical';

// Server-side only imports
let JSDOM: any = null;
if (typeof window === 'undefined') {
  try {
    const jsdom = require('jsdom');
    JSDOM = jsdom.JSDOM;
  } catch (error) {
    console.warn(
      'JSDOM not available for HTML conversion:',
      error instanceof Error ? error.message : String(error)
    );
  }
}

/**
 * Serialized ImageNode data
 */
export type SerializedImageNode = Spread<
  {
    src: string;
    alt?: string;
    width?: string;
    height?: string;
  },
  SerializedLexicalNode
>;

/**
 * Custom ImageNode that handles <img> tag conversion
 * This allows PayloadCMS's convertHTMLToLexical to properly recognize img tags
 */
export class ImageNode extends DecoratorNode<any> {
  __src: string;
  __alt?: string;
  __width?: string;
  __height?: string;

  static getType(): string {
    return 'image';
  }

  static clone(node: ImageNode): ImageNode {
    return new ImageNode(
      node.__src,
      node.__alt,
      node.__width,
      node.__height,
      node.__key
    );
  }

  constructor(
    src: string,
    alt?: string,
    width?: string,
    height?: string,
    key?: NodeKey
  ) {
    super(key);
    this.__src = src;
    this.__alt = alt;
    this.__width = width;
    this.__height = height;
  }

  // Required DecoratorNode method
  decorate(): any {
    // For server-side, return a simple object representation
    return {
      type: 'img',
      props: {
        src: this.__src,
        alt: this.__alt,
        width: this.__width,
        height: this.__height,
      },
    };
  }

  // Getters for accessing data
  getSrc(): string {
    return this.__src;
  }

  getAlt(): string | undefined {
    return this.__alt;
  }

  getWidth(): string | undefined {
    return this.__width;
  }

  getHeight(): string | undefined {
    return this.__height;
  }

  // Serialization for DecoratorNode
  exportJSON(): SerializedImageNode {
    return {
      ...super.exportJSON(),
      src: this.__src,
      alt: this.__alt,
      width: this.__width,
      height: this.__height,
      type: 'image',
      version: 1,
    };
  }

  static importJSON(serializedNode: SerializedImageNode): ImageNode {
    const { src, alt, width, height } = serializedNode;
    return $createImageNode(src, alt, width, height);
  }

  // DOM conversion methods
  createDOM(): HTMLElement {
    const img = document.createElement('img');
    img.src = this.__src;
    if (this.__alt) img.alt = this.__alt;
    if (this.__width) img.style.width = this.__width;
    if (this.__height) img.style.height = this.__height;
    return img;
  }

  updateDOM(): boolean {
    return false; // Never needs DOM updates
  }

  // DOM conversion - this is the key part that makes <img> tags work
  static importDOM(): DOMConversionMap | null {
    return {
      img: (_node: Node) => ({
        // eslint-disable-line @typescript-eslint/no-unused-vars
        conversion: convertImageElement,
        priority: 0,
      }),
    };
  }
}

/**
 * Convert DOM img element to ImageNode
 */
function convertImageElement(domNode: Node): DOMConversionOutput {
  if (domNode instanceof HTMLImageElement) {
    const { src, alt, width, height } = domNode;
    const node = $createImageNode(
      src,
      alt,
      width ? width.toString() : undefined,
      height ? height.toString() : undefined
    );
    return { node };
  }
  return { node: null };
}

/**
 * Create a new ImageNode
 */
export function $createImageNode(
  src: string,
  alt?: string,
  width?: string,
  height?: string
): ImageNode {
  return new ImageNode(src, alt, width, height);
}

/**
 * Type guard for ImageNode
 */
export function $isImageNode(
  node: LexicalNode | null | undefined
): node is ImageNode {
  return node instanceof ImageNode;
}

/**
 * Enhanced HTML to Lexical conversion that preserves PayloadCMS upload nodes
 */
export async function enhancedHtmlToLexical(html: string): Promise<{
  result: object;
  metrics: {
    startTime: number;
    endTime: number;
    duration: number;
    success: boolean;
    fallbackUsed: boolean;
    method: string;
    preservedUploads: number;
  };
}> {
  const startTime = Date.now();

  if (!html || typeof html !== 'string') {
    return {
      result: {
        root: {
          children: [],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      },
      metrics: {
        startTime,
        endTime: Date.now(),
        duration: 0,
        success: true,
        fallbackUsed: false,
        method: 'empty',
        preservedUploads: 0,
      },
    };
  }

  try {
    // Step 1: Pre-process HTML to extract upload metadata and create image map
    const { processedHtml, imagePositions } =
      await preprocessHtmlForUploads(html);

    // Step 2: Use standard PayloadCMS conversion (without custom ImageNode)
    const resolvedConfig = await config;
    const editorConfig = await editorConfigFactory.default({
      config: resolvedConfig,
    });

    // Convert HTML to Lexical using standard PayloadCMS converter
    console.log(
      '🔄 CONVERSION DEBUG: Starting PayloadCMS HTML→Lexical conversion...'
    );
    console.log(`📄 Input HTML length: ${processedHtml.length} characters`);

    const lexicalResult = convertHTMLToLexical({
      editorConfig,
      html: processedHtml,
      JSDOM,
    });

    console.log('✅ CONVERSION DEBUG: PayloadCMS conversion completed');
    console.log(
      `📊 Lexical result children: ${lexicalResult?.root?.children?.length || 0}`
    );

    // Debug: Check lexical structure
    if (lexicalResult?.root?.children) {
      console.log('🔍 CONVERSION DEBUG: Lexical structure overview:');
      lexicalResult.root.children.forEach((child: any, idx: number) => {
        console.log(
          `  ${idx + 1}. ${child.type} ${child.children ? `(${child.children.length} children)` : ''}`
        );
      });
    }

    // Step 3: Fix links that weren't converted properly by PayloadCMS
    const linkFixedResult = fixLinksInLexical(lexicalResult, processedHtml);

    // Step 4: Manually inject upload nodes at image positions
    const enhancedResult = await injectUploadNodes(
      linkFixedResult,
      imagePositions
    );

    // Validate the result has proper structure
    if (!enhancedResult?.root || !enhancedResult.root.children) {
      console.error('❌ CRITICAL: Enhanced result has invalid root structure!');
      console.error('Result:', enhancedResult);
      throw new Error('Enhanced conversion produced invalid Lexical state');
    }

    if (enhancedResult.root.children.length === 0) {
      console.warn('⚠️ WARNING: Enhanced result has empty children array');
      // Add a default empty paragraph to prevent empty state
      enhancedResult.root.children = [
        {
          type: 'paragraph',
          version: 1,
          children: [],
          direction: 'ltr',
          format: '',
          indent: 0,
          textFormat: 0,
        },
      ];
    }

    // No editor cleanup needed with standard converter

    const endTime = Date.now();

    // Enhanced conversion completed successfully

    return {
      result: enhancedResult,
      metrics: {
        startTime,
        endTime,
        duration: endTime - startTime,
        success: true,
        fallbackUsed: false,
        method: 'enhanced',
        preservedUploads: imagePositions.length,
      },
    };
  } catch (error) {
    console.error('❌ Enhanced HTML→Lexical conversion failed:', error);
    console.error('❌ Error details:', {
      message: error instanceof Error ? error.message : String(error),
      stack:
        error instanceof Error ? error.stack?.substring(0, 500) : undefined,
    });

    // Fallback to standard conversion
    try {
      console.log('🔄 Trying fallback to standard convertHTMLToLexical...');

      const resolvedConfig = await config;
      const editorConfig = await editorConfigFactory.default({
        config: resolvedConfig,
      });

      const fallbackResult = convertHTMLToLexical({
        editorConfig,
        html,
        JSDOM,
      });

      console.log('✅ Fallback conversion successful');
      console.log('🔍 Fallback result structure:', {
        hasRoot: !!fallbackResult?.root,
        childrenCount: fallbackResult?.root?.children?.length || 0,
      });

      return {
        result: fallbackResult,
        metrics: {
          startTime,
          endTime: Date.now(),
          duration: Date.now() - startTime,
          success: true,
          fallbackUsed: true,
          method: 'fallback',
          preservedUploads: 0,
        },
      };
    } catch (fallbackError) {
      console.error('❌ Fallback conversion also failed:', fallbackError);
      console.error('❌ Creating emergency safe Lexical state...');

      // Create a safe, valid Lexical state with content to prevent empty state error
      const emergencyResult = {
        root: {
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  detail: 0,
                  format: 0,
                  mode: 'normal',
                  style: '',
                  text: 'Content conversion failed. Please edit manually.',
                  type: 'text',
                  version: 1,
                },
              ],
              direction: 'ltr',
              format: '',
              indent: 0,
              textFormat: 0,
              textStyle: '',
            },
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      };

      return {
        result: emergencyResult,
        metrics: {
          startTime,
          endTime: Date.now(),
          duration: Date.now() - startTime,
          success: false,
          fallbackUsed: true,
          method: 'emergency',
          preservedUploads: 0,
        },
      };
    }
  }
}

/**
 * Pre-process HTML to extract and preserve upload metadata
 */
async function preprocessHtmlForUploads(html: string): Promise<{
  processedHtml: string;
  uploadMetadata: Array<{
    id: string;
    src: string;
    alt: string;
    width?: string;
    height?: string;
    mediaDoc?: any;
  }>;
  imagePositions: Array<{
    placeholder: string;
    metadata: any;
  }>;
}> {
  const uploadMetadata: Array<{
    id: string;
    src: string;
    alt: string;
    width?: string;
    height?: string;
    mediaDoc?: any;
  }> = [];
  const imagePositions: Array<{
    placeholder: string;
    metadata: any;
  }> = [];

  if (!html.includes('<img')) {
    return { processedHtml: html, uploadMetadata, imagePositions };
  }

  // Extract upload metadata from HTML images

  // Check if JSDOM is available
  if (!JSDOM) {
    console.error('❌ JSDOM not available - cannot process images');
    throw new Error('JSDOM not available for HTML processing');
  }

  // Parse HTML to find img tags
  const dom = new JSDOM(html);
  const document = dom.window.document;
  const imgTags = document.querySelectorAll('img');

  const payload = await getPayload({ config });

  // Process each img tag
  console.log(`🔍 IMAGE DEBUG: Processing ${imgTags.length} img tags...`);

  for (let i = 0; i < imgTags.length; i++) {
    const img = imgTags[i];
    const src = img.getAttribute('src');
    const alt = img.getAttribute('alt') || '';
    const width = img.getAttribute('width');
    const height = img.getAttribute('height');

    console.log(
      `🔍 IMAGE DEBUG: Processing image ${i + 1}/${imgTags.length}: ${src}`
    );

    if (
      src &&
      (src.startsWith('/media/') || src.startsWith('/api/media/file/'))
    ) {
      let mediaDoc = null;

      try {
        if (src.startsWith('/media/') && /^\/media\/\d+$/.test(src)) {
          // Handle media ID URLs like /media/123
          const mediaId = src.split('/').pop();
          if (mediaId && /^\d+$/.test(mediaId)) {
            console.log(`🔍 Looking up media by ID: ${mediaId}`);
            mediaDoc = await payload.findByID({
              collection: 'media',
              id: mediaId,
            });
          }
        } else {
          // Handle filename URLs like /api/media/file/image.png
          const rawFilename = src.split('/').pop()?.split('?')[0];
          let filename = rawFilename
            ? decodeURIComponent(rawFilename)
            : undefined;

          // Remove PayloadCMS size suffixes (e.g., -400x225.png -> .png)
          if (filename) {
            filename = filename.replace(/-\d+x\d+(\.[^.]+)$/, '$1');
          }

          if (filename) {
            console.log(`🔍 Looking up media by filename: ${filename}`);
            const mediaResults = await payload.find({
              collection: 'media',
              where: {
                filename: {
                  equals: filename,
                },
              },
              limit: 1,
            });

            if (mediaResults.docs.length > 0) {
              mediaDoc = mediaResults.docs[0];
            }
          }
        }

        if (mediaDoc) {
          const uniqueId = `upload-${i}-${Date.now()}`;
          const placeholder = `__UPLOAD_PLACEHOLDER_${uniqueId}__`;

          uploadMetadata.push({
            id: uniqueId,
            src,
            alt,
            width,
            height,
            mediaDoc,
          });

          imagePositions.push({
            placeholder,
            metadata: {
              id: uniqueId,
              src,
              alt,
              width,
              height,
              mediaDoc,
            },
          });

          // Replace img tag with placeholder text
          const placeholderP = document.createElement('p');
          placeholderP.textContent = placeholder;
          img.parentNode?.replaceChild(placeholderP, img);

          console.log(
            `✅ IMAGE DEBUG: Replaced image ${i + 1} with placeholder: ${placeholder}`
          );
          console.log(
            `✅ Found media document for ${src}: ${mediaDoc.filename}`
          );
        } else {
          console.warn(`⚠️ No media document found for ${src}`);
        }
      } catch (error) {
        console.warn(`Failed to find media document for ${src}:`, error);
      }
    }
  }

  console.log(`📊 Found ${uploadMetadata.length} upload images to preserve`);

  // DEBUG: List each image found
  uploadMetadata.forEach((img, index) => {
    console.log(
      `  ${index + 1}. ${img.src} -> ${img.mediaDoc?.filename || 'NO_MEDIA_DOC'}`
    );
  });

  const processedHtml = dom.serialize();

  console.log('🔍 IMAGE DEBUG: HTML preprocessing completed');
  console.log(`📄 Processed HTML length: ${processedHtml.length} characters`);
  console.log(
    `📊 Images processed: ${uploadMetadata.length}, Placeholders created: ${imagePositions.length}`
  );

  // Debug: Show a portion of processed HTML around placeholders
  imagePositions.forEach((pos, idx) => {
    const placeholderIndex = processedHtml.indexOf(pos.placeholder);
    if (placeholderIndex !== -1) {
      const start = Math.max(0, placeholderIndex - 50);
      const end = Math.min(
        processedHtml.length,
        placeholderIndex + pos.placeholder.length + 50
      );
      const snippet = processedHtml.substring(start, end);
      console.log(
        `🔍 IMAGE DEBUG: Placeholder ${idx + 1} context: ...${snippet}...`
      );
    }
  });

  return {
    processedHtml,
    uploadMetadata,
    imagePositions,
  };
}

/**
 * Inject upload nodes into lexical structure at placeholder positions
 */
async function injectUploadNodes(
  lexicalData: any,
  imagePositions: Array<{ placeholder: string; metadata: any }>
): Promise<any> {
  if (!lexicalData || !lexicalData.root || !imagePositions.length) {
    return lexicalData;
  }

  console.log('🔧 Injecting upload nodes at placeholder positions...');
  console.log(
    `🔍 Found ${imagePositions.length} image placeholders to replace`
  );

  // Recursively process nodes to find and replace placeholders
  const processNode = (node: any): any => {
    if (!node || typeof node !== 'object') {
      return node;
    }

    // Check if this is a text node that contains a placeholder
    if (node.type === 'text' && node.text) {
      for (const position of imagePositions) {
        if (node.text.includes(position.placeholder)) {
          console.log(
            `🔄 INJECT DEBUG: Found placeholder in text node: ${position.placeholder}`
          );
          console.log(`🔄 INJECT DEBUG: Text node content: "${node.text}"`);

          // Create upload node from metadata
          const uploadNode = {
            type: 'upload',
            version: 3,
            value: position.metadata.mediaDoc.id,
            relationTo: 'media',
            format: '',
            fields: null,
            id: `upload-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
          };

          console.log(
            `✅ INJECT DEBUG: Created upload node for media ID: ${position.metadata.mediaDoc.id}`
          );
          return uploadNode;
        }
      }
    }

    // Process children recursively
    if (node.children && Array.isArray(node.children)) {
      return {
        ...node,
        children: node.children.map(processNode),
      };
    }

    return node;
  };

  const result = {
    ...lexicalData,
    root: {
      ...lexicalData.root,
      children: lexicalData.root.children.map(processNode),
    },
  };

  console.log('✅ Upload node injection completed');

  // DEBUG: Count how many upload nodes were actually created
  const uploadCount = (JSON.stringify(result).match(/"type":"upload"/g) || [])
    .length;
  console.log(
    `🔍 DEBUG: Created ${uploadCount} upload nodes out of ${imagePositions.length} expected`
  );

  if (uploadCount !== imagePositions.length) {
    console.warn(
      `⚠️ MISMATCH: Expected ${imagePositions.length} uploads, created ${uploadCount}`
    );
  }

  return result;
}

/**
 * Fix links that weren't converted properly by PayloadCMS convertHTMLToLexical
 * This ensures German translated content has proper link nodes
 */
function fixLinksInLexical(lexicalResult: any, originalHtml: string): any {
  console.log('🔗 LINK FIX: Starting link repair process...');

  if (!JSDOM) {
    console.error('❌ JSDOM not available - cannot fix links');
    return lexicalResult;
  }

  // Extract links from the original HTML
  const dom = new JSDOM(originalHtml);
  const htmlLinks = Array.from(
    dom.window.document.querySelectorAll('a') as NodeListOf<HTMLAnchorElement>
  ).map((link, index) => ({
    index,
    href: link.getAttribute('href') || '#',
    target: link.getAttribute('target'),
    rel: link.getAttribute('rel'),
    text: link.textContent || '',
  }));

  if (htmlLinks.length === 0) {
    console.log('🔗 No links found in HTML, skipping link fix');
    return lexicalResult;
  }

  console.log(`🔗 Found ${htmlLinks.length} links in HTML to restore`);

  // Deep clone the result to avoid mutations
  const result = JSON.parse(JSON.stringify(lexicalResult));

  // Track which HTML links we've processed
  let htmlLinkIndex = 0;

  // Recursively traverse and fix link nodes
  function traverseAndFixLinks(node: any): any {
    if (!node || typeof node !== 'object') return node;

    // If this is a link node, fix its structure
    if (node.type === 'link') {
      const htmlLink = htmlLinks[htmlLinkIndex];
      if (htmlLink) {
        console.log(`🔧 Fixing link ${htmlLinkIndex + 1}: ${htmlLink.href}`);

        // Ensure proper PayloadCMS link structure
        const fixedLink = {
          ...node,
          type: 'link',
          fields: {
            linkType: 'custom',
            url: htmlLink.href,
            newTab: htmlLink.target === '_blank',
            ...(htmlLink.rel && { rel: htmlLink.rel.split(' ') }),
          },
          // Preserve any existing children
          children: node.children || [],
        };

        htmlLinkIndex++;
        return fixedLink;
      }
    }

    // If this is a text node that should contain a link, we might need to rebuild it
    // This handles cases where PayloadCMS didn't create proper link nodes
    if (node.type === 'text' && htmlLinkIndex < htmlLinks.length) {
      const htmlLink = htmlLinks[htmlLinkIndex];
      if (htmlLink && node.text && node.text.includes(htmlLink.text)) {
        console.log(`🔧 Converting text node to link: ${htmlLink.text}`);

        // Split the text and create a proper link structure
        const parts = node.text.split(htmlLink.text);
        const nodes = [];

        // Add text before the link
        if (parts[0]) {
          nodes.push({
            type: 'text',
            text: parts[0],
            mode: node.mode,
            style: node.style,
            detail: node.detail,
            format: node.format,
            version: node.version,
          });
        }

        // Add the link node
        nodes.push({
          type: 'link',
          fields: {
            linkType: 'custom',
            url: htmlLink.href,
            newTab: htmlLink.target === '_blank',
            ...(htmlLink.rel && { rel: htmlLink.rel.split(' ') }),
          },
          children: [
            {
              type: 'text',
              text: htmlLink.text,
              mode: 'normal',
              style: '',
              detail: 0,
              format: 0,
              version: 1,
            },
          ],
          format: '',
          indent: 0,
          version: 1,
        });

        // Add text after the link
        if (parts[1]) {
          nodes.push({
            type: 'text',
            text: parts[1],
            mode: node.mode,
            style: node.style,
            detail: node.detail,
            format: node.format,
            version: node.version,
          });
        }

        htmlLinkIndex++;
        return nodes; // Return array to be flattened by parent
      }
    }

    // Recursively process children
    if (node.children && Array.isArray(node.children)) {
      const newChildren = [];
      for (const child of node.children) {
        const result = traverseAndFixLinks(child);
        if (Array.isArray(result)) {
          newChildren.push(...result); // Flatten arrays
        } else {
          newChildren.push(result);
        }
      }
      node.children = newChildren;
    }

    return node;
  }

  if (result.root && result.root.children) {
    traverseAndFixLinks(result);
  }

  console.log(
    `🔗 Link fix completed. Processed ${htmlLinkIndex} of ${htmlLinks.length} links`
  );
  return result;
}

/**
 * Convenience function that matches the interface of the original htmlToLexical
 */
export async function htmlToLexicalWithUploadPreservation(
  html: string
): Promise<{
  result: object;
  metrics: {
    startTime: number;
    endTime: number;
    duration: number;
    success: boolean;
    fallbackUsed: boolean;
    method: string;
  };
}> {
  const enhancedResult = await enhancedHtmlToLexical(html);

  return {
    result: enhancedResult.result,
    metrics: {
      startTime: enhancedResult.metrics.startTime,
      endTime: enhancedResult.metrics.endTime,
      duration: enhancedResult.metrics.duration,
      success: enhancedResult.metrics.success,
      fallbackUsed: enhancedResult.metrics.fallbackUsed,
      method: enhancedResult.metrics.method,
    },
  };
}
