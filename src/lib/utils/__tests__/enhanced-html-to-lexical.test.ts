/**
 * Tests for Enhanced HTML-to-Lexical Converter
 *
 * Validates that the enhanced converter properly preserves PayloadCMS upload nodes
 * during translation, solving the critical image preservation issue.
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import {
  enhancedHtmlToLexical,
  htmlToLexicalWithUploadPreservation,
} from '../enhanced-html-to-lexical';

// Mock PayloadCMS dependencies
vi.mock('@payloadcms/richtext-lexical', () => ({
  convertHTMLToLexical: vi.fn(),
  editorConfigFactory: {
    default: vi.fn().mockResolvedValue({}),
  },
}));

vi.mock('payload', () => ({
  getPayload: vi.fn(),
}));

vi.mock('../../../payload.config', () => ({
  default: Promise.resolve({}),
}));

// Mock JSDOM
vi.mock('jsdom', () => ({
  JSDOM: vi.fn().mockImplementation(html => ({
    window: {
      document: {
        querySelectorAll: vi.fn().mockReturnValue([]),
      },
    },
    serialize: vi.fn().mockReturnValue(html),
  })),
}));

describe('Enhanced HTML-to-Lexical Converter', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  describe('enhancedHtmlToLexical', () => {
    it('should handle empty input gracefully', async () => {
      const result = await enhancedHtmlToLexical('');

      expect(result.result).toEqual({
        root: {
          children: [],
          direction: 'ltr',
          format: '',
          indent: 0,
          type: 'root',
          version: 1,
        },
      });
      expect(result.metrics.success).toBe(true);
      expect(result.metrics.method).toBe('empty');
      expect(result.metrics.preservedUploads).toBe(0);
    });

    it('should handle HTML without images', async () => {
      const { convertHTMLToLexical } = await import(
        '@payloadcms/richtext-lexical'
      );
      const mockLexicalResult = {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [{ type: 'text', text: 'Test content' }],
            },
          ],
        },
      };

      (convertHTMLToLexical as any).mockReturnValue(mockLexicalResult);

      const html = '<p>Test content without images</p>';
      const result = await enhancedHtmlToLexical(html);

      expect(result.metrics.success).toBe(true);
      expect(result.metrics.method).toBe('enhanced');
      expect(result.metrics.preservedUploads).toBe(0);
      expect(result.result).toEqual(mockLexicalResult);
    });

    it('should preserve upload metadata for media images', async () => {
      const { convertHTMLToLexical } = await import(
        '@payloadcms/richtext-lexical'
      );
      const { getPayload } = await import('payload');

      // Mock payload.find to return a media document
      const mockMediaDoc = {
        id: 'media-123',
        filename: 'test-image.jpg',
        mimeType: 'image/jpeg',
        width: 800,
        height: 600,
        alt: 'Test image',
        sizes: { thumbnail: { url: '/media/test-image-thumbnail.jpg' } },
      };

      const mockPayload = {
        find: vi.fn().mockResolvedValue({
          docs: [mockMediaDoc],
        }),
      };

      (getPayload as any).mockResolvedValue(mockPayload);

      // Mock JSDOM to return img elements
      const { JSDOM } = await import('jsdom');
      const mockImg = {
        getAttribute: vi.fn().mockImplementation(attr => {
          if (attr === 'src') return '/media/test-image.jpg';
          if (attr === 'alt') return 'Test image';
          if (attr === 'width') return '800';
          if (attr === 'height') return '600';
          return null;
        }),
        setAttribute: vi.fn(),
      };

      (JSDOM as any).mockImplementation(() => ({
        window: {
          document: {
            querySelectorAll: vi.fn().mockReturnValue([mockImg]),
          },
        },
        serialize: vi
          .fn()
          .mockReturnValue(
            '<img src="/media/test-image.jpg" alt="Test image" width="800" height="600">'
          ),
      }));

      // Mock Lexical result with generic image node
      const mockLexicalResult = {
        root: {
          children: [
            {
              type: 'paragraph',
              children: [
                {
                  type: 'image',
                  src: '/media/test-image.jpg',
                  alt: 'Test image',
                },
              ],
            },
          ],
        },
      };

      (convertHTMLToLexical as any).mockReturnValue(mockLexicalResult);

      const html =
        '<p><img src="/media/test-image.jpg" alt="Test image" width="800" height="600"></p>';
      const result = await enhancedHtmlToLexical(html);

      expect(result.metrics.success).toBe(true);
      expect(result.metrics.method).toBe('enhanced');
      expect(result.metrics.preservedUploads).toBe(1);

      // Verify that the image node was converted to an upload node
      const uploadNode = result.result.root.children[0].children[0];
      expect(uploadNode.type).toBe('upload');
      expect(uploadNode.value.id).toBe('media-123');
      expect(uploadNode.value.filename).toBe('test-image.jpg');
      expect(uploadNode.value.relationTo).toBe('media');
    });

    it('should fallback to standard conversion on error', async () => {
      const { convertHTMLToLexical } = await import(
        '@payloadcms/richtext-lexical'
      );

      // Mock first conversion to throw error
      (convertHTMLToLexical as any)
        .mockImplementationOnce(() => {
          throw new Error('Enhanced conversion failed');
        })
        .mockReturnValueOnce({
          root: { children: [{ type: 'paragraph', children: [] }] },
        });

      const html = '<p>Test content</p>';
      const result = await enhancedHtmlToLexical(html);

      expect(result.metrics.success).toBe(true);
      expect(result.metrics.fallbackUsed).toBe(true);
      expect(result.metrics.method).toBe('fallback');
      expect(result.metrics.preservedUploads).toBe(0);
    });
  });

  describe('htmlToLexicalWithUploadPreservation', () => {
    it('should return correct interface format', async () => {
      const { convertHTMLToLexical } = await import(
        '@payloadcms/richtext-lexical'
      );
      const mockLexicalResult = {
        root: {
          children: [{ type: 'paragraph', children: [] }],
        },
      };

      (convertHTMLToLexical as any).mockReturnValue(mockLexicalResult);

      const html = '<p>Test</p>';
      const result = await htmlToLexicalWithUploadPreservation(html);

      expect(result).toHaveProperty('result');
      expect(result).toHaveProperty('metrics');
      expect(result.metrics).toHaveProperty('startTime');
      expect(result.metrics).toHaveProperty('endTime');
      expect(result.metrics).toHaveProperty('duration');
      expect(result.metrics).toHaveProperty('success');
      expect(result.metrics).toHaveProperty('fallbackUsed');
      expect(result.metrics).toHaveProperty('method');
    });
  });
});
