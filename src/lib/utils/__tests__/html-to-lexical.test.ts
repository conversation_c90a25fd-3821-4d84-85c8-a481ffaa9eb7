/**
 * Test Suite for HTML-to-Lexical Converter
 *
 * Comprehensive tests for PayloadCMS HTML→Lexical conversion with real-world samples.
 * Tests conversion quality, performance, and fallback mechanisms.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

import {
  htmlToLexical,
  validateHTMLForConversion,
  type HTMLQualityAnalysis,
  type ConversionMetrics,
} from '../html-to-lexical';

// Mock PayloadCMS dependencies for testing
jest.mock('@payloadcms/richtext-lexical', () => ({
  convertHTMLToLexical: jest.fn(),
  editorConfigFactory: {
    default: jest.fn(),
  },
}));

jest.mock('jsdom', () => ({
  JSDOM: jest.fn().mockImplementation(html => ({
    window: {
      document: {
        body: {
          textContent: html.replace(/<[^>]+>/g, ''),
          children: [],
        },
        querySelectorAll: jest.fn(selector => {
          // Mock basic element counting
          const elementCounts: { [key: string]: number } = {
            '*': Math.floor(html.length / 20), // Rough element count
            'h1, h2, h3, h4, h5, h6': html.includes('<h') ? 2 : 0,
            table: html.includes('<table') ? 1 : 0,
            'ul, ol': html.includes('<ul') || html.includes('<ol') ? 1 : 0,
            a: html.includes('<a') ? 1 : 0,
            img: html.includes('<img') ? 1 : 0,
            'strong, b, em, i, u, mark':
              html.includes('<strong') || html.includes('<b') ? 1 : 0,
          };
          return { length: elementCounts[selector] || 0 };
        }),
      },
    },
  })),
}));

jest.mock('@/payload.config', () => ({
  default: Promise.resolve({
    collections: [],
    globals: [],
  }),
}));

describe('HTML-to-Lexical Converter', () => {
  beforeEach(() => {
    jest.clearAllMocks();

    // Mock successful PayloadCMS conversion by default
    const mockConvertHTMLToLexical =
      require('@payloadcms/richtext-lexical').convertHTMLToLexical;
    mockConvertHTMLToLexical.mockReturnValue({
      root: {
        type: 'root',
        version: 1,
        children: [
          {
            type: 'paragraph',
            version: 1,
            children: [
              {
                type: 'text',
                version: 1,
                text: 'Converted content',
                format: 0,
              },
            ],
          },
        ],
      },
    });

    const mockEditorConfigFactory =
      require('@payloadcms/richtext-lexical').editorConfigFactory;
    mockEditorConfigFactory.default.mockResolvedValue({
      features: [],
      nodes: [],
    });
  });

  describe('htmlToLexical', () => {
    it('should convert simple HTML successfully', async () => {
      const html = '<p>Simple paragraph content</p>';
      const { result, metrics } = await htmlToLexical(html);

      expect(result).toBeDefined();
      expect(result.root).toBeDefined();
      expect(result.root.type).toBe('root');
      expect(metrics.success).toBe(true);
      expect(metrics.fallbackUsed).toBe(false);
      expect(metrics.method).toBe('html');
      expect(metrics.duration).toBeGreaterThan(0);
    });

    it('should handle empty input gracefully', async () => {
      const { result, metrics } = await htmlToLexical('');

      expect(result).toBeDefined();
      expect(result.root).toBeDefined();
      expect(metrics.success).toBe(false);
      expect(metrics.method).toBe('text');
    });

    it('should handle complex HTML with tables and lists', async () => {
      const complexHTML = `
				<h1>Financial Report</h1>
				<table>
					<tr><th>Company</th><th>Price</th></tr>
					<tr><td>DAX Corp</td><td>€150.50</td></tr>
				</table>
				<ul>
					<li>Strong quarterly results</li>
					<li>Increased market share</li>
				</ul>
			`;

      const { result, metrics } = await htmlToLexical(complexHTML);

      expect(result).toBeDefined();
      expect(metrics.success).toBe(true);
      expect(metrics.method).toBe('html');
    });

    it('should fallback to markdown on conversion failure', async () => {
      // Mock PayloadCMS conversion to fail
      const mockConvertHTMLToLexical =
        require('@payloadcms/richtext-lexical').convertHTMLToLexical;
      mockConvertHTMLToLexical.mockImplementation(() => {
        throw new Error('Conversion failed');
      });

      // Mock markdown conversion
      jest.doMock('../lexical', () => ({
        markdownToLexical: jest.fn().mockResolvedValue({
          root: {
            type: 'root',
            version: 1,
            children: [],
          },
        }),
      }));

      const html = '<p>Test content</p>';
      const { result, metrics } = await htmlToLexical(html);

      expect(result).toBeDefined();
      expect(metrics.success).toBe(true);
      expect(metrics.fallbackUsed).toBe(true);
      expect(metrics.method).toBe('markdown');
    });

    it('should measure performance metrics', async () => {
      const html = '<p>Performance test content</p>';
      const { metrics } = await htmlToLexical(html);

      expect(metrics.startTime).toBeGreaterThan(0);
      expect(metrics.endTime).toBeGreaterThan(metrics.startTime);
      expect(metrics.duration).toBe(metrics.endTime - metrics.startTime);
      expect(metrics.memoryBefore).toBeDefined();
      expect(metrics.memoryAfter).toBeDefined();
    });
  });

  describe('validateHTMLForConversion', () => {
    it('should analyze simple HTML correctly', () => {
      const html = '<p>Simple paragraph</p>';
      const analysis = validateHTMLForConversion(html);

      expect(analysis.isComplex).toBe(false);
      expect(analysis.hasStructuredContent).toBe(false);
      expect(analysis.recommendedFormat).toBe('markdown');
      expect(analysis.details.complexity).toBe('low');
    });

    it('should detect complex HTML structures', () => {
      const complexHTML = `
				<h1>Complex Document</h1>
				<table>
					<tr><th>Header</th></tr>
					<tr><td>Data</td></tr>
				</table>
				<ul>
					<li>Item 1</li>
					<li>Item 2</li>
				</ul>
				<p>With <strong>formatting</strong> and <a href="#">links</a></p>
			`;

      const analysis = validateHTMLForConversion(complexHTML);

      expect(analysis.isComplex).toBe(true);
      expect(analysis.hasStructuredContent).toBe(true);
      expect(analysis.hasRichFormatting).toBe(true);
      expect(analysis.details.hasHeadings).toBe(true);
      expect(analysis.details.hasTables).toBe(true);
      expect(analysis.details.hasLists).toBe(true);
      expect(analysis.details.hasLinks).toBe(true);
      expect(analysis.recommendedFormat).toBe('html');
    });

    it('should detect financial content', () => {
      const financialHTML = `
				<h2>DAX Analysis</h2>
				<p>The DAX index rose by 2.5% today, with strong performance from major German companies.</p>
				<p>Quarterly earnings exceeded expectations with revenue of €1.2 billion.</p>
			`;

      const analysis = validateHTMLForConversion(financialHTML);

      expect(analysis.isFinancialContent).toBe(true);
      expect(analysis.details.hasFinancialTerms).toBe(true);
      expect(analysis.score).toBeGreaterThan(50);
    });

    it('should detect German content', () => {
      const germanHTML = `
				<h2>Deutsche Börse Nachrichten</h2>
				<p>Die deutschen Aktien zeigten heute eine sehr gute Performance.</p>
				<p>Der DAX stieg um 2,5% und erreichte neue Höchststände.</p>
			`;

      const analysis = validateHTMLForConversion(germanHTML);

      expect(analysis.details.hasGermanContent).toBe(true);
      expect(analysis.score).toBeGreaterThan(50);
    });

    it('should handle empty input', () => {
      const analysis = validateHTMLForConversion('');

      expect(analysis.isComplex).toBe(false);
      expect(analysis.hasStructuredContent).toBe(false);
      expect(analysis.recommendedFormat).toBe('text');
      expect(analysis.score).toBe(0);
    });

    it('should handle malformed HTML gracefully', () => {
      const malformedHTML =
        '<div><p>Unclosed paragraph<span>Nested content</div>';
      const analysis = validateHTMLForConversion(malformedHTML);

      expect(analysis).toBeDefined();
      expect(analysis.score).toBeGreaterThanOrEqual(0);
      expect(analysis.score).toBeLessThanOrEqual(100);
    });

    it('should calculate quality scores correctly', () => {
      const highQualityHTML = `
				<h1>Premium Financial Analysis</h1>
				<h2>DAX Performance Review</h2>
				<table>
					<tr><th>Metric</th><th>Value</th></tr>
					<tr><td>Revenue</td><td>€2.5 Mrd</td></tr>
				</table>
				<ul>
					<li><strong>Gewinn</strong>: Increased by 15%</li>
					<li><em>Umsatz</em>: Record quarterly results</li>
				</ul>
				<p>Die deutsche Börse zeigt sehr gute Ergebnisse.</p>
			`;

      const analysis = validateHTMLForConversion(highQualityHTML);

      expect(analysis.score).toBeGreaterThan(80);
      expect(analysis.isComplex).toBe(true);
      expect(analysis.hasStructuredContent).toBe(true);
      expect(analysis.hasRichFormatting).toBe(true);
      expect(analysis.isFinancialContent).toBe(true);
      expect(analysis.details.hasGermanContent).toBe(true);
      expect(analysis.recommendedFormat).toBe('html');
    });
  });

  describe('Real-world Content Tests', () => {
    it('should handle typical German financial article', async () => {
      const germanFinancialHTML = `
				<h1>DAX Weekly Outlook: Starke Performance erwartet 🚀</h1>
				<p>Die <strong>deutsche Börse</strong> zeigt sich diese Woche von ihrer besten Seite.</p>
				<h2>Wichtige Kennzahlen</h2>
				<ul>
					<li>DAX: +2,5% auf 15.850 Punkte</li>
					<li>MDAX: +1,8% auf 26.420 Punkte</li>
					<li>TecDAX: +3,2% auf 3.180 Punkte</li>
				</ul>
				<p>Analysten erwarten für das kommende Quartal weitere <em>Wachstumsimpulse</em>.</p>
			`;

      const { result, metrics } = await htmlToLexical(germanFinancialHTML);
      const analysis = validateHTMLForConversion(germanFinancialHTML);

      expect(result).toBeDefined();
      expect(metrics.success).toBe(true);
      expect(analysis.isFinancialContent).toBe(true);
      expect(analysis.details.hasGermanContent).toBe(true);
      expect(analysis.hasStructuredContent).toBe(true);
    });

    it('should handle large content efficiently', async () => {
      // Generate large HTML content
      const largeHTML = `
				<h1>Large Financial Report</h1>
				${Array(100)
          .fill(0)
          .map(
            (_, i) => `
					<h2>Section ${i + 1}</h2>
					<p>This is paragraph ${i + 1} with financial data and German content. 
					Der DAX stieg um ${(Math.random() * 5).toFixed(2)}% auf ${(15000 + Math.random() * 1000).toFixed(0)} Punkte.</p>
				`
          )
          .join('')}
			`;

      const { result, metrics } = await htmlToLexical(largeHTML);

      expect(result).toBeDefined();
      expect(metrics.success).toBe(true);
      expect(metrics.duration).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should handle edge cases gracefully', async () => {
      const edgeCases = [
        '', // Empty
        '   ', // Whitespace only
        '<>', // Invalid HTML
        '<script>alert("test")</script>', // Script tags
        '<div><p>Unclosed tags', // Malformed HTML
        'Plain text without HTML', // No HTML
      ];

      for (const html of edgeCases) {
        const { result, metrics } = await htmlToLexical(html);
        expect(result).toBeDefined();
        expect(result.root).toBeDefined();
        // Should not throw errors
      }
    });
  });

  describe('Performance Benchmarks', () => {
    it('should convert typical articles within performance targets', async () => {
      const typicalHTML = `
				<h1>Market Update</h1>
				<p>Today's market showed strong performance across all sectors.</p>
				<h2>Key Highlights</h2>
				<ul>
					<li>Technology stocks up 3%</li>
					<li>Financial sector gains 2.5%</li>
					<li>Energy stocks mixed</li>
				</ul>
				<p>Analysts remain optimistic about Q4 prospects.</p>
			`;

      const { metrics } = await htmlToLexical(typicalHTML);

      expect(metrics.duration).toBeLessThan(2000); // < 2 seconds
      expect(metrics.success).toBe(true);
    });

    it('should handle memory efficiently', async () => {
      const html = '<p>Memory test content</p>';
      const { metrics } = await htmlToLexical(html);

      const memoryDelta =
        metrics.memoryAfter.heapUsed - metrics.memoryBefore.heapUsed;
      expect(memoryDelta).toBeLessThan(50 * 1024 * 1024); // < 50MB
    });
  });
});
