/**
 * Test Suite for Lexical Validation Service
 *
 * Comprehensive tests for PayloadCMS Lexical field validation and normalization.
 * Ensures proper handling of various data formats and edge cases.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

import {
  validateLexicalData,
  validateLexicalNode,
  normalizeLexicalData,
  normalizeNode,
  createEmptyLexicalState,
  isValidLexicalFormat,
  htmlToBasicLexical,
  type LexicalDocument,
  type LexicalNode,
} from '../lexical-validation';

describe('Lexical Validation Service', () => {
  describe('createEmptyLexicalState', () => {
    it('should create a valid empty Lexical document', () => {
      const result = createEmptyLexicalState();

      expect(result).toHaveProperty('root');
      expect(result.root.type).toBe('root');
      expect(result.root.version).toBe(1);
      expect(result.root.children).toHaveLength(1);
      expect(result.root.children![0].type).toBe('paragraph');
    });

    it('should create consistent structure across calls', () => {
      const result1 = createEmptyLexicalState();
      const result2 = createEmptyLexicalState();

      expect(result1).toEqual(result2);
    });
  });

  describe('isValidLexicalFormat', () => {
    it('should return true for valid Lexical documents', () => {
      const validDoc: LexicalDocument = {
        root: {
          type: 'root',
          version: 1,
          children: [],
        },
      };

      expect(isValidLexicalFormat(validDoc)).toBe(true);
    });

    it('should return false for invalid inputs', () => {
      expect(isValidLexicalFormat(null)).toBe(false);
      expect(isValidLexicalFormat(undefined)).toBe(false);
      expect(isValidLexicalFormat('string')).toBe(false);
      expect(isValidLexicalFormat(123)).toBe(false);
      expect(isValidLexicalFormat({})).toBe(false);
      expect(isValidLexicalFormat({ root: null })).toBe(false);
      expect(isValidLexicalFormat({ root: { type: 'paragraph' } })).toBe(false);
    });
  });

  describe('validateLexicalData', () => {
    it('should validate correct Lexical documents', () => {
      const validDoc: LexicalDocument = {
        root: {
          type: 'root',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          children: [
            {
              type: 'paragraph',
              version: 1,
              direction: 'ltr',
              format: '',
              indent: 0,
              textFormat: 0,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'Hello world',
                  format: 0,
                },
              ],
            },
          ],
        },
      };

      const result = validateLexicalData(validDoc);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing root property', () => {
      const invalidDoc = { notRoot: {} };

      const result = validateLexicalData(invalidDoc);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required "root" property');
      expect(result.normalized).toBeDefined();
    });

    it('should handle null and undefined inputs', () => {
      const nullResult = validateLexicalData(null);
      expect(nullResult.isValid).toBe(false);
      expect(nullResult.normalized).toBeDefined();

      const undefinedResult = validateLexicalData(undefined);
      expect(undefinedResult.isValid).toBe(false);
      expect(undefinedResult.normalized).toBeDefined();
    });

    it('should validate nested node structures', () => {
      const docWithInvalidChild = {
        root: {
          type: 'root',
          version: 1,
          children: [
            {
              // Missing type and version
              text: 'Invalid node',
            },
          ],
        },
      };

      const result = validateLexicalData(docWithInvalidChild);
      expect(result.isValid).toBe(false);
      expect(
        result.errors.some(err =>
          err.includes('Missing required "type" property')
        )
      ).toBe(true);
    });
  });

  describe('validateLexicalNode', () => {
    it('should validate correct nodes', () => {
      const validNode: LexicalNode = {
        type: 'paragraph',
        version: 1,
        children: [],
      };

      const result = validateLexicalNode(validNode);
      expect(result.isValid).toBe(true);
      expect(result.errors).toHaveLength(0);
    });

    it('should detect missing required properties', () => {
      const invalidNode = { text: 'Missing type and version' };

      const result = validateLexicalNode(invalidNode);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Missing required "type" property');
      expect(result.errors).toContain(
        'Missing or invalid "version" property (must be number)'
      );
    });

    it('should validate expected node types', () => {
      const node = { type: 'paragraph', version: 1 };

      const result = validateLexicalNode(node, 'text');
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Expected type "text", got "paragraph"');
    });

    it('should validate children arrays', () => {
      const nodeWithInvalidChildren = {
        type: 'paragraph',
        version: 1,
        children: 'not an array',
      };

      const result = validateLexicalNode(nodeWithInvalidChildren);
      expect(result.isValid).toBe(false);
      expect(result.errors).toContain('Children property must be an array');
    });
  });

  describe('normalizeLexicalData', () => {
    it('should return valid documents unchanged', () => {
      const validDoc = createEmptyLexicalState();
      const result = normalizeLexicalData(validDoc);

      expect(result).toEqual(validDoc);
    });

    it('should normalize invalid inputs to empty state', () => {
      const result1 = normalizeLexicalData(null);
      const result2 = normalizeLexicalData('invalid');
      const result3 = normalizeLexicalData(123);

      const emptyState = createEmptyLexicalState();
      expect(result1).toEqual(emptyState);
      expect(result2).toEqual(emptyState);
      expect(result3).toEqual(emptyState);
    });

    it('should fix missing root children', () => {
      const docWithoutChildren = {
        root: {
          type: 'root',
          version: 1,
        },
      };

      const result = normalizeLexicalData(docWithoutChildren);
      expect(result.root.children).toBeDefined();
      expect(result.root.children).toHaveLength(1);
      expect(result.root.children![0].type).toBe('paragraph');
    });

    it('should normalize malformed nodes', () => {
      const malformedDoc = {
        root: {
          type: 'root',
          // Missing version
          children: [
            {
              // Missing type and version
              text: 'Some text',
            },
          ],
        },
      };

      const result = normalizeLexicalData(malformedDoc);
      expect(result.root.version).toBe(1);
      expect(result.root.children![0].type).toBe('paragraph');
      expect(result.root.children![0].version).toBe(1);
    });
  });

  describe('normalizeNode', () => {
    it('should normalize nodes with missing properties', () => {
      const incompleteNode = { text: 'Some text' };

      const result = normalizeNode(incompleteNode);
      expect(result.type).toBe('paragraph');
      expect(result.version).toBe(1);
    });

    it('should respect expected types', () => {
      const node = {};

      const result = normalizeNode(node, 'root');
      expect(result.type).toBe('root');
      expect(result.direction).toBe('ltr');
    });

    it('should normalize children recursively', () => {
      const nodeWithChildren = {
        type: 'paragraph',
        version: 1,
        children: [
          { text: 'Child without type' },
          { type: 'text', text: 'Valid child' },
        ],
      };

      const result = normalizeNode(nodeWithChildren);
      expect(result.children).toHaveLength(2);
      expect(result.children![0].type).toBe('paragraph'); // Normalized
      expect(result.children![1].type).toBe('text'); // Unchanged
    });
  });

  describe('htmlToBasicLexical', () => {
    it('should convert simple HTML to Lexical', () => {
      const html = '<p>Hello world</p>';
      const result = htmlToBasicLexical(html);

      expect(result.root.children).toHaveLength(1);
      expect(result.root.children![0].type).toBe('paragraph');
      expect(result.root.children![0].children![0].text).toBe('Hello world');
    });

    it('should handle headings', () => {
      const html = '<h1>Title</h1><h2>Subtitle</h2>';
      const result = htmlToBasicLexical(html);

      expect(result.root.children).toHaveLength(2);
      expect(result.root.children![0].type).toBe('heading');
      expect(result.root.children![0].tag).toBe('h1');
      expect(result.root.children![1].type).toBe('heading');
      expect(result.root.children![1].tag).toBe('h2');
    });

    it('should handle empty or invalid HTML', () => {
      const result1 = htmlToBasicLexical('');
      const result2 = htmlToBasicLexical('<invalid>');

      const emptyState = createEmptyLexicalState();
      expect(result1).toEqual(emptyState);
      expect(result2.root.children).toHaveLength(1);
      expect(result2.root.children![0].type).toBe('paragraph');
    });

    it('should extract text from complex HTML', () => {
      const html = '<div><span>Nested</span> <strong>text</strong></div>';
      const result = htmlToBasicLexical(html);

      expect(result.root.children).toHaveLength(1);
      expect(result.root.children![0].children![0].text).toContain('Nested');
      expect(result.root.children![0].children![0].text).toContain('text');
    });
  });

  describe('Edge Cases and Error Handling', () => {
    it('should handle circular references gracefully', () => {
      const circularObj: any = { root: {} };
      circularObj.root.parent = circularObj;

      // Should not throw and should return normalized data
      expect(() => normalizeLexicalData(circularObj)).not.toThrow();
    });

    it('should handle very large documents', () => {
      const largeDoc = {
        root: {
          type: 'root',
          version: 1,
          children: Array(1000)
            .fill(null)
            .map((_, i) => ({
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: `Paragraph ${i}`,
                  format: 0,
                },
              ],
            })),
        },
      };

      const result = validateLexicalData(largeDoc);
      expect(result.isValid).toBe(true);
    });

    it('should handle malformed JSON-like strings', () => {
      const malformedString = '{"root":{"type":"root","children":[}';

      // Should handle gracefully and return empty state
      const result = normalizeLexicalData(malformedString);
      expect(result).toEqual(createEmptyLexicalState());
    });
  });
});
