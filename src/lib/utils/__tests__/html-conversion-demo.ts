/**
 * HTML-to-Lexical Conversion Demonstration
 *
 * Interactive demonstration of the HTML-to-Lexical conversion capabilities.
 * Shows conversion quality, performance metrics, and strategy selection.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

import { htmlToLexical, validateHTMLForConversion } from '../html-to-lexical';
import { ContentProcessorIntegration } from '../../server/content-processor-integration';
import { ConversionPerformanceMonitor } from '../conversion-performance';
import fs from 'fs';
import path from 'path';

/**
 * Demonstration runner for HTML-to-Lexical conversion
 */
export class HTMLConversionDemo {
  private processor: ContentProcessorIntegration;
  private monitor: ConversionPerformanceMonitor;

  constructor() {
    this.processor = ContentProcessorIntegration.getInstance();
    this.monitor = ConversionPerformanceMonitor.getInstance();
  }

  /**
   * Run comprehensive demonstration
   */
  public async runDemo(): Promise<void> {
    console.log('🚀 HTML-to-Lexical Conversion Demonstration\n');
    console.log('='.repeat(60));

    try {
      // Test 1: Simple HTML conversion
      await this.testSimpleConversion();

      // Test 2: Complex financial content
      await this.testComplexFinancialContent();

      // Test 3: German content processing
      await this.testGermanContent();

      // Test 4: Strategy comparison
      await this.testStrategyComparison();

      // Test 5: Performance analysis
      await this.testPerformanceAnalysis();

      // Test 6: Real-world samples
      await this.testRealWorldSamples();

      console.log('\n✅ Demonstration completed successfully!');
      console.log('='.repeat(60));
    } catch (error) {
      console.error('❌ Demonstration failed:', error);
    }
  }

  /**
   * Test simple HTML conversion
   */
  private async testSimpleConversion(): Promise<void> {
    console.log('\n📝 Test 1: Simple HTML Conversion');
    console.log('-'.repeat(40));

    const simpleHTML = `
			<h1>Market Update</h1>
			<p>Today's trading session showed <strong>strong performance</strong> across all sectors.</p>
			<ul>
				<li>Technology stocks up 3%</li>
				<li>Financial sector gains 2.5%</li>
			</ul>
		`;

    const analysis = validateHTMLForConversion(simpleHTML);
    console.log('Content Analysis:', {
      score: analysis.score,
      complexity: analysis.details.complexity,
      hasStructuredContent: analysis.hasStructuredContent,
      recommendedFormat: analysis.recommendedFormat,
    });

    const result = await this.processor.processContent(simpleHTML, 'html');
    console.log('Processing Result:', {
      strategy: result.strategy,
      success: result.metrics.success,
      processingTime: `${result.metrics.processingTime}ms`,
      qualityScore: result.metrics.qualityScore,
    });

    if (result.lexicalContent) {
      console.log('✅ Conversion successful');
    } else {
      console.log('❌ Conversion failed');
    }
  }

  /**
   * Test complex financial content
   */
  private async testComplexFinancialContent(): Promise<void> {
    console.log('\n📊 Test 2: Complex Financial Content');
    console.log('-'.repeat(40));

    const complexHTML = `
			<h1>Q3 2024 Financial Performance Report</h1>
			<table>
				<thead>
					<tr><th>Metric</th><th>Q3 2024</th><th>Q3 2023</th><th>Change</th></tr>
				</thead>
				<tbody>
					<tr><td>Revenue</td><td>€2.45B</td><td>€2.12B</td><td>+15.3%</td></tr>
					<tr><td>EBITDA</td><td>€485M</td><td>€398M</td><td>+21.9%</td></tr>
				</tbody>
			</table>
			<blockquote>
				<p>"Our strategic investments have positioned us as a market leader."</p>
			</blockquote>
		`;

    const analysis = validateHTMLForConversion(complexHTML);
    console.log('Content Analysis:', {
      score: analysis.score,
      isFinancialContent: analysis.isFinancialContent,
      hasStructuredContent: analysis.hasStructuredContent,
      complexity: analysis.details.complexity,
    });

    const result = await this.processor.processContent(complexHTML, 'html');
    console.log('Processing Result:', {
      strategy: result.strategy,
      processingTime: `${result.metrics.processingTime}ms`,
      inputSize: `${result.metrics.inputSize} chars`,
      outputSize: `${result.metrics.outputSize} chars`,
    });
  }

  /**
   * Test German content processing
   */
  private async testGermanContent(): Promise<void> {
    console.log('\n🇩🇪 Test 3: German Content Processing');
    console.log('-'.repeat(40));

    const germanHTML = `
			<h1>DAX Weekly Outlook: Starke Performance erwartet</h1>
			<p>Die <strong>deutsche Börse</strong> zeigt sich diese Woche von ihrer besten Seite.</p>
			<h2>Wichtige Kennzahlen</h2>
			<ul>
				<li>DAX: +2,5% auf 15.850 Punkte</li>
				<li>MDAX: +1,8% auf 26.420 Punkte</li>
			</ul>
			<p>Analysten erwarten weitere <em>Wachstumsimpulse</em>.</p>
		`;

    const analysis = validateHTMLForConversion(germanHTML);
    console.log('Content Analysis:', {
      score: analysis.score,
      hasGermanContent: analysis.details.hasGermanContent,
      isFinancialContent: analysis.isFinancialContent,
      recommendedFormat: analysis.recommendedFormat,
    });

    const result = await this.processor.processContent(germanHTML, 'html');
    console.log('Processing Result:', {
      strategy: result.strategy,
      hasGermanContent: result.metadata.hasGermanContent,
      isFinancialContent: result.metadata.isFinancialContent,
      qualityScore: result.metrics.qualityScore,
    });
  }

  /**
   * Test strategy comparison
   */
  private async testStrategyComparison(): Promise<void> {
    console.log('\n⚖️  Test 4: Strategy Comparison');
    console.log('-'.repeat(40));

    const testContent = `
			<h2>Market Analysis</h2>
			<p>The market showed <strong>strong performance</strong> today.</p>
			<p>Key highlights include technology sector gains.</p>
		`;

    // Test different strategies
    const strategies = [
      'html-direct',
      'html-fallback',
      'markdown',
      'text-only',
    ] as const;

    for (const strategy of strategies) {
      try {
        const result = await this.processor.processContent(
          testContent,
          'html',
          { forceStrategy: strategy }
        );

        console.log(`${strategy}:`, {
          success: result.metrics.success,
          processingTime: `${result.metrics.processingTime}ms`,
          fallbackUsed: result.metrics.fallbackUsed,
        });
      } catch (error) {
        console.log(
          `${strategy}: Failed -`,
          error instanceof Error ? error.message : 'Unknown error'
        );
      }
    }
  }

  /**
   * Test performance analysis
   */
  private async testPerformanceAnalysis(): Promise<void> {
    console.log('\n📈 Test 5: Performance Analysis');
    console.log('-'.repeat(40));

    // Process multiple items to generate statistics
    const testContents = [
      '<p>Simple content</p>',
      '<h1>Title</h1><p>Medium content with <strong>formatting</strong></p>',
      '<table><tr><td>Complex</td><td>Table</td></tr></table>',
      '<h1>Large</h1>' + '<p>Content</p>'.repeat(10),
    ];

    for (const content of testContents) {
      await this.processor.processContent(content, 'html');
    }

    const stats = this.processor.getStats();
    console.log('Conversion Statistics:');
    Object.entries(stats).forEach(([strategy, data]) => {
      if (data.count > 0) {
        console.log(`  ${strategy}:`, {
          count: data.count,
          avgTime: `${Math.round(data.avgTime)}ms`,
          successRate: `${Math.round(data.successRate * 100)}%`,
        });
      }
    });
  }

  /**
   * Test real-world samples from fixtures
   */
  private async testRealWorldSamples(): Promise<void> {
    console.log('\n🌍 Test 6: Real-World Samples');
    console.log('-'.repeat(40));

    const fixturesDir = path.join(__dirname, 'fixtures');

    try {
      const files = fs
        .readdirSync(fixturesDir)
        .filter(f => f.endsWith('.html'));

      for (const file of files) {
        console.log(`\nTesting: ${file}`);

        const filePath = path.join(fixturesDir, file);
        const content = fs.readFileSync(filePath, 'utf-8');

        const analysis = validateHTMLForConversion(content);
        const result = await this.processor.processContent(content, 'html');

        console.log(
          `  Analysis: Score ${analysis.score}, ${analysis.details.complexity} complexity`
        );
        console.log(
          `  Result: ${result.strategy} strategy, ${result.metrics.processingTime}ms`
        );
        console.log(`  Success: ${result.metrics.success ? '✅' : '❌'}`);
      }
    } catch (error) {
      console.log('No fixture files found or error reading fixtures');
    }
  }

  /**
   * Generate performance report
   */
  public generatePerformanceReport(): void {
    console.log('\n📊 Performance Report');
    console.log('='.repeat(60));

    const summary = this.monitor.getPerformanceSummary();

    console.log('Overall Performance:');
    console.log(`  Total Operations: ${summary.current.totalOperations}`);
    console.log(
      `  Success Rate: ${Math.round(summary.current.successRate * 100)}%`
    );
    console.log(
      `  Average Duration: ${Math.round(summary.current.averageDuration)}ms`
    );
    console.log(
      `  Throughput: ${summary.current.throughput.toFixed(2)} ops/sec`
    );

    if (summary.current.bottlenecks.length > 0) {
      console.log('\nBottlenecks:');
      summary.current.bottlenecks.forEach(bottleneck => {
        console.log(`  ⚠️  ${bottleneck}`);
      });
    }

    if (summary.current.recommendations.length > 0) {
      console.log('\nRecommendations:');
      summary.current.recommendations.forEach(rec => {
        console.log(`  💡 ${rec}`);
      });
    }
  }
}

/**
 * Run demonstration if this file is executed directly
 */
if (require.main === module) {
  const demo = new HTMLConversionDemo();
  demo
    .runDemo()
    .then(() => {
      demo.generatePerformanceReport();
      process.exit(0);
    })
    .catch(error => {
      console.error('Demo failed:', error);
      process.exit(1);
    });
}
