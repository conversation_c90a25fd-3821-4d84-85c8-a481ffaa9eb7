# Enhanced HTML-to-Lexical Testing Scenarios

## Test Case 1: Single Inline Image

**Content:**

```
This is a test article with an inline image.

[Upload an image here via the rich text editor]

The image should preserve through translation.
```

**Expected:** Image appears in German translation with translated alt text.

## Test Case 2: Multiple Images

**Content:**

```
Multiple images test:

[Image 1]
First image caption.

[Image 2]
Second image caption.

Both should preserve.
```

**Expected:** Both images appear in German translation.

## Test Case 3: Complex Content with Images

**Content:**

```
# Financial Report

This quarter shows strong performance.

[Chart Image]

## Key Metrics
- Revenue: Up 15%
- Profit: Up 22%

[Graph Image]

Outlook remains positive.
```

**Expected:** All formatting + images preserved, text translated.

## Test Case 4: Image with Complex Alt Text

**Upload image with alt text:** "Q4 2024 Revenue Chart showing 15% growth compared to Q3"
**Expected:** German alt text like "Q4 2024 Umsatz-Diagramm zeigt 15% Wachstum im Vergleich zu Q3"
