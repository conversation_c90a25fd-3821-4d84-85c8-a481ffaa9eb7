/**
 * Conversion Performance Monitoring
 *
 * Comprehensive performance monitoring for HTML-to-Lexical conversion operations.
 * Tracks metrics, identifies bottlenecks, and provides optimization insights.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

/**
 * Performance metric data point
 */
export interface PerformanceMetric {
  timestamp: number;
  operation: string;
  duration: number;
  inputSize: number;
  outputSize: number;
  memoryUsage: {
    before: NodeJS.MemoryUsage;
    after: NodeJS.MemoryUsage;
    delta: number;
  };
  success: boolean;
  strategy: string;
  metadata: Record<string, any>;
}

/**
 * Performance analysis result
 */
export interface PerformanceAnalysis {
  totalOperations: number;
  successRate: number;
  averageDuration: number;
  medianDuration: number;
  p95Duration: number;
  averageMemoryUsage: number;
  throughput: number; // operations per second
  bottlenecks: string[];
  recommendations: string[];
  trends: {
    durationTrend: 'improving' | 'stable' | 'degrading';
    memoryTrend: 'improving' | 'stable' | 'degrading';
    successRateTrend: 'improving' | 'stable' | 'degrading';
  };
}

/**
 * Performance monitoring service
 */
export class ConversionPerformanceMonitor {
  private static instance: ConversionPerformanceMonitor;
  private metrics: PerformanceMetric[] = [];
  private readonly maxMetrics = 1000; // Keep last 1000 metrics
  private readonly analysisWindow = 100; // Analyze last 100 operations

  private constructor() {
    // Initialize with empty metrics
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): ConversionPerformanceMonitor {
    if (!ConversionPerformanceMonitor.instance) {
      ConversionPerformanceMonitor.instance =
        new ConversionPerformanceMonitor();
    }
    return ConversionPerformanceMonitor.instance;
  }

  /**
   * Record a performance metric
   */
  public recordMetric(metric: PerformanceMetric): void {
    this.metrics.push(metric);

    // Keep only the most recent metrics
    if (this.metrics.length > this.maxMetrics) {
      this.metrics = this.metrics.slice(-this.maxMetrics);
    }

    // Log performance warnings
    this.checkPerformanceWarnings(metric);
  }

  /**
   * Create a performance metric from operation data
   */
  public createMetric(
    operation: string,
    startTime: number,
    endTime: number,
    inputSize: number,
    outputSize: number,
    memoryBefore: NodeJS.MemoryUsage,
    memoryAfter: NodeJS.MemoryUsage,
    success: boolean,
    strategy: string,
    metadata: Record<string, any> = {}
  ): PerformanceMetric {
    return {
      timestamp: startTime,
      operation,
      duration: endTime - startTime,
      inputSize,
      outputSize,
      memoryUsage: {
        before: memoryBefore,
        after: memoryAfter,
        delta: memoryAfter.heapUsed - memoryBefore.heapUsed,
      },
      success,
      strategy,
      metadata,
    };
  }

  /**
   * Analyze performance metrics and provide insights
   */
  public analyzePerformance(operation?: string): PerformanceAnalysis {
    let relevantMetrics = this.metrics;

    // Filter by operation if specified
    if (operation) {
      relevantMetrics = this.metrics.filter(m => m.operation === operation);
    }

    // Use recent metrics for analysis
    const recentMetrics = relevantMetrics.slice(-this.analysisWindow);

    if (recentMetrics.length === 0) {
      return this.getEmptyAnalysis();
    }

    // Calculate basic statistics
    const totalOperations = recentMetrics.length;
    const successfulOps = recentMetrics.filter(m => m.success).length;
    const successRate = successfulOps / totalOperations;

    const durations = recentMetrics.map(m => m.duration);
    const memoryDeltas = recentMetrics.map(m => m.memoryUsage.delta);

    const averageDuration = this.calculateAverage(durations);
    const medianDuration = this.calculateMedian(durations);
    const p95Duration = this.calculatePercentile(durations, 95);
    const averageMemoryUsage = this.calculateAverage(memoryDeltas);

    // Calculate throughput (operations per second)
    const timeSpan =
      recentMetrics[recentMetrics.length - 1].timestamp -
      recentMetrics[0].timestamp;
    const throughput = timeSpan > 0 ? (totalOperations * 1000) / timeSpan : 0;

    // Identify bottlenecks
    const bottlenecks = this.identifyBottlenecks(recentMetrics);

    // Generate recommendations
    const recommendations = this.generateRecommendations(recentMetrics, {
      averageDuration,
      averageMemoryUsage,
      successRate,
    });

    // Analyze trends
    const trends = this.analyzeTrends(recentMetrics);

    return {
      totalOperations,
      successRate,
      averageDuration,
      medianDuration,
      p95Duration,
      averageMemoryUsage,
      throughput,
      bottlenecks,
      recommendations,
      trends,
    };
  }

  /**
   * Get performance summary for monitoring dashboard
   */
  public getPerformanceSummary(): {
    current: PerformanceAnalysis;
    byOperation: Record<string, PerformanceAnalysis>;
    byStrategy: Record<string, PerformanceAnalysis>;
  } {
    const current = this.analyzePerformance();

    // Group by operation
    const operations = [...new Set(this.metrics.map(m => m.operation))];
    const byOperation: Record<string, PerformanceAnalysis> = {};
    operations.forEach(op => {
      byOperation[op] = this.analyzePerformance(op);
    });

    // Group by strategy
    const strategies = [...new Set(this.metrics.map(m => m.strategy))];
    const byStrategy: Record<string, PerformanceAnalysis> = {};
    strategies.forEach(strategy => {
      const strategyMetrics = this.metrics.filter(m => m.strategy === strategy);
      if (strategyMetrics.length > 0) {
        byStrategy[strategy] = this.analyzeMetrics(
          strategyMetrics.slice(-this.analysisWindow)
        );
      }
    });

    return {
      current,
      byOperation,
      byStrategy,
    };
  }

  /**
   * Check for performance warnings and log them
   */
  private checkPerformanceWarnings(metric: PerformanceMetric): void {
    const warnings: string[] = [];

    // Duration warnings
    if (metric.duration > 5000) {
      warnings.push(
        `Slow operation: ${metric.operation} took ${metric.duration}ms`
      );
    }

    // Memory warnings
    if (metric.memoryUsage.delta > 50 * 1024 * 1024) {
      // 50MB
      warnings.push(
        `High memory usage: ${metric.operation} used ${Math.round(metric.memoryUsage.delta / 1024 / 1024)}MB`
      );
    }

    // Failure warnings
    if (!metric.success) {
      warnings.push(
        `Operation failed: ${metric.operation} with strategy ${metric.strategy}`
      );
    }

    // Log warnings
    warnings.forEach(warning => {
      console.warn(`⚠️ Performance Warning: ${warning}`);
    });
  }

  /**
   * Identify performance bottlenecks
   */
  private identifyBottlenecks(metrics: PerformanceMetric[]): string[] {
    const bottlenecks: string[] = [];

    const durations = metrics.map(m => m.duration);
    const averageDuration = this.calculateAverage(durations);
    const p95Duration = this.calculatePercentile(durations, 95);

    // Check for slow operations
    if (averageDuration > 2000) {
      bottlenecks.push('Average operation duration exceeds 2 seconds');
    }

    if (p95Duration > 5000) {
      bottlenecks.push('95th percentile duration exceeds 5 seconds');
    }

    // Check for memory issues
    const memoryDeltas = metrics.map(m => m.memoryUsage.delta);
    const averageMemoryDelta = this.calculateAverage(memoryDeltas);

    if (averageMemoryDelta > 20 * 1024 * 1024) {
      // 20MB
      bottlenecks.push('High average memory usage per operation');
    }

    // Check for failure rate
    const failureRate =
      1 - metrics.filter(m => m.success).length / metrics.length;
    if (failureRate > 0.05) {
      // 5% failure rate
      bottlenecks.push('High operation failure rate');
    }

    return bottlenecks;
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(
    metrics: PerformanceMetric[],
    stats: {
      averageDuration: number;
      averageMemoryUsage: number;
      successRate: number;
    }
  ): string[] {
    const recommendations: string[] = [];

    // Duration recommendations
    if (stats.averageDuration > 3000) {
      recommendations.push(
        'Consider implementing content size limits or chunking for large documents'
      );
    }

    if (stats.averageDuration > 1000) {
      recommendations.push(
        'Optimize HTML parsing or consider caching frequently converted content'
      );
    }

    // Memory recommendations
    if (stats.averageMemoryUsage > 30 * 1024 * 1024) {
      // 30MB
      recommendations.push(
        'Implement memory optimization strategies or process content in smaller chunks'
      );
    }

    // Success rate recommendations
    if (stats.successRate < 0.95) {
      recommendations.push(
        'Investigate conversion failures and improve error handling'
      );
    }

    // Strategy recommendations
    const strategyStats = this.getStrategyStats(metrics);
    const bestStrategy = Object.entries(strategyStats).sort(
      ([, a], [, b]) =>
        b.successRate * 1000 -
        b.avgDuration -
        (a.successRate * 1000 - a.avgDuration)
    )[0];

    if (bestStrategy) {
      recommendations.push(
        `Consider prioritizing '${bestStrategy[0]}' strategy for better performance`
      );
    }

    return recommendations;
  }

  /**
   * Analyze performance trends
   */
  private analyzeTrends(
    metrics: PerformanceMetric[]
  ): PerformanceAnalysis['trends'] {
    if (metrics.length < 20) {
      return {
        durationTrend: 'stable',
        memoryTrend: 'stable',
        successRateTrend: 'stable',
      };
    }

    const halfPoint = Math.floor(metrics.length / 2);
    const firstHalf = metrics.slice(0, halfPoint);
    const secondHalf = metrics.slice(halfPoint);

    // Duration trend
    const firstHalfAvgDuration = this.calculateAverage(
      firstHalf.map(m => m.duration)
    );
    const secondHalfAvgDuration = this.calculateAverage(
      secondHalf.map(m => m.duration)
    );
    const durationChange =
      (secondHalfAvgDuration - firstHalfAvgDuration) / firstHalfAvgDuration;

    // Memory trend
    const firstHalfAvgMemory = this.calculateAverage(
      firstHalf.map(m => m.memoryUsage.delta)
    );
    const secondHalfAvgMemory = this.calculateAverage(
      secondHalf.map(m => m.memoryUsage.delta)
    );
    const memoryChange =
      (secondHalfAvgMemory - firstHalfAvgMemory) / Math.abs(firstHalfAvgMemory);

    // Success rate trend
    const firstHalfSuccessRate =
      firstHalf.filter(m => m.success).length / firstHalf.length;
    const secondHalfSuccessRate =
      secondHalf.filter(m => m.success).length / secondHalf.length;
    const successRateChange = secondHalfSuccessRate - firstHalfSuccessRate;

    return {
      durationTrend:
        durationChange < -0.1
          ? 'improving'
          : durationChange > 0.1
            ? 'degrading'
            : 'stable',
      memoryTrend:
        memoryChange < -0.1
          ? 'improving'
          : memoryChange > 0.1
            ? 'degrading'
            : 'stable',
      successRateTrend:
        successRateChange > 0.05
          ? 'improving'
          : successRateChange < -0.05
            ? 'degrading'
            : 'stable',
    };
  }

  /**
   * Get strategy performance statistics
   */
  private getStrategyStats(
    metrics: PerformanceMetric[]
  ): Record<
    string,
    { avgDuration: number; successRate: number; count: number }
  > {
    const strategyStats: Record<
      string,
      { durations: number[]; successes: number; count: number }
    > = {};

    metrics.forEach(metric => {
      if (!strategyStats[metric.strategy]) {
        strategyStats[metric.strategy] = {
          durations: [],
          successes: 0,
          count: 0,
        };
      }

      strategyStats[metric.strategy].durations.push(metric.duration);
      strategyStats[metric.strategy].count++;
      if (metric.success) {
        strategyStats[metric.strategy].successes++;
      }
    });

    const result: Record<
      string,
      { avgDuration: number; successRate: number; count: number }
    > = {};
    Object.entries(strategyStats).forEach(([strategy, stats]) => {
      result[strategy] = {
        avgDuration: this.calculateAverage(stats.durations),
        successRate: stats.successes / stats.count,
        count: stats.count,
      };
    });

    return result;
  }

  /**
   * Analyze a specific set of metrics
   */
  private analyzeMetrics(metrics: PerformanceMetric[]): PerformanceAnalysis {
    if (metrics.length === 0) {
      return this.getEmptyAnalysis();
    }

    const totalOperations = metrics.length;
    const successfulOps = metrics.filter(m => m.success).length;
    const successRate = successfulOps / totalOperations;

    const durations = metrics.map(m => m.duration);
    const memoryDeltas = metrics.map(m => m.memoryUsage.delta);

    const averageDuration = this.calculateAverage(durations);
    const medianDuration = this.calculateMedian(durations);
    const p95Duration = this.calculatePercentile(durations, 95);
    const averageMemoryUsage = this.calculateAverage(memoryDeltas);

    const timeSpan =
      metrics[metrics.length - 1].timestamp - metrics[0].timestamp;
    const throughput = timeSpan > 0 ? (totalOperations * 1000) / timeSpan : 0;

    return {
      totalOperations,
      successRate,
      averageDuration,
      medianDuration,
      p95Duration,
      averageMemoryUsage,
      throughput,
      bottlenecks: this.identifyBottlenecks(metrics),
      recommendations: this.generateRecommendations(metrics, {
        averageDuration,
        averageMemoryUsage,
        successRate,
      }),
      trends: this.analyzeTrends(metrics),
    };
  }

  /**
   * Get empty analysis for when no metrics are available
   */
  private getEmptyAnalysis(): PerformanceAnalysis {
    return {
      totalOperations: 0,
      successRate: 0,
      averageDuration: 0,
      medianDuration: 0,
      p95Duration: 0,
      averageMemoryUsage: 0,
      throughput: 0,
      bottlenecks: [],
      recommendations: ['No performance data available'],
      trends: {
        durationTrend: 'stable',
        memoryTrend: 'stable',
        successRateTrend: 'stable',
      },
    };
  }

  /**
   * Calculate average of an array of numbers
   */
  private calculateAverage(numbers: number[]): number {
    if (numbers.length === 0) return 0;
    return numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
  }

  /**
   * Calculate median of an array of numbers
   */
  private calculateMedian(numbers: number[]): number {
    if (numbers.length === 0) return 0;

    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);

    return sorted.length % 2 === 0
      ? (sorted[mid - 1] + sorted[mid]) / 2
      : sorted[mid];
  }

  /**
   * Calculate percentile of an array of numbers
   */
  private calculatePercentile(numbers: number[], percentile: number): number {
    if (numbers.length === 0) return 0;

    const sorted = [...numbers].sort((a, b) => a - b);
    const index = Math.ceil((percentile / 100) * sorted.length) - 1;

    return sorted[Math.max(0, Math.min(index, sorted.length - 1))];
  }

  /**
   * Clear all metrics (useful for testing)
   */
  public clearMetrics(): void {
    this.metrics = [];
  }

  /**
   * Get raw metrics for debugging
   */
  public getMetrics(): PerformanceMetric[] {
    return [...this.metrics];
  }
}
