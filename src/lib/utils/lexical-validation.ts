/**
 * Lexical Validation Service
 *
 * Provides comprehensive validation and normalization for PayloadCMS Lexical field data.
 * Ensures proper rendering in admin interface and prevents raw JSON display.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

// JSDOM import - only available on server side
let JSDOM: any = null;

async function loadJSDOM() {
  if (typeof window === 'undefined' && !JSDOM) {
    try {
      const jsdom = await import('jsdom');
      JSDOM = jsdom.JSDOM;
    } catch (error) {
      console.warn('JSDOM not available, HTML conversion will be limited');
    }
  }
}

/**
 * Lexical node types supported by PayloadCMS
 */
export type LexicalNodeType =
  | 'root'
  | 'paragraph'
  | 'text'
  | 'heading'
  | 'linebreak'
  | 'link';

/**
 * Base Lexical node structure
 */
export interface LexicalNode {
  type: LexicalNodeType;
  version: number;
  children?: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string | number;
  indent?: number;
  text?: string;
  tag?: string;
  textFormat?: number;
}

/**
 * Complete Lexical document structure
 */
export interface LexicalDocument {
  root: LexicalNode;
}

/**
 * Validation result interface
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  normalized?: LexicalDocument;
}

/**
 * Create an empty Lexical state with proper structure
 * This serves as a fallback for invalid or missing content
 */
export function createEmptyLexicalState(): LexicalDocument {
  return {
    root: {
      type: 'root',
      version: 1,
      direction: 'ltr',
      format: '',
      indent: 0,
      children: [
        {
          type: 'paragraph',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          textFormat: 0,
          children: [],
        },
      ],
    },
  };
}

/**
 * Validate if content is a proper Lexical document structure
 * Performs deep validation of the document tree
 */
export function validateLexicalData(content: unknown): ValidationResult {
  const errors: string[] = [];

  // Check if content exists and is an object
  if (!content || typeof content !== 'object' || content === null) {
    errors.push('Content must be a valid object');
    return {
      isValid: false,
      errors,
      normalized: createEmptyLexicalState(),
    };
  }

  const data = content as Partial<LexicalDocument>;

  // Check for root property
  if (!data.root) {
    errors.push('Missing required "root" property');
    return {
      isValid: false,
      errors,
      normalized: createEmptyLexicalState(),
    };
  }

  // Validate root node structure
  const rootValidation = validateLexicalNode(data.root, 'root');
  if (!rootValidation.isValid) {
    errors.push(...rootValidation.errors.map(err => `Root node: ${err}`));
  }

  // If validation failed, return normalized version
  if (errors.length > 0) {
    return {
      isValid: false,
      errors,
      normalized: normalizeLexicalData(content),
    };
  }

  return {
    isValid: true,
    errors: [],
  };
}

/**
 * Validate individual Lexical node structure
 */
export function validateLexicalNode(
  node: unknown,
  expectedType?: LexicalNodeType
): ValidationResult {
  const errors: string[] = [];

  if (!node || typeof node !== 'object' || node === null) {
    errors.push('Node must be a valid object');
    return { isValid: false, errors };
  }

  const lexicalNode = node as Partial<LexicalNode>;

  // Check required properties
  if (!lexicalNode.type) {
    errors.push('Missing required "type" property');
  } else if (expectedType && lexicalNode.type !== expectedType) {
    errors.push(`Expected type "${expectedType}", got "${lexicalNode.type}"`);
  }

  if (typeof lexicalNode.version !== 'number') {
    errors.push('Missing or invalid "version" property (must be number)');
  }

  // Validate children if present
  if (lexicalNode.children) {
    if (!Array.isArray(lexicalNode.children)) {
      errors.push('Children property must be an array');
    } else {
      lexicalNode.children.forEach((child, index) => {
        const childValidation = validateLexicalNode(child);
        if (!childValidation.isValid) {
          errors.push(
            ...childValidation.errors.map(err => `Child ${index}: ${err}`)
          );
        }
      });
    }
  }

  return {
    isValid: errors.length === 0,
    errors,
  };
}

/**
 * Normalize Lexical data to ensure proper structure
 * Attempts to fix common issues and create valid Lexical document
 */
export function normalizeLexicalData(content: unknown): LexicalDocument {
  // If content is null, undefined, or not an object, return empty state
  if (!content || typeof content !== 'object' || content === null) {
    // Only log warning if content was explicitly provided but invalid
    // Don't log for undefined/null values which are expected for optional fields
    if (content !== undefined && content !== null) {
      console.warn(
        'Lexical validation: Invalid content, returning empty state'
      );
    }
    return createEmptyLexicalState();
  }

  const data = content as any;

  // Check for circular references first
  try {
    JSON.stringify(data);
  } catch (error) {
    console.warn(
      'Lexical validation: Circular reference detected, returning empty state'
    );
    return createEmptyLexicalState();
  }

  // Create visited set to prevent circular references during normalization
  const visited = new WeakSet();

  // Try to extract or create root node
  let rootNode: LexicalNode;

  if (data.root && typeof data.root === 'object') {
    rootNode = normalizeNode(data.root, 'root', visited);
  } else {
    // Create new root node
    rootNode = {
      type: 'root',
      version: 1,
      direction: 'ltr',
      format: '',
      indent: 0,
      children: [],
    };
  }

  // Ensure root has children array
  if (!Array.isArray(rootNode.children)) {
    rootNode.children = [];
  }

  // If root has no children, add empty paragraph
  if (rootNode.children.length === 0) {
    rootNode.children.push({
      type: 'paragraph',
      version: 1,
      direction: 'ltr',
      format: '',
      indent: 0,
      textFormat: 0,
      children: [],
    });
  }

  return { root: rootNode };
}

/**
 * Normalize individual node to ensure proper structure
 */
export function normalizeNode(
  node: any,
  expectedType?: LexicalNodeType,
  visited?: WeakSet<object>
): LexicalNode {
  if (!node || typeof node !== 'object') {
    // Return default node based on expected type
    if (expectedType === 'root') {
      return {
        type: 'root',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        children: [],
      };
    }
    return {
      type: 'paragraph',
      version: 1,
      direction: 'ltr',
      format: '',
      indent: 0,
      textFormat: 0,
      children: [],
    };
  }

  // Check for circular references if visited set is provided
  if (visited) {
    if (visited.has(node)) {
      console.warn(
        'Lexical validation: Circular reference detected in node, skipping'
      );
      return {
        type: 'paragraph',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        textFormat: 0,
        children: [],
      };
    }
    visited.add(node);
  }

  // Ensure required properties exist
  const normalized: LexicalNode = {
    type: node.type || expectedType || 'paragraph',
    version: typeof node.version === 'number' ? node.version : 1,
    ...node,
  };

  // Normalize children if present
  if (node.children && Array.isArray(node.children)) {
    normalized.children = node.children.map((child: any) =>
      normalizeNode(child, undefined, visited)
    );
  }

  // Set default properties based on node type
  if (normalized.type === 'root') {
    normalized.direction = normalized.direction || 'ltr';
    normalized.format = normalized.format || '';
    normalized.indent =
      typeof normalized.indent === 'number' ? normalized.indent : 0;
  } else if (normalized.type === 'paragraph') {
    normalized.direction = normalized.direction || 'ltr';
    normalized.format = normalized.format || '';
    normalized.indent =
      typeof normalized.indent === 'number' ? normalized.indent : 0;
    normalized.textFormat =
      typeof normalized.textFormat === 'number' ? normalized.textFormat : 0;
  }

  return normalized;
}

/**
 * Check if content is already in valid Lexical format
 * Quick validation for performance-critical operations
 */
export function isValidLexicalFormat(content: unknown): boolean {
  if (!content || typeof content !== 'object' || content === null) {
    return false;
  }

  const data = content as any;
  return !!(
    data.root &&
    typeof data.root === 'object' &&
    data.root.type === 'root' &&
    Array.isArray(data.root.children)
  );
}

/**
 * Prepare Lexical data for storage - ensures proper format before saving
 * This is the main function used in beforeChange hooks
 */
export function prepareLexicalForStorage(data: unknown): LexicalDocument {
  // Handle undefined/null gracefully for optional fields
  if (data === undefined || data === null) {
    return createEmptyLexicalState();
  }

  if (typeof data === 'string') {
    try {
      const parsed = JSON.parse(data);
      return normalizeLexicalData(parsed);
    } catch {
      return createEmptyLexicalState();
    }
  }

  return normalizeLexicalData(data);
}

/**
 * Prepare Lexical data for reading - ensures proper format for display
 * This is the main function used in afterRead hooks
 */
export function prepareLexicalForReading(data: unknown): LexicalDocument {
  if (!data) {
    return createEmptyLexicalState();
  }

  const validation = validateLexicalData(data);
  if (!validation.isValid) {
    console.warn(
      'Invalid Lexical data detected, normalizing:',
      validation.errors
    );
    return validation.normalized || createEmptyLexicalState();
  }

  return data as LexicalDocument;
}

/**
 * Create simple text-based Lexical document (browser-safe fallback)
 */
function createSimpleTextLexical(html: string): LexicalDocument {
  // Simple HTML tag removal for browser environment
  const textContent = html
    .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '') // Remove scripts
    .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '') // Remove styles
    .replace(/<[^>]+>/g, ' ') // Remove HTML tags
    .replace(/\s+/g, ' ') // Normalize whitespace
    .trim();

  if (!textContent) {
    return createEmptyLexicalState();
  }

  return {
    root: {
      type: 'root',
      version: 1,
      direction: 'ltr',
      format: '',
      indent: 0,
      children: [
        {
          type: 'paragraph',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          textFormat: 0,
          children: [
            {
              type: 'text',
              version: 1,
              text: textContent,
              format: 0,
            },
          ],
        },
      ],
    },
  };
}

/**
 * Convert HTML string to basic Lexical structure
 * Used as fallback when content is in HTML format
 */
export function htmlToBasicLexical(html: string): LexicalDocument {
  if (!html || typeof html !== 'string') {
    return createEmptyLexicalState();
  }

  // Check if we're in browser environment or JSDOM is not available
  if (typeof window !== 'undefined' || !JSDOM) {
    console.warn(
      'htmlToBasicLexical: JSDOM not available, using simple text extraction'
    );
    return createSimpleTextLexical(html);
  }

  try {
    const dom = new JSDOM(html);
    const document = dom.window.document;
    const body = document.body;

    const children: LexicalNode[] = [];

    // Convert each child element to Lexical nodes
    Array.from(body.children).forEach(element => {
      const node = htmlElementToLexicalNode(element as Element);
      if (node) {
        children.push(node);
      }
    });

    // If no children found, create paragraph from text content
    if (children.length === 0 && body.textContent?.trim()) {
      children.push({
        type: 'paragraph',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        textFormat: 0,
        children: [
          {
            type: 'text',
            version: 1,
            text: body.textContent.trim(),
            format: 0,
          },
        ],
      });
    }

    return {
      root: {
        type: 'root',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        children:
          children.length > 0
            ? children
            : [
                {
                  type: 'paragraph',
                  version: 1,
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  textFormat: 0,
                  children: [],
                },
              ],
      },
    };
  } catch (error) {
    console.error('Error converting HTML to Lexical:', error);
    return createEmptyLexicalState();
  }
}

/**
 * Convert HTML element to Lexical node
 */
function htmlElementToLexicalNode(element: Element): LexicalNode | null {
  const tagName = element.tagName.toLowerCase();

  switch (tagName) {
    case 'p':
      return {
        type: 'paragraph',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        textFormat: 0,
        children: extractTextNodes(element),
      };
    case 'h1':
    case 'h2':
    case 'h3':
      return {
        type: 'heading',
        version: 1,
        tag: tagName,
        direction: 'ltr',
        format: '',
        indent: 0,
        children: extractTextNodes(element),
      };
    case 'a':
      const href = element.getAttribute('href');
      const target = element.getAttribute('target');
      const rel = element.getAttribute('rel');

      // Create PayloadCMS-compatible link node
      const linkNode: any = {
        type: 'link',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        children: extractTextNodes(element),
      };

      // Add URL and attributes following PayloadCMS patterns
      if (href) {
        linkNode.url = href;
      }

      if (target === '_blank') {
        linkNode.newTab = true;
      }

      if (rel) {
        linkNode.rel = rel.split(' ').filter(r => r.trim() !== '');
      }

      console.log('🔍 HTML→LEXICAL: Created link node:', {
        href,
        target,
        rel,
        newTab: linkNode.newTab,
        relArray: linkNode.rel,
      });

      return linkNode;
    default:
      // For other elements, treat as paragraph
      if (element.textContent?.trim()) {
        return {
          type: 'paragraph',
          version: 1,
          direction: 'ltr',
          format: '',
          indent: 0,
          textFormat: 0,
          children: extractTextNodes(element),
        };
      }
      return null;
  }
}

/**
 * Extract text nodes from HTML element
 */
function extractTextNodes(element: Element): LexicalNode[] {
  const textContent = element.textContent?.trim();
  if (!textContent) {
    return [];
  }

  return [
    {
      type: 'text',
      version: 1,
      text: textContent,
      format: 0,
    },
  ];
}
