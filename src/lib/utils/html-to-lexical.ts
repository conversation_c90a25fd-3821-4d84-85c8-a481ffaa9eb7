/**
 * HTML-to-Lexical Converter
 *
 * Superior HTML→Lexical conversion using PayloadCMS native functions.
 * Replaces suboptimal Markdown→Lexical pipeline for better semantic structure preservation.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

import {
  convertHTMLToLexical,
  editorConfigFactory,
} from '@payloadcms/richtext-lexical';

// Server-side only imports
let JSDOM: any = null;
if (typeof window === 'undefined') {
  try {
    JSDOM = require('jsdom').JSDOM;
  } catch (error) {
    console.warn('JSDOM not available for HTML conversion');
  }
}

import config from '../../payload.config';
import { createEmptyLexicalState } from './lexical-validation';

/**
 * HTML quality analysis result
 */
export interface HTMLQualityAnalysis {
  isComplex: boolean;
  hasStructuredContent: boolean;
  hasRichFormatting: boolean;
  isFinancialContent: boolean;
  recommendedFormat: 'html' | 'markdown' | 'text';
  score: number;
  details: {
    elementCount: number;
    hasHeadings: boolean;
    hasTables: boolean;
    hasLists: boolean;
    hasLinks: boolean;
    hasImages: boolean;

    hasGermanContent: boolean;
    complexity: 'low' | 'medium' | 'high';
  };
}

/**
 * Conversion performance metrics
 */
export interface ConversionMetrics {
  startTime: number;
  endTime: number;
  duration: number;
  memoryBefore: NodeJS.MemoryUsage;
  memoryAfter: NodeJS.MemoryUsage;
  success: boolean;
  fallbackUsed: boolean;
  method: 'html' | 'markdown' | 'text';
}

/**
 * Convert HTML to Lexical using PayloadCMS native conversion
 * Superior to markdown conversion for semantic structure preservation
 */
export async function htmlToLexical(html: string): Promise<{
  result: object;
  metrics: ConversionMetrics;
}> {
  // Check if we're in browser environment
  if (typeof window !== 'undefined') {
    throw new Error('htmlToLexical can only be used on the server side');
  }

  const startTime = Date.now();
  const memoryBefore = process.memoryUsage();

  if (!html || typeof html !== 'string') {
    console.warn('htmlToLexical: Empty or invalid HTML input');
    return {
      result: createEmptyLexicalState(),
      metrics: {
        startTime,
        endTime: Date.now(),
        duration: Date.now() - startTime,
        memoryBefore,
        memoryAfter: process.memoryUsage(),
        success: false,
        fallbackUsed: false,
        method: 'text',
      },
    };
  }

  try {
    console.time('html-to-lexical-conversion');

    // Sanitize HTML to fix common link issues before conversion
    const sanitizedHtml = sanitizeHtmlForLexical(html);

    // Get the resolved PayloadCMS config
    const resolvedConfig = await config;

    // Create editor config using PayloadCMS factory
    const editorConfig = await editorConfigFactory.default({
      config: resolvedConfig,
    });

    // Convert HTML to Lexical using PayloadCMS native function
    const lexicalResult = convertHTMLToLexical({
      editorConfig,
      html: sanitizedHtml,
      JSDOM,
    });

    console.timeEnd('html-to-lexical-conversion');

    const endTime = Date.now();
    const memoryAfter = process.memoryUsage();

    console.log('✅ HTML→Lexical conversion successful', {
      duration: endTime - startTime,
      memoryDelta: memoryAfter.heapUsed - memoryBefore.heapUsed,
    });

    return {
      result: lexicalResult,
      metrics: {
        startTime,
        endTime,
        duration: endTime - startTime,
        memoryBefore,
        memoryAfter,
        success: true,
        fallbackUsed: false,
        method: 'html',
      },
    };
  } catch (error) {
    console.error('❌ HTML→Lexical conversion failed:', error);

    // Fallback to markdown conversion
    try {
      console.log('🔄 Attempting fallback to markdown conversion...');
      const { markdownToLexical } = await import('./lexical');

      // Convert HTML to markdown first (basic conversion)
      const markdown = htmlToMarkdownBasic(html);
      const fallbackResult = await markdownToLexical(markdown);

      const endTime = Date.now();
      const memoryAfter = process.memoryUsage();

      console.log('✅ Fallback markdown conversion successful');

      return {
        result: fallbackResult,
        metrics: {
          startTime,
          endTime,
          duration: endTime - startTime,
          memoryBefore,
          memoryAfter,
          success: true,
          fallbackUsed: true,
          method: 'markdown',
        },
      };
    } catch (fallbackError) {
      console.error('❌ Fallback conversion also failed:', fallbackError);

      const endTime = Date.now();
      const memoryAfter = process.memoryUsage();

      return {
        result: createEmptyLexicalState(),
        metrics: {
          startTime,
          endTime,
          duration: endTime - startTime,
          memoryBefore,
          memoryAfter,
          success: false,
          fallbackUsed: true,
          method: 'text',
        },
      };
    }
  }
}

/**
 * Validate HTML content for optimal conversion strategy
 * Analyzes structure complexity and recommends best conversion approach
 */
export function validateHTMLForConversion(html: string): HTMLQualityAnalysis {
  if (!html || typeof html !== 'string') {
    return {
      isComplex: false,
      hasStructuredContent: false,
      hasRichFormatting: false,
      isFinancialContent: false,
      recommendedFormat: 'text',
      score: 0,
      details: {
        elementCount: 0,
        hasHeadings: false,
        hasTables: false,
        hasLists: false,
        hasLinks: false,
        hasImages: false,
        hasGermanContent: false,
        complexity: 'low',
      },
    };
  }

  try {
    // Parse HTML using JSDOM for analysis
    const dom = new JSDOM(html);
    const document = dom.window.document;

    // Count different types of elements
    const elementCount = document.querySelectorAll('*').length;
    const hasHeadings =
      document.querySelectorAll('h1, h2, h3, h4, h5, h6').length > 0;
    const hasTables = document.querySelectorAll('table').length > 0;
    const hasLists = document.querySelectorAll('ul, ol').length > 0;
    const hasLinks = document.querySelectorAll('a').length > 0;
    const hasImages = document.querySelectorAll('img').length > 0;

    // Analyze text content for German content
    const textContent = document.body.textContent || '';
    const hasGermanContent = detectGermanContent(textContent);

    // Calculate complexity
    let complexityScore = 0;
    if (elementCount > 50) complexityScore += 3;
    else if (elementCount > 20) complexityScore += 2;
    else if (elementCount > 5) complexityScore += 1;

    if (hasTables) complexityScore += 2;
    if (hasLists) complexityScore += 1;
    if (hasLinks) complexityScore += 1;
    if (hasImages) complexityScore += 1;

    const complexity: 'low' | 'medium' | 'high' =
      complexityScore >= 5 ? 'high' : complexityScore >= 3 ? 'medium' : 'low';

    // Determine if content has rich formatting
    const hasRichFormatting =
      document.querySelectorAll('strong, b, em, i, u, mark').length > 0;

    // Determine if content is structured
    const hasStructuredContent = hasHeadings || hasTables || hasLists;

    // Calculate overall quality score (0-100)
    let score = 50; // Base score
    if (hasStructuredContent) score += 20;
    if (hasRichFormatting) score += 15;
    if (hasGermanContent) score += 5;
    if (complexity === 'high') score += 10;
    else if (complexity === 'medium') score += 5;

    // Recommend format based on analysis
    let recommendedFormat: 'html' | 'markdown' | 'text' = 'html';
    if (complexity === 'low' && !hasStructuredContent) {
      recommendedFormat = elementCount > 3 ? 'markdown' : 'text';
    }

    return {
      isComplex: complexity !== 'low',
      hasStructuredContent,
      hasRichFormatting,
      isFinancialContent: false,
      recommendedFormat,
      score: Math.min(100, Math.max(0, score)),
      details: {
        elementCount,
        hasHeadings,
        hasTables,
        hasLists,
        hasLinks,
        hasImages,
        hasGermanContent,
        complexity,
      },
    };
  } catch (error) {
    console.error('Error analyzing HTML quality:', error);
    return {
      isComplex: false,
      hasStructuredContent: false,
      hasRichFormatting: false,
      isFinancialContent: false,
      recommendedFormat: 'markdown',
      score: 25,
      details: {
        elementCount: 0,
        hasHeadings: false,
        hasTables: false,
        hasLists: false,
        hasLinks: false,
        hasImages: false,
        hasGermanContent: false,
        complexity: 'low',
      },
    };
  }
}

/**
 * Detect German content in text
 */
function detectGermanContent(text: string): boolean {
  const germanIndicators = [
    // German articles and common words
    'der',
    'die',
    'das',
    'und',
    'oder',
    'aber',
    'mit',
    'von',
    'zu',
    'auf',
    'für',
    'bei',
    'nach',
    'über',
    'unter',
    'zwischen',
    'durch',
    'gegen',
    'ohne',
    'um',
    'während',
    'wegen',
    'trotz',
    'seit',
    'bis',
    'außer',

    // German umlauts and special characters
    'ä',
    'ö',
    'ü',
    'ß',
    'Ä',
    'Ö',
    'Ü',

    // Common German words
    'auch',
    'noch',
    'schon',
    'nur',
    'mehr',
    'sehr',
    'gut',
    'neue',
    'ersten',
    'große',
    'deutschen',
    'anderen',
    'beiden',
    'eigenen',
    'verschiedenen',
  ];

  const lowerText = text.toLowerCase();
  const matches = germanIndicators.filter(indicator =>
    lowerText.includes(indicator.toLowerCase())
  ).length;

  // Consider it German if we find at least 3 German indicators
  return matches >= 3;
}

/**
 * Sanitize HTML to fix common issues that cause Lexical validation errors
 * Specifically handles problematic link elements that cause "Enter a URL" errors
 */
function sanitizeHtmlForLexical(html: string): string {
  if (!html || typeof html !== 'string') {
    return '';
  }

  try {
    // Use JSDOM to parse and sanitize HTML
    if (!JSDOM) {
      console.warn('JSDOM not available, skipping HTML sanitization');
      return html;
    }

    const dom = new JSDOM(html);
    const document = dom.window.document;

    // Fix problematic links
    const links = document.querySelectorAll('a');
    links.forEach((link: any) => {
      const href = link.getAttribute('href');

      // Only remove genuinely problematic links, preserve valid ones
      if (
        !href ||
        href.trim() === '' ||
        href.startsWith('javascript:') ||
        href.length > 2000 // Very long URLs can cause issues
      ) {
        // Convert to span to preserve text content but remove problematic link
        const span = document.createElement('span');
        span.textContent = link.textContent || '';
        link.parentNode?.replaceChild(span, link);
      } else {
        // Keep ALL valid links - be much less aggressive
        // Only remove clearly dangerous links (javascript:, data:, etc.)
        if (href.startsWith('javascript:') || href.startsWith('data:')) {
          console.warn(`Removing dangerous link: ${href}`);
          const span = document.createElement('span');
          span.textContent = link.textContent || '';
          link.parentNode?.replaceChild(span, link);
        } else {
          // Preserve ALL other links including relative URLs, domains, etc.
          console.log(`Preserving link: ${href}`);
        }
      }
    });

    // Remove problematic attributes that might cause validation issues
    const allElements = document.querySelectorAll('*');
    allElements.forEach((element: any) => {
      // Remove data attributes that might contain URLs
      const attributes = Array.from(element.attributes);
      attributes.forEach((attr: any) => {
        if (
          attr.name.startsWith('data-') &&
          (attr.value.includes('http') || attr.value.includes('www.'))
        ) {
          element.removeAttribute(attr.name);
        }
      });
    });

    return document.body.innerHTML;
  } catch (error) {
    console.warn('Error sanitizing HTML for Lexical conversion:', error);
    return html; // Return original HTML if sanitization fails
  }
}

/**
 * Basic HTML to Markdown conversion for fallback
 * Simple conversion without external dependencies
 */
function htmlToMarkdownBasic(html: string): string {
  if (!html) return '';

  try {
    // Parse HTML for basic conversion

    // Simple conversion rules
    let markdown = html;

    // Headers
    markdown = markdown.replace(
      /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi,
      (_, level, content) => {
        const hashes = '#'.repeat(parseInt(level));
        return `\n${hashes} ${content.trim()}\n`;
      }
    );

    // Paragraphs
    markdown = markdown.replace(/<p[^>]*>(.*?)<\/p>/gi, '\n$1\n');

    // Bold and italic
    markdown = markdown.replace(
      /<(strong|b)[^>]*>(.*?)<\/(strong|b)>/gi,
      '**$2**'
    );
    markdown = markdown.replace(/<(em|i)[^>]*>(.*?)<\/(em|i)>/gi, '*$2*');

    // Lists
    markdown = markdown.replace(/<li[^>]*>(.*?)<\/li>/gi, '- $1\n');
    markdown = markdown.replace(/<\/?[uo]l[^>]*>/gi, '\n');

    // Remove remaining HTML tags
    markdown = markdown.replace(/<[^>]+>/g, '');

    // Clean up whitespace
    markdown = markdown.replace(/\n\s*\n\s*\n/g, '\n\n');
    markdown = markdown.trim();

    return markdown;
  } catch (error) {
    console.error('Error in basic HTML to markdown conversion:', error);
    // Return plain text as last resort
    return html
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }
}
