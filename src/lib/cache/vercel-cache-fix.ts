/**
 * URGENT: Vercel Edge Cache Invalidation Fix
 *
 * Critical fix for multi-layer caching issues causing intermittent content updates.
 * Addresses Vercel edge cache, race conditions, and comprehensive cache invalidation.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 * @priority CRITICAL - Fixes client publishing issues
 */

import { revalidateTag, revalidatePath } from 'next/cache';

/**
 * CRITICAL: Force Vercel edge cache invalidation
 * Vercel's edge cache can persist even after Next.js cache is cleared
 */
export async function forceVercelCacheInvalidation(paths: string[] = ['/']) {
  console.log('🚨 FORCING Vercel edge cache invalidation...');

  try {
    // Method 1: Revalidate paths with special options to force edge invalidation
    for (const path of paths) {
      try {
        // Use layout revalidation to force deeper cache clearing
        revalidatePath(path, 'layout');
        revalidatePath(path, 'page');
        console.log(`✅ Force-revalidated: ${path}`);
      } catch (error) {
        console.error(`❌ Failed to force-revalidate ${path}:`, error);
      }
    }

    // Method 2: Add small delay to ensure revalidation propagates
    await new Promise(resolve => setTimeout(resolve, 250));

    console.log('✅ Vercel edge cache invalidation completed');
    return true;
  } catch (error) {
    console.error('❌ Vercel edge cache invalidation failed:', error);
    return false;
  }
}

/**
 * RACE CONDITION FIX: Delayed cache invalidation with retries
 * Ensures database writes are fully committed before cache invalidation
 */
export async function delayedCacheInvalidation(
  invalidationFunction: () => Promise<void>,
  options: {
    initialDelay?: number;
    retries?: number;
    retryDelay?: number;
  } = {}
) {
  const { initialDelay = 500, retries = 3, retryDelay = 1000 } = options;

  console.log(
    `⏳ Delayed cache invalidation: waiting ${initialDelay}ms for database commit...`
  );

  // Initial delay to ensure database write is committed
  await new Promise(resolve => setTimeout(resolve, initialDelay));

  // Attempt invalidation with retries
  for (let attempt = 1; attempt <= retries; attempt++) {
    try {
      await invalidationFunction();
      console.log(`✅ Cache invalidation successful on attempt ${attempt}`);
      return true;
    } catch (error) {
      console.error(`❌ Cache invalidation attempt ${attempt} failed:`, error);

      if (attempt < retries) {
        console.log(`⏳ Retrying in ${retryDelay}ms...`);
        await new Promise(resolve => setTimeout(resolve, retryDelay));
      } else {
        console.error('❌ All cache invalidation attempts failed');
        throw error;
      }
    }
  }

  return false;
}

/**
 * COMPREHENSIVE ARTICLE CACHE INVALIDATION
 * Enhanced version that handles all cache layers including Vercel edge
 */
export async function comprehensiveArticleInvalidation(
  articleId: number,
  articleSlug?: string,
  categories: Array<{ id: number; slug: string }> = [],
  options: {
    isPublished?: boolean;
    isNewArticle?: boolean;
    forceEdgeInvalidation?: boolean;
  } = {}
) {
  console.log(
    `🚀 COMPREHENSIVE cache invalidation for article ${articleId}...`
  );

  const {
    isPublished = false,
    isNewArticle = false,
    forceEdgeInvalidation = true,
  } = options;

  // Wrap in delayed execution to handle race conditions
  return delayedCacheInvalidation(
    async () => {
      // Step 1: Standard Next.js cache invalidation
      const tagsToInvalidate = [
        'articles',
        `article-${articleId}`,
        ...(articleSlug ? [`articles_${articleSlug}`] : []),
      ];

      // Add homepage tier tags if published
      if (isPublished || isNewArticle) {
        tagsToInvalidate.push(
          'tier-1',
          'tier-2',
          'tier-3',
          'tier-1-articles',
          'tier-2-articles',
          'tier-3-articles',
          'featured-articles' // Invalidate featured articles when any article changes
        );
      }

      // Add category tags
      for (const category of categories) {
        tagsToInvalidate.push(
          `category-${category.id}`,
          `categories_${category.slug}`
        );
      }

      // Invalidate all tags
      console.log(`🏷️ Invalidating ${tagsToInvalidate.length} cache tags...`);
      await Promise.all(
        tagsToInvalidate.map(tag => {
          try {
            revalidateTag(tag);
            return Promise.resolve();
          } catch (error) {
            console.error(`Failed to invalidate tag ${tag}:`, error);
            return Promise.resolve(); // Don't fail entire process for one tag
          }
        })
      );

      // Step 2: Path invalidation with Vercel edge cache forcing
      const pathsToInvalidate = ['/'];

      if (articleSlug) {
        pathsToInvalidate.push(`/artikel/${articleSlug}`);
      }

      // Add category paths
      for (const category of categories) {
        pathsToInvalidate.push(`/kategorien/${category.slug}`);
      }

      console.log(`📄 Invalidating ${pathsToInvalidate.length} paths...`);

      // Force Vercel edge cache invalidation if requested
      if (forceEdgeInvalidation) {
        await forceVercelCacheInvalidation(pathsToInvalidate);
      } else {
        // Standard path invalidation
        await Promise.all(
          pathsToInvalidate.map(path => {
            try {
              revalidatePath(path);
              return Promise.resolve();
            } catch (error) {
              console.error(`Failed to invalidate path ${path}:`, error);
              return Promise.resolve();
            }
          })
        );
      }

      console.log('✅ Comprehensive article cache invalidation completed');
    },
    {
      initialDelay: 750, // Longer delay for article publishing
      retries: 3,
      retryDelay: 1500,
    }
  );
}

/**
 * NUCLEAR OPTION: Clear ALL caches site-wide
 * Use this when normal invalidation fails
 */
export async function nuclearCacheInvalidation() {
  console.log('🚨 NUCLEAR CACHE INVALIDATION - Clearing ALL caches...');

  try {
    // All possible cache tags
    const allTags = [
      'articles',
      'categories',
      'pages',
      'media',
      'tier-1',
      'tier-2',
      'tier-3',
      'tier-1-articles',
      'tier-2-articles',
      'tier-3-articles',
      'all-tier-articles',
      'layout',
      'navigation',
      'published-article-slugs',
      'published-page-slugs',
    ];

    console.log(`🏷️ Clearing ${allTags.length} cache tags...`);
    await Promise.all(
      allTags.map(tag => {
        try {
          revalidateTag(tag);
          return Promise.resolve();
        } catch (error) {
          console.error(`Failed to clear tag ${tag}:`, error);
          return Promise.resolve();
        }
      })
    );

    // Critical paths
    const criticalPaths = [
      '/',
      '/artikel/[slug]',
      '/kategorien/[slug]',
      '/[slug]',
    ];

    console.log(`📄 Force-clearing ${criticalPaths.length} critical paths...`);
    await forceVercelCacheInvalidation(criticalPaths);

    // Add extra delay to ensure propagation
    await new Promise(resolve => setTimeout(resolve, 1000));

    console.log('✅ Nuclear cache invalidation completed');
    return true;
  } catch (error) {
    console.error('❌ Nuclear cache invalidation failed:', error);
    return false;
  }
}

/**
 * IMAGE CACHE INVALIDATION FIX
 * Enhanced image cache busting with proper timestamp handling
 */
export async function fixImageCacheInvalidation(mediaId: number) {
  console.log(`🖼️ Fixing image cache for media ${mediaId}...`);

  try {
    // Force media-related cache tags
    const mediaTags = [
      'media',
      `media-${mediaId}`,
      'articles', // Articles that might use this media
      'pages', // Pages that might use this media
    ];

    await Promise.all(
      mediaTags.map(tag => {
        try {
          revalidateTag(tag);
          return Promise.resolve();
        } catch (error) {
          console.error(`Failed to invalidate media tag ${tag}:`, error);
          return Promise.resolve();
        }
      })
    );

    // Small delay to ensure media cache clears
    await new Promise(resolve => setTimeout(resolve, 200));

    console.log('✅ Image cache invalidation completed');
    return true;
  } catch (error) {
    console.error('❌ Image cache invalidation failed:', error);
    return false;
  }
}

/**
 * CACHE HEALTH CHECK
 * Validates that cache invalidation is working properly
 */
export async function validateCacheHealth(): Promise<{
  status: 'healthy' | 'degraded' | 'unhealthy';
  issues: string[];
  recommendations: string[];
}> {
  const issues: string[] = [];
  const recommendations: string[] = [];

  try {
    // Test basic cache functions
    revalidateTag('test-tag');
    revalidatePath('/');

    console.log('✅ Basic cache functions working');
  } catch (error) {
    issues.push('Basic cache functions failing');
    recommendations.push('Check Next.js cache configuration');
  }

  // Check environment variables
  if (!process.env.VERCEL_URL && !process.env.VERCEL_PROJECT_PRODUCTION_URL) {
    issues.push('Vercel environment not detected');
    recommendations.push('Ensure proper Vercel deployment configuration');
  }

  // Determine status
  let status: 'healthy' | 'degraded' | 'unhealthy' = 'healthy';
  if (issues.length > 0) {
    status = issues.length > 2 ? 'unhealthy' : 'degraded';
  }

  return { status, issues, recommendations };
}
