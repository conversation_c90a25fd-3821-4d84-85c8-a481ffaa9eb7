/**
 * Enhanced Content Processor
 *
 * Unified processing pipeline that integrates all previous sprint components
 * for superior content quality and performance. Leverages HTML-first conversion,
 * intelligent format selection, and optimized title processing.
 *
 * Features:
 * - Enhanced Firecrawl client with multi-format extraction (Sprint 3)
 * - HTML-to-Lexical conversion with fallbacks (Sprint 2)
 * - Title optimization and cleaning (Sprint 4)
 * - Lexical validation and quality assurance (Sprint 1)
 * - Intelligent format selection and performance optimization
 * - Comprehensive error handling and fallback mechanisms
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 5: Content Processor Integration
 */

// Import all component utilities from previous sprints
import {
  extractContentEnhanced,
  type EnhancedExtractionResult,
} from '../integrations/firecrawl/enhanced-client';
import {
  htmlToLexical,
  validateHTMLForConversion,
} from '../utils/html-to-lexical';
import {
  createEmptyLexicalState,
  validateLexicalData,
} from '../utils/lexical-validation';
import {
  cleanEnglishTitle,
  cleanSourceTitle,
  optimizeGermanFinancialTitle,
  validateTitleQuality,
} from '../utils/title-optimization';

/**
 * Enhanced processing result with comprehensive metadata
 */
export interface EnhancedProcessingResult {
  success: boolean;
  url: string;
  timestamp: Date;

  // Processed content
  content: {
    lexical: object;
    originalFormat: 'html' | 'markdown' | 'structured';
    fallbackUsed: boolean;
  };

  // Optimized titles
  titles: {
    original: string;
    cleaned: string;
    english?: string;
    optimized: string;
    quality: {
      score: number;
      issues: string[];
      recommendations: string[];
    };
  };

  // Processing metadata
  metadata: {
    extractionMethod: 'standard' | 'structured' | 'stealth';
    conversionMethod: 'html' | 'markdown' | 'fallback';
    processingTime: number;
    contentQuality: number;
    isFinancialContent: boolean;
    isGermanContent: boolean;
    wordCount: number;
    readingTime: number;
  };

  // Performance metrics
  performance: {
    extractionTime: number;
    conversionTime: number;
    titleProcessingTime: number;
    totalTime: number;
    memoryUsage: number;
    cacheHit: boolean;
  };

  // Warnings and errors
  warnings: string[];
  errors: string[];

  // Fallback information
  fallbacks: {
    extractionFallback: boolean;
    conversionFallback: boolean;
    titleFallback: boolean;
  };
}

/**
 * Processing options for enhanced content processor
 */
export interface EnhancedProcessingOptions {
  // Format preferences
  preferredFormat?: 'html' | 'markdown' | 'auto';
  enableStructuredExtraction?: boolean;
  enableStealthMode?: boolean;

  // Title processing
  optimizeTitles?: boolean;
  generateEnglishTitles?: boolean;

  // Performance options
  enableCaching?: boolean;
  maxProcessingTime?: number;

  // Quality options
  minContentQuality?: number;
  validateOutput?: boolean;

  // Fallback options
  enableFallbacks?: boolean;
  maxRetries?: number;
}

/**
 * Content processing cache for performance optimization
 */
interface ProcessingCache {
  [url: string]: {
    result: EnhancedProcessingResult;
    timestamp: Date;
    expiresAt: Date;
  };
}

// Global cache instance
const processingCache: ProcessingCache = {};
const CACHE_DURATION = 30 * 60 * 1000; // 30 minutes

/**
 * Main enhanced content processing function
 * Orchestrates all components for optimal content processing
 */
export async function processContentEnhanced(
  url: string,
  options: EnhancedProcessingOptions = {}
): Promise<EnhancedProcessingResult> {
  const startTime = Date.now();
  const memoryBefore = process.memoryUsage();

  console.log(`🚀 Enhanced content processing starting for: ${url}`);

  // Set default options
  const opts = {
    preferredFormat: 'auto',
    enableStructuredExtraction: true,
    enableStealthMode: true,
    optimizeTitles: true,
    generateEnglishTitles: true,
    enableCaching: true,
    maxProcessingTime: 30000, // 30 seconds
    minContentQuality: 50,
    validateOutput: true,
    enableFallbacks: true,
    maxRetries: 3,
    ...options,
  } as Required<EnhancedProcessingOptions>;

  // Check cache first
  if (opts.enableCaching) {
    const cached = getCachedResult(url);
    if (cached) {
      console.log(`💾 Cache hit for: ${url}`);
      cached.performance.cacheHit = true;
      return cached;
    }
  }

  try {
    // Step 1: Enhanced content extraction
    console.log(`📡 Step 1: Enhanced content extraction`);
    const extractionStart = Date.now();
    const extractionResult = await extractContentEnhanced(url);
    const extractionTime = Date.now() - extractionStart;

    if (!extractionResult.success) {
      return createErrorResult(
        url,
        'Content extraction failed',
        extractionResult.errors,
        startTime,
        memoryBefore
      );
    }

    // Step 2: Intelligent format selection
    console.log(`🎯 Step 2: Intelligent format selection`);
    const selectedFormat = selectOptimalFormat(extractionResult, opts);
    console.log(`📋 Selected format: ${selectedFormat}`);

    // Step 3: Content conversion to Lexical
    console.log(`🔄 Step 3: Content conversion to Lexical`);
    const conversionStart = Date.now();
    const conversionResult = await convertToLexical(
      extractionResult,
      selectedFormat,
      opts
    );
    const conversionTime = Date.now() - conversionStart;

    if (!conversionResult.success) {
      return createErrorResult(
        url,
        'Content conversion failed',
        conversionResult.errors,
        startTime,
        memoryBefore
      );
    }

    // Step 4: Title processing and optimization
    console.log(`📝 Step 4: Title processing and optimization`);
    const titleStart = Date.now();
    const titleResult = await processTitles(extractionResult, opts);
    const titleProcessingTime = Date.now() - titleStart;

    // Step 5: Quality validation and final assembly
    console.log(`✅ Step 5: Quality validation and final assembly`);
    const finalResult = assembleProcessingResult(
      url,
      extractionResult,
      conversionResult,
      titleResult,
      {
        extractionTime,
        conversionTime,
        titleProcessingTime,
        totalTime: Date.now() - startTime,
        memoryUsage: process.memoryUsage().heapUsed - memoryBefore.heapUsed,
        cacheHit: false,
      },
      startTime,
      memoryBefore
    );

    // Cache successful result
    if (opts.enableCaching && finalResult.success) {
      cacheResult(url, finalResult);
    }

    console.log(
      `✅ Enhanced processing completed in ${finalResult.performance.totalTime}ms`
    );
    return finalResult;
  } catch (error: unknown) {
    const errorMessage =
      error instanceof Error ? error.message : 'Unknown processing error';
    console.error(`❌ Enhanced processing failed:`, errorMessage);
    return createErrorResult(
      url,
      errorMessage,
      [errorMessage],
      startTime,
      memoryBefore
    );
  }
}

/**
 * Select optimal format based on content analysis and options
 */
function selectOptimalFormat(
  extractionResult: EnhancedExtractionResult,
  options: Required<EnhancedProcessingOptions>
): 'html' | 'markdown' | 'structured' {
  // If user specified a preference, respect it (unless auto)
  if (options.preferredFormat !== 'auto') {
    if (options.preferredFormat === 'html' && extractionResult.formats.html) {
      return 'html';
    }
    if (
      options.preferredFormat === 'markdown' &&
      extractionResult.formats.markdown
    ) {
      return 'markdown';
    }
  }

  // Check if structured data is available and preferred
  if (
    options.enableStructuredExtraction &&
    extractionResult.formats.structured
  ) {
    console.log(`📊 Structured data available, using structured format`);
    return 'structured';
  }

  // Analyze HTML quality if available
  if (extractionResult.formats.html) {
    const htmlAnalysis = validateHTMLForConversion(
      extractionResult.formats.html
    );

    // Prefer HTML for complex or financial content
    if (htmlAnalysis.isComplex || htmlAnalysis.isFinancialContent) {
      console.log(`🏗️ Complex/financial content detected, using HTML format`);
      return 'html';
    }

    // Use HTML if quality score is high
    if (htmlAnalysis.score > 70) {
      console.log(
        `⭐ High quality HTML detected (score: ${htmlAnalysis.score}), using HTML format`
      );
      return 'html';
    }
  }

  // Fallback to markdown if available
  if (extractionResult.formats.markdown) {
    console.log(`📄 Using markdown format as fallback`);
    return 'markdown';
  }

  // Final fallback to HTML if nothing else available
  if (extractionResult.formats.html) {
    console.log(`🔄 Using HTML format as final fallback`);
    return 'html';
  }

  // This shouldn't happen if extraction was successful
  throw new Error('No usable content format available');
}

/**
 * Convert content to Lexical format with fallback mechanisms
 */
async function convertToLexical(
  extractionResult: EnhancedExtractionResult,
  format: 'html' | 'markdown' | 'structured',
  options: Required<EnhancedProcessingOptions>
): Promise<{
  success: boolean;
  lexical?: object;
  method: string;
  errors: string[];
}> {
  const errors: string[] = [];

  try {
    // Handle structured data format
    if (format === 'structured' && extractionResult.formats.structured) {
      console.log(`📊 Converting structured data to Lexical`);
      // For structured data, we'll create a Lexical document from the structured content
      const structuredLexical = convertStructuredToLexical(
        extractionResult.formats.structured
      );
      if (structuredLexical) {
        return {
          success: true,
          lexical: structuredLexical,
          method: 'structured',
          errors: [],
        };
      }
      errors.push('Structured data conversion failed');
    }

    // Handle HTML format
    if (
      (format === 'html' || format === 'structured') &&
      extractionResult.formats.html
    ) {
      console.log(`🏗️ Converting HTML to Lexical`);
      try {
        const htmlResult = await htmlToLexical(extractionResult.formats.html);
        if (htmlResult.metrics.success) {
          return {
            success: true,
            lexical: htmlResult.result,
            method: 'html',
            errors: [],
          };
        }
        errors.push('HTML to Lexical conversion failed');
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'HTML conversion error';
        errors.push(`HTML conversion error: ${errorMessage}`);
      }
    }

    // Fallback to markdown conversion
    if (extractionResult.formats.markdown) {
      console.log(`📄 Falling back to Markdown conversion`);
      try {
        // Import markdown conversion utility
        const { markdownToLexical } = await import('../utils/lexical');
        const markdownResult = await markdownToLexical(
          extractionResult.formats.markdown
        );
        return {
          success: true,
          lexical: markdownResult,
          method: 'markdown',
          errors,
        };
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : 'Markdown conversion error';
        errors.push(`Markdown conversion error: ${errorMessage}`);
      }
    }

    // Final fallback to empty Lexical state
    console.log(`🔄 All conversions failed, using empty Lexical state`);
    return {
      success: true,
      lexical: createEmptyLexicalState(),
      method: 'fallback',
      errors,
    };
  } catch (error) {
    const errorMessage =
      error instanceof Error ? error.message : 'Conversion error';
    errors.push(`Conversion error: ${errorMessage}`);
    return { success: false, method: 'error', errors };
  }
}

/**
 * Convert structured data to Lexical format
 */
function convertStructuredToLexical(structuredData: any): object | null {
  try {
    // Create a basic Lexical document from structured data
    const children: any[] = [];

    // Add title if available
    if (structuredData.title) {
      children.push({
        type: 'heading',
        version: 1,
        tag: 'h1',
        direction: 'ltr',
        format: '',
        indent: 0,
        children: [
          {
            type: 'text',
            version: 1,
            text: structuredData.title,
            format: 0,
          },
        ],
      });
    }

    // Add summary if available
    if (structuredData.summary) {
      children.push({
        type: 'paragraph',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        textFormat: 0,
        children: [
          {
            type: 'text',
            version: 1,
            text: structuredData.summary,
            format: 1, // Bold
          },
        ],
      });
    }

    // Add main content
    if (structuredData.content) {
      // Split content into paragraphs
      const paragraphs = structuredData.content.split('\n\n');
      paragraphs.forEach((paragraph: string) => {
        if (paragraph.trim()) {
          children.push({
            type: 'paragraph',
            version: 1,
            direction: 'ltr',
            format: '',
            indent: 0,
            textFormat: 0,
            children: [
              {
                type: 'text',
                version: 1,
                text: paragraph.trim(),
                format: 0,
              },
            ],
          });
        }
      });
    }

    return {
      root: {
        type: 'root',
        version: 1,
        direction: 'ltr',
        format: '',
        indent: 0,
        children:
          children.length > 0
            ? children
            : [
                {
                  type: 'paragraph',
                  version: 1,
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  textFormat: 0,
                  children: [],
                },
              ],
      },
    };
  } catch (error) {
    console.error('Error converting structured data to Lexical:', error);
    return null;
  }
}

/**
 * Process and optimize titles using Sprint 4 utilities
 */
async function processTitles(
  extractionResult: EnhancedExtractionResult,
  options: Required<EnhancedProcessingOptions>
): Promise<{
  original: string;
  cleaned: string;
  english?: string;
  optimized: string;
  quality: { score: number; issues: string[]; recommendations: string[] };
}> {
  // Get original title from extraction result
  const originalTitle = extractionResult.metadata.title || 'Untitled Article';

  console.log(`📝 Processing title: "${originalTitle}"`);

  // Step 1: Clean source title
  const cleanedTitle = cleanSourceTitle(originalTitle);
  console.log(`🧹 Cleaned title: "${cleanedTitle}"`);

  // Step 2: Generate English title if requested
  let englishTitle: string | undefined;
  if (
    options.generateEnglishTitles &&
    extractionResult.metadata.hasGermanContent
  ) {
    englishTitle = cleanEnglishTitle(cleanedTitle);
    console.log(`🇬🇧 English title: "${englishTitle}"`);
  }

  // Step 3: Optimize for German financial content
  let optimizedTitle = cleanedTitle;
  if (
    extractionResult.metadata.isFinancialContent &&
    extractionResult.metadata.hasGermanContent
  ) {
    optimizedTitle = optimizeGermanFinancialTitle(cleanedTitle);
    console.log(`🇩🇪 Optimized German title: "${optimizedTitle}"`);
  }

  // Step 4: Validate title quality
  const qualityResult = validateTitleQuality(optimizedTitle);
  console.log(`⭐ Title quality score: ${qualityResult.score}/100`);

  return {
    original: originalTitle,
    cleaned: cleanedTitle,
    english: englishTitle,
    optimized: optimizedTitle,
    quality: {
      score: qualityResult.score,
      issues: qualityResult.issues.map(issue => issue.description),
      recommendations: qualityResult.recommendations,
    },
  };
}

/**
 * Assemble final processing result with all metadata
 */
function assembleProcessingResult(
  url: string,
  extractionResult: EnhancedExtractionResult,
  conversionResult: {
    success: boolean;
    lexical?: object;
    method: string;
    errors: string[];
  },
  titleResult: any,
  performance: any,
  startTime: number,
  memoryBefore: NodeJS.MemoryUsage
): EnhancedProcessingResult {
  const success = conversionResult.success && !!conversionResult.lexical;

  // Determine original format
  let originalFormat: 'html' | 'markdown' | 'structured' = 'html';
  if (conversionResult.method === 'structured') originalFormat = 'structured';
  else if (conversionResult.method === 'markdown') originalFormat = 'markdown';

  // Calculate content quality score
  const contentQuality = calculateContentQuality(
    extractionResult,
    conversionResult,
    titleResult
  );

  return {
    success,
    url,
    timestamp: new Date(),
    content: {
      lexical: conversionResult.lexical || createEmptyLexicalState(),
      originalFormat,
      fallbackUsed: conversionResult.method === 'fallback',
    },
    titles: titleResult,
    metadata: {
      extractionMethod: extractionResult.metadata.extractionMethod,
      conversionMethod: conversionResult.method as
        | 'html'
        | 'markdown'
        | 'fallback',
      processingTime: performance.totalTime,
      contentQuality,
      isFinancialContent: extractionResult.metadata.isFinancialContent,
      isGermanContent: extractionResult.metadata.hasGermanContent,
      wordCount: extractionResult.metadata.wordCount,
      readingTime: extractionResult.metadata.readingTime,
    },
    performance,
    warnings: extractionResult.warnings.concat(
      conversionResult.errors.filter(e => !e.includes('failed'))
    ),
    errors: success
      ? []
      : extractionResult.errors.concat(conversionResult.errors),
    fallbacks: {
      extractionFallback: extractionResult.fallbackUsed,
      conversionFallback: conversionResult.method === 'fallback',
      titleFallback: titleResult.quality.score < 50,
    },
  };
}

/**
 * Calculate overall content quality score
 */
function calculateContentQuality(
  extractionResult: EnhancedExtractionResult,
  conversionResult: any,
  titleResult: any
): number {
  let score = 50; // Base score

  // Extraction quality contribution (40%)
  score += (extractionResult.metadata.qualityScore - 50) * 0.4;

  // Title quality contribution (30%)
  score += (titleResult.quality.score - 50) * 0.3;

  // Conversion success contribution (30%)
  if (conversionResult.success) {
    if (conversionResult.method === 'html') score += 15;
    else if (conversionResult.method === 'structured') score += 20;
    else if (conversionResult.method === 'markdown') score += 10;
    else score += 5; // fallback
  }

  // Bonus for financial content
  if (extractionResult.metadata.isFinancialContent) score += 5;
  if (extractionResult.metadata.hasGermanContent) score += 5;

  return Math.max(0, Math.min(100, Math.round(score)));
}

/**
 * Cache management functions
 */
function getCachedResult(url: string): EnhancedProcessingResult | null {
  const cached = processingCache[url];
  if (!cached) return null;

  // Check if cache entry is still valid
  if (new Date() > cached.expiresAt) {
    delete processingCache[url];
    return null;
  }

  return cached.result;
}

function cacheResult(url: string, result: EnhancedProcessingResult): void {
  const now = new Date();
  processingCache[url] = {
    result,
    timestamp: now,
    expiresAt: new Date(now.getTime() + CACHE_DURATION),
  };

  // Clean up old cache entries periodically
  cleanupCache();
}

function cleanupCache(): void {
  const now = new Date();
  Object.keys(processingCache).forEach(url => {
    if (now > processingCache[url].expiresAt) {
      delete processingCache[url];
    }
  });
}

/**
 * Create error result for failed processing
 */
function createErrorResult(
  url: string,
  error: string,
  errors: string[],
  startTime: number,
  memoryBefore: NodeJS.MemoryUsage
): EnhancedProcessingResult {
  const memoryAfter = process.memoryUsage();

  return {
    success: false,
    url,
    timestamp: new Date(),
    content: {
      lexical: createEmptyLexicalState(),
      originalFormat: 'html',
      fallbackUsed: true,
    },
    titles: {
      original: '',
      cleaned: '',
      optimized: '',
      quality: {
        score: 0,
        issues: ['Processing failed'],
        recommendations: ['Retry processing or check source URL'],
      },
    },
    metadata: {
      extractionMethod: 'standard',
      conversionMethod: 'fallback',
      processingTime: Date.now() - startTime,
      contentQuality: 0,
      isFinancialContent: false,
      isGermanContent: false,
      wordCount: 0,
      readingTime: 0,
    },
    performance: {
      extractionTime: 0,
      conversionTime: 0,
      titleProcessingTime: 0,
      totalTime: Date.now() - startTime,
      memoryUsage: memoryAfter.heapUsed - memoryBefore.heapUsed,
      cacheHit: false,
    },
    warnings: [],
    errors: [error, ...errors],
    fallbacks: {
      extractionFallback: true,
      conversionFallback: true,
      titleFallback: true,
    },
  };
}

/**
 * Clear processing cache (useful for testing or memory management)
 */
export function clearProcessingCache(): void {
  Object.keys(processingCache).forEach(key => {
    delete processingCache[key];
  });
  console.log('🧹 Processing cache cleared');
}

/**
 * Get cache statistics for monitoring
 */
export function getCacheStats(): {
  size: number;
  entries: string[];
  oldestEntry?: Date;
  newestEntry?: Date;
} {
  const entries = Object.keys(processingCache);
  const timestamps = entries.map(url => processingCache[url].timestamp);

  return {
    size: entries.length,
    entries,
    oldestEntry:
      timestamps.length > 0
        ? new Date(Math.min(...timestamps.map(d => d.getTime())))
        : undefined,
    newestEntry:
      timestamps.length > 0
        ? new Date(Math.max(...timestamps.map(d => d.getTime())))
        : undefined,
  };
}
