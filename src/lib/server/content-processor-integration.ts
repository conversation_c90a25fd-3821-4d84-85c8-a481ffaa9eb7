/**
 * Content Processor Integration Service
 *
 * Integrates HTML-to-Lexical converter with existing content processing pipeline.
 * Replaces markdown conversion with superior HTML conversion for better semantic preservation.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

import {
  htmlToLexical,
  validateHTMLForConversion,
} from '../utils/html-to-lexical';
import { markdownToLexical } from '../utils/lexical';
import {
  validateLexicalData,
  normalizeLexicalData,
} from '../utils/lexical-validation';

/**
 * Content processing strategy based on input format and quality
 */
export type ProcessingStrategy =
  | 'html-direct'
  | 'html-fallback'
  | 'markdown'
  | 'text-only';

/**
 * Content processing result with metrics and metadata
 */
export interface ContentProcessingResult {
  lexicalContent: object;
  strategy: ProcessingStrategy;
  metrics: {
    processingTime: number;
    inputSize: number;
    outputSize: number;
    qualityScore: number;
    success: boolean;
    fallbackUsed: boolean;
  };
  metadata: {
    hasStructuredContent: boolean;
    isFinancialContent: boolean;
    hasGermanContent: boolean;
    complexity: 'low' | 'medium' | 'high';
    recommendedFormat: 'html' | 'markdown' | 'text';
  };
  warnings: string[];
  errors: string[];
}

/**
 * Enhanced content processor that intelligently selects optimal conversion strategy
 * Prioritizes HTML-to-Lexical for superior semantic structure preservation
 */
export class ContentProcessorIntegration {
  private static instance: ContentProcessorIntegration;
  private conversionStats: Map<
    ProcessingStrategy,
    { count: number; avgTime: number; successRate: number }
  >;

  private constructor() {
    this.conversionStats = new Map();
    this.initializeStats();
  }

  /**
   * Get singleton instance
   */
  public static getInstance(): ContentProcessorIntegration {
    if (!ContentProcessorIntegration.instance) {
      ContentProcessorIntegration.instance = new ContentProcessorIntegration();
    }
    return ContentProcessorIntegration.instance;
  }

  /**
   * Process content using optimal strategy based on input analysis
   */
  public async processContent(
    content: string,
    sourceFormat: 'html' | 'markdown' | 'text' = 'html',
    options: {
      forceStrategy?: ProcessingStrategy;
      enableFallback?: boolean;
      maxProcessingTime?: number;
    } = {}
  ): Promise<ContentProcessingResult> {
    const startTime = Date.now();
    const inputSize = content.length;
    const warnings: string[] = [];
    const errors: string[] = [];

    // Default options
    const {
      forceStrategy,
      enableFallback = true,
      maxProcessingTime = 10000, // 10 seconds
    } = options;

    try {
      // Analyze content to determine optimal strategy
      const strategy =
        forceStrategy || this.determineOptimalStrategy(content, sourceFormat);

      console.log(`🔄 Processing content using strategy: ${strategy}`, {
        inputSize,
        sourceFormat,
        forceStrategy: !!forceStrategy,
      });

      // Process content based on strategy
      const result = await this.executeStrategy(
        strategy,
        content,
        enableFallback,
        maxProcessingTime
      );

      // Validate and normalize the result
      const validation = validateLexicalData(result.lexicalContent);
      if (!validation.isValid) {
        warnings.push('Content validation failed, normalizing data');
        result.lexicalContent = validation.normalized || result.lexicalContent;
      }

      // Calculate metrics
      const endTime = Date.now();
      const processingTime = endTime - startTime;
      const outputSize = JSON.stringify(result.lexicalContent).length;

      // Analyze content metadata
      const htmlAnalysis =
        sourceFormat === 'html'
          ? validateHTMLForConversion(content)
          : {
              hasStructuredContent: false,
              isFinancialContent: false,
              details: { hasGermanContent: false, complexity: 'low' as const },
              recommendedFormat: sourceFormat as 'markdown' | 'text',
              score: 50,
            };

      // Update statistics
      this.updateStats(strategy, processingTime, result.success);

      const finalResult: ContentProcessingResult = {
        lexicalContent: result.lexicalContent,
        strategy: result.actualStrategy,
        metrics: {
          processingTime,
          inputSize,
          outputSize,
          qualityScore: htmlAnalysis.score,
          success: result.success,
          fallbackUsed: result.fallbackUsed,
        },
        metadata: {
          hasStructuredContent: htmlAnalysis.hasStructuredContent,
          isFinancialContent: htmlAnalysis.isFinancialContent,
          hasGermanContent: htmlAnalysis.details.hasGermanContent,
          complexity: htmlAnalysis.details.complexity,
          recommendedFormat: htmlAnalysis.recommendedFormat,
        },
        warnings: [...warnings, ...result.warnings],
        errors: [...errors, ...result.errors],
      };

      console.log(`✅ Content processing completed`, {
        strategy: finalResult.strategy,
        processingTime,
        qualityScore: finalResult.metrics.qualityScore,
        success: finalResult.metrics.success,
      });

      return finalResult;
    } catch (error) {
      const processingTime = Date.now() - startTime;
      errors.push(
        `Content processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );

      console.error('❌ Content processing failed:', error);

      // Return fallback result
      return {
        lexicalContent: normalizeLexicalData(null),
        strategy: 'text-only',
        metrics: {
          processingTime,
          inputSize,
          outputSize: 0,
          qualityScore: 0,
          success: false,
          fallbackUsed: true,
        },
        metadata: {
          hasStructuredContent: false,
          isFinancialContent: false,
          hasGermanContent: false,
          complexity: 'low',
          recommendedFormat: 'text',
        },
        warnings,
        errors,
      };
    }
  }

  /**
   * Determine optimal processing strategy based on content analysis
   */
  private determineOptimalStrategy(
    content: string,
    sourceFormat: 'html' | 'markdown' | 'text'
  ): ProcessingStrategy {
    if (!content || content.trim().length === 0) {
      return 'text-only';
    }

    // For HTML content, analyze quality and complexity
    if (sourceFormat === 'html') {
      const analysis = validateHTMLForConversion(content);

      // Use HTML-direct for high-quality structured content
      if (analysis.score >= 70 && analysis.hasStructuredContent) {
        return 'html-direct';
      }

      // Use HTML with fallback for medium quality content
      if (analysis.score >= 40) {
        return 'html-fallback';
      }
    }

    // For markdown or lower quality HTML, use markdown strategy
    if (
      sourceFormat === 'markdown' ||
      content.includes('# ') ||
      content.includes('## ')
    ) {
      return 'markdown';
    }

    // Default to text-only for simple content
    return 'text-only';
  }

  /**
   * Execute the selected processing strategy
   */
  private async executeStrategy(
    strategy: ProcessingStrategy,
    content: string,
    enableFallback: boolean,
    maxProcessingTime: number
  ): Promise<{
    lexicalContent: object;
    actualStrategy: ProcessingStrategy;
    success: boolean;
    fallbackUsed: boolean;
    warnings: string[];
    errors: string[];
  }> {
    const warnings: string[] = [];
    const errors: string[] = [];

    // Set timeout for processing
    const timeoutPromise = new Promise((_, reject) => {
      setTimeout(
        () => reject(new Error('Processing timeout')),
        maxProcessingTime
      );
    });

    try {
      switch (strategy) {
        case 'html-direct':
        case 'html-fallback': {
          const conversionPromise = htmlToLexical(content);
          const { result, metrics } = (await Promise.race([
            conversionPromise,
            timeoutPromise,
          ])) as any;

          if (metrics.success && !metrics.fallbackUsed) {
            return {
              lexicalContent: result,
              actualStrategy: 'html-direct',
              success: true,
              fallbackUsed: false,
              warnings,
              errors,
            };
          }

          if (metrics.success && metrics.fallbackUsed) {
            warnings.push('HTML conversion used markdown fallback');
            return {
              lexicalContent: result,
              actualStrategy: 'html-fallback',
              success: true,
              fallbackUsed: true,
              warnings,
              errors,
            };
          }

          // If HTML conversion failed and fallback is enabled, try markdown
          if (enableFallback) {
            warnings.push(
              'HTML conversion failed, attempting markdown fallback'
            );
            const markdownResult = await markdownToLexical(content);
            return {
              lexicalContent: markdownResult,
              actualStrategy: 'markdown',
              success: true,
              fallbackUsed: true,
              warnings,
              errors,
            };
          }

          throw new Error('HTML conversion failed and fallback disabled');
        }

        case 'markdown': {
          const markdownPromise = markdownToLexical(content);
          const result = await Promise.race([markdownPromise, timeoutPromise]);

          return {
            lexicalContent: result as object,
            actualStrategy: 'markdown',
            success: true,
            fallbackUsed: false,
            warnings,
            errors,
          };
        }

        case 'text-only': {
          // Create simple text-only Lexical content
          const textLexical = {
            root: {
              type: 'root',
              version: 1,
              direction: 'ltr',
              format: '',
              indent: 0,
              children: [
                {
                  type: 'paragraph',
                  version: 1,
                  direction: 'ltr',
                  format: '',
                  indent: 0,
                  textFormat: 0,
                  children: [
                    {
                      type: 'text',
                      version: 1,
                      text: content.trim(),
                      format: 0,
                    },
                  ],
                },
              ],
            },
          };

          return {
            lexicalContent: textLexical,
            actualStrategy: 'text-only',
            success: true,
            fallbackUsed: false,
            warnings,
            errors,
          };
        }

        default:
          throw new Error(`Unknown processing strategy: ${strategy}`);
      }
    } catch (error) {
      errors.push(
        `Strategy execution failed: ${error instanceof Error ? error.message : 'Unknown error'}`
      );

      // Final fallback to text-only if enabled
      if (enableFallback && strategy !== 'text-only') {
        warnings.push('All strategies failed, using text-only fallback');
        return this.executeStrategy(
          'text-only',
          content,
          false,
          maxProcessingTime
        );
      }

      throw error;
    }
  }

  /**
   * Initialize conversion statistics
   */
  private initializeStats(): void {
    const strategies: ProcessingStrategy[] = [
      'html-direct',
      'html-fallback',
      'markdown',
      'text-only',
    ];
    strategies.forEach(strategy => {
      this.conversionStats.set(strategy, {
        count: 0,
        avgTime: 0,
        successRate: 1.0,
      });
    });
  }

  /**
   * Update conversion statistics
   */
  private updateStats(
    strategy: ProcessingStrategy,
    processingTime: number,
    success: boolean
  ): void {
    const stats = this.conversionStats.get(strategy);
    if (!stats) return;

    const newCount = stats.count + 1;
    const newAvgTime =
      (stats.avgTime * stats.count + processingTime) / newCount;
    const newSuccessRate =
      (stats.successRate * stats.count + (success ? 1 : 0)) / newCount;

    this.conversionStats.set(strategy, {
      count: newCount,
      avgTime: newAvgTime,
      successRate: newSuccessRate,
    });
  }

  /**
   * Get conversion statistics for monitoring
   */
  public getStats(): Record<
    ProcessingStrategy,
    { count: number; avgTime: number; successRate: number }
  > {
    const result: any = {};
    this.conversionStats.forEach((stats, strategy) => {
      result[strategy] = { ...stats };
    });
    return result;
  }

  /**
   * Reset statistics (useful for testing)
   */
  public resetStats(): void {
    this.initializeStats();
  }
}
