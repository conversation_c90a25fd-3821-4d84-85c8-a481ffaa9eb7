/**
 * Enhanced Content Processor Integration Tests
 *
 * Comprehensive test suite for the unified enhanced content processing pipeline
 * that integrates all previous sprint components for superior content quality.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 5: Content Processor Integration
 */

import { describe, test, expect, beforeEach, afterEach } from '@jest/globals';
import {
  processContentEnhanced,
  clearProcessingCache,
  getCacheStats,
  EnhancedProcessingResult,
  type EnhancedProcessingOptions,
} from '../enhanced-content-processor';

// Test URLs for German financial sites
const TEST_URLS = {
  finanzenNet:
    'https://www.finanzen.net/nachricht/aktien/dax-steigt-heute-um-2-prozent-12345678',
  handelsblatt:
    'https://www.handelsblatt.com/finanzen/maerkte/aktien/dax-analyse-neue-hoechststaende-12345678.html',
  wallstreetOnline:
    'https://www.wallstreet-online.de/nachricht/dax-prognose-positive-entwicklung-12345678',
  boerse:
    'https://www.boerse.de/nachrichten/DAX-Analyse-Quartalszahlen-12345678',
  managerMagazin:
    'https://www.manager-magazin.de/finanzen/boerse/dax-entwicklung-rekordstand-12345678',
  unknown: 'https://example.com/test-financial-article',
  invalid: 'not-a-valid-url',
  nonExistent: 'https://nonexistent-domain-12345.com/article',
};

// Mock data for testing
const MOCK_EXTRACTION_RESULT = {
  success: true,
  url: TEST_URLS.finanzenNet,
  timestamp: new Date(),
  formats: {
    html: '<h1>DAX steigt um 2,5%</h1><p>Der Deutsche Aktienindex erreicht neue Höchststände...</p>',
    markdown:
      '# DAX steigt um 2,5%\n\nDer Deutsche Aktienindex erreicht neue Höchststände...',
  },
  metadata: {
    title: '🚀 DAX steigt um 2,5% - Aktien im Höhenflug! ⭐',
    wordCount: 150,
    readingTime: 1,
    isFinancialContent: true,
    hasGermanContent: true,
    qualityScore: 85,
    extractionMethod: 'standard' as const,
    processingTime: 1000,
    retryCount: 0,
  },
  performance: {
    totalTime: 1000,
    firecrawlTime: 800,
    processingTime: 200,
    memoryUsage: 1024 * 1024,
  },
  warnings: [],
  errors: [],
  fallbackUsed: false,
};

describe('Enhanced Content Processor', () => {
  beforeEach(() => {
    // Clear cache before each test
    clearProcessingCache();
  });

  afterEach(() => {
    // Clean up after each test
    clearProcessingCache();
  });

  describe('Basic Processing', () => {
    // Skip actual API tests if no API key is available
    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should process German financial content successfully',
      async () => {
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);

        expect(result).toBeTruthy();
        expect(result.success).toBe(true);
        expect(result.url).toBe(TEST_URLS.finanzenNet);
        expect(result.timestamp).toBeInstanceOf(Date);

        // Check content structure
        expect(result.content).toBeTruthy();
        expect(result.content.lexical).toBeTruthy();
        expect(['html', 'markdown', 'structured']).toContain(
          result.content.originalFormat
        );

        // Check title processing
        expect(result.titles).toBeTruthy();
        expect(result.titles.original).toBeTruthy();
        expect(result.titles.cleaned).toBeTruthy();
        expect(result.titles.optimized).toBeTruthy();
        expect(result.titles.quality.score).toBeGreaterThanOrEqual(0);
        expect(result.titles.quality.score).toBeLessThanOrEqual(100);

        // Check metadata
        expect(result.metadata).toBeTruthy();
        expect(['standard', 'structured', 'stealth']).toContain(
          result.metadata.extractionMethod
        );
        expect(['html', 'markdown', 'fallback']).toContain(
          result.metadata.conversionMethod
        );
        expect(typeof result.metadata.contentQuality).toBe('number');
        expect(typeof result.metadata.isFinancialContent).toBe('boolean');
        expect(typeof result.metadata.isGermanContent).toBe('boolean');

        // Check performance metrics
        expect(result.performance).toBeTruthy();
        expect(typeof result.performance.totalTime).toBe('number');
        expect(typeof result.performance.extractionTime).toBe('number');
        expect(typeof result.performance.conversionTime).toBe('number');
        expect(typeof result.performance.titleProcessingTime).toBe('number');
        expect(typeof result.performance.memoryUsage).toBe('number');
        expect(typeof result.performance.cacheHit).toBe('boolean');

        // Check arrays
        expect(Array.isArray(result.warnings)).toBe(true);
        expect(Array.isArray(result.errors)).toBe(true);

        // Check fallback information
        expect(result.fallbacks).toBeTruthy();
        expect(typeof result.fallbacks.extractionFallback).toBe('boolean');
        expect(typeof result.fallbacks.conversionFallback).toBe('boolean');
        expect(typeof result.fallbacks.titleFallback).toBe('boolean');
      },
      30000
    ); // 30 second timeout

    skipIfNoApiKey(
      'should handle stealth mode for protected sites',
      async () => {
        const options: EnhancedProcessingOptions = {
          enableStealthMode: true,
          preferredFormat: 'html',
        };

        const result = await processContentEnhanced(
          TEST_URLS.handelsblatt,
          options
        );

        expect(result).toBeTruthy();
        expect(result.url).toBe(TEST_URLS.handelsblatt);

        if (result.success) {
          expect(['stealth', 'structured']).toContain(
            result.metadata.extractionMethod
          );
        }
      },
      45000
    ); // Longer timeout for stealth mode

    skipIfNoApiKey(
      'should process with structured data extraction',
      async () => {
        const options: EnhancedProcessingOptions = {
          enableStructuredExtraction: true,
          preferredFormat: 'auto',
        };

        const result = await processContentEnhanced(
          TEST_URLS.finanzenNet,
          options
        );

        expect(result).toBeTruthy();

        if (result.success && result.content.originalFormat === 'structured') {
          expect(result.metadata.extractionMethod).toBe('structured');
          expect(result.metadata.contentQuality).toBeGreaterThan(70);
        }
      },
      30000
    );

    test('should handle invalid URLs gracefully', async () => {
      const result = await processContentEnhanced(TEST_URLS.invalid);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.content.fallbackUsed).toBe(true);
      expect(result.fallbacks.extractionFallback).toBe(true);
    });

    test('should handle non-existent domains', async () => {
      const result = await processContentEnhanced(TEST_URLS.nonExistent);

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.performance.totalTime).toBeGreaterThan(0);
    });
  });

  describe('Processing Options', () => {
    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should respect format preferences',
      async () => {
        const htmlOptions: EnhancedProcessingOptions = {
          preferredFormat: 'html',
        };

        const markdownOptions: EnhancedProcessingOptions = {
          preferredFormat: 'markdown',
        };

        const htmlResult = await processContentEnhanced(
          TEST_URLS.finanzenNet,
          htmlOptions
        );
        const markdownResult = await processContentEnhanced(
          TEST_URLS.finanzenNet,
          markdownOptions
        );

        if (htmlResult.success && markdownResult.success) {
          // Results might be different based on format preference
          expect(htmlResult.content.originalFormat).toBeDefined();
          expect(markdownResult.content.originalFormat).toBeDefined();
        }
      },
      60000
    );

    skipIfNoApiKey(
      'should handle title optimization options',
      async () => {
        const withOptimization: EnhancedProcessingOptions = {
          optimizeTitles: true,
          generateEnglishTitles: true,
        };

        const withoutOptimization: EnhancedProcessingOptions = {
          optimizeTitles: false,
          generateEnglishTitles: false,
        };

        const optimizedResult = await processContentEnhanced(
          TEST_URLS.finanzenNet,
          withOptimization
        );
        const basicResult = await processContentEnhanced(
          TEST_URLS.finanzenNet,
          withoutOptimization
        );

        if (optimizedResult.success && basicResult.success) {
          // Optimized result should have better title quality
          expect(optimizedResult.titles.quality.score).toBeGreaterThanOrEqual(
            basicResult.titles.quality.score
          );
        }
      },
      60000
    );

    test('should handle processing timeout', async () => {
      const options: EnhancedProcessingOptions = {
        maxProcessingTime: 1000, // Very short timeout
      };

      const result = await processContentEnhanced(
        TEST_URLS.finanzenNet,
        options
      );

      // Should either succeed quickly or fail with timeout
      expect(result).toBeTruthy();
      if (!result.success) {
        expect(
          result.errors.some(
            error => error.includes('timeout') || error.includes('time')
          )
        ).toBe(true);
      }
    });
  });

  describe('Caching System', () => {
    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should cache successful results',
      async () => {
        const options: EnhancedProcessingOptions = {
          enableCaching: true,
        };

        // First request
        const firstResult = await processContentEnhanced(
          TEST_URLS.finanzenNet,
          options
        );
        expect(firstResult.performance.cacheHit).toBe(false);

        // Second request should hit cache
        const secondResult = await processContentEnhanced(
          TEST_URLS.finanzenNet,
          options
        );
        expect(secondResult.performance.cacheHit).toBe(true);

        // Results should be identical except for cache hit flag
        expect(secondResult.url).toBe(firstResult.url);
        expect(secondResult.success).toBe(firstResult.success);
      },
      45000
    );

    test('should respect cache disable option', async () => {
      const options: EnhancedProcessingOptions = {
        enableCaching: false,
      };

      // Both requests should not use cache
      const firstResult = await processContentEnhanced(
        TEST_URLS.finanzenNet,
        options
      );
      const secondResult = await processContentEnhanced(
        TEST_URLS.finanzenNet,
        options
      );

      expect(firstResult.performance.cacheHit).toBe(false);
      expect(secondResult.performance.cacheHit).toBe(false);
    });

    test('should provide cache statistics', () => {
      const initialStats = getCacheStats();
      expect(initialStats.size).toBe(0);
      expect(initialStats.entries).toEqual([]);
      expect(initialStats.oldestEntry).toBeUndefined();
      expect(initialStats.newestEntry).toBeUndefined();
    });

    test('should clear cache correctly', () => {
      // Cache should be empty after clearing
      clearProcessingCache();
      const stats = getCacheStats();
      expect(stats.size).toBe(0);
    });
  });

  describe('Error Handling and Fallbacks', () => {
    test('should handle missing API key gracefully', async () => {
      // Temporarily remove API key
      const originalApiKey = process.env.FIRECRAWL_API_KEY;
      delete process.env.FIRECRAWL_API_KEY;

      const result = await processContentEnhanced(TEST_URLS.finanzenNet);

      expect(result.success).toBe(false);
      expect(
        result.errors.some(
          error => error.includes('API key') || error.includes('configured')
        )
      ).toBe(true);
      expect(result.fallbacks.extractionFallback).toBe(true);

      // Restore API key
      if (originalApiKey) {
        process.env.FIRECRAWL_API_KEY = originalApiKey;
      }
    });

    test('should provide meaningful error messages', async () => {
      const result = await processContentEnhanced('invalid-url');

      expect(result.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.errors[0]).toBeTruthy();
      expect(typeof result.errors[0]).toBe('string');
    });

    test('should handle fallback scenarios', async () => {
      const result = await processContentEnhanced(TEST_URLS.invalid);

      expect(result.fallbacks).toBeTruthy();
      expect(result.fallbacks.extractionFallback).toBe(true);
      expect(result.fallbacks.conversionFallback).toBe(true);
      expect(result.content.fallbackUsed).toBe(true);
    });
  });

  describe('Performance Benchmarks', () => {
    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should complete processing within target time',
      async () => {
        const startTime = Date.now();
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);
        const endTime = Date.now();

        const totalTime = endTime - startTime;

        // Should complete within 15 seconds (target is < 10 seconds)
        expect(totalTime).toBeLessThan(15000);

        if (result.success) {
          expect(result.performance.totalTime).toBeLessThan(15000);
          expect(result.performance.totalTime).toBeGreaterThan(0);
        }
      },
      20000
    );

    skipIfNoApiKey(
      'should provide detailed performance metrics',
      async () => {
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);

        if (result.success) {
          expect(result.performance.extractionTime).toBeGreaterThan(0);
          expect(result.performance.conversionTime).toBeGreaterThan(0);
          expect(result.performance.titleProcessingTime).toBeGreaterThan(0);
          expect(result.performance.totalTime).toBeGreaterThan(0);
          expect(typeof result.performance.memoryUsage).toBe('number');

          // Total time should be sum of individual times (approximately)
          const sumOfParts =
            result.performance.extractionTime +
            result.performance.conversionTime +
            result.performance.titleProcessingTime;
          expect(result.performance.totalTime).toBeGreaterThanOrEqual(
            sumOfParts * 0.8
          );
        }
      },
      30000
    );

    skipIfNoApiKey(
      'should maintain reasonable memory usage',
      async () => {
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);

        if (result.success) {
          // Memory usage should be reasonable (less than 50MB for single article)
          expect(result.performance.memoryUsage).toBeLessThan(50 * 1024 * 1024);
        }
      },
      30000
    );
  });

  describe('Content Quality', () => {
    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should provide quality metrics',
      async () => {
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);

        if (result.success) {
          expect(typeof result.metadata.contentQuality).toBe('number');
          expect(result.metadata.contentQuality).toBeGreaterThanOrEqual(0);
          expect(result.metadata.contentQuality).toBeLessThanOrEqual(100);

          expect(typeof result.metadata.isFinancialContent).toBe('boolean');
          expect(typeof result.metadata.isGermanContent).toBe('boolean');
          expect(typeof result.metadata.wordCount).toBe('number');
          expect(typeof result.metadata.readingTime).toBe('number');
        }
      },
      30000
    );

    skipIfNoApiKey(
      'should detect German financial content',
      async () => {
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);

        if (result.success) {
          // finanzen.net should be detected as German financial content
          expect(result.metadata.isFinancialContent).toBe(true);
          expect(result.metadata.isGermanContent).toBe(true);
        }
      },
      30000
    );

    skipIfNoApiKey(
      'should optimize titles for German financial content',
      async () => {
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);

        if (
          result.success &&
          result.metadata.isFinancialContent &&
          result.metadata.isGermanContent
        ) {
          expect(result.titles.optimized).toBeTruthy();
          expect(result.titles.optimized.length).toBeGreaterThan(0);
          expect(result.titles.quality.score).toBeGreaterThan(0);

          // Optimized title should be different from original if original had issues
          if (result.titles.quality.score < 80) {
            expect(result.titles.optimized).not.toBe(result.titles.original);
          }
        }
      },
      30000
    );
  });

  describe('Integration with Previous Sprints', () => {
    test('should integrate all sprint components', () => {
      // This test verifies that all imports are working correctly
      expect(processContentEnhanced).toBeDefined();
      expect(clearProcessingCache).toBeDefined();
      expect(getCacheStats).toBeDefined();
    });

    const skipIfNoApiKey = process.env.FIRECRAWL_API_KEY ? test : test.skip;

    skipIfNoApiKey(
      'should demonstrate end-to-end improvement',
      async () => {
        const result = await processContentEnhanced(TEST_URLS.finanzenNet);

        if (result.success) {
          // Should demonstrate improvements from all sprints:
          // Sprint 1: Proper Lexical validation
          expect(result.content.lexical).toBeTruthy();

          // Sprint 2: HTML-to-Lexical conversion
          expect(['html', 'markdown', 'structured']).toContain(
            result.content.originalFormat
          );

          // Sprint 3: Enhanced Firecrawl extraction
          expect(['standard', 'structured', 'stealth']).toContain(
            result.metadata.extractionMethod
          );

          // Sprint 4: Title optimization
          expect(result.titles.optimized).toBeTruthy();
          expect(result.titles.quality.score).toBeGreaterThanOrEqual(0);

          // Sprint 5: Integration and performance
          expect(result.performance.totalTime).toBeGreaterThan(0);
          expect(result.metadata.contentQuality).toBeGreaterThan(0);
        }
      },
      30000
    );
  });
});
