/**
 * Test Suite for Content Processor Integration Service
 *
 * Comprehensive tests for the integrated HTML-to-Lexical conversion pipeline.
 * Tests strategy selection, fallback mechanisms, and performance monitoring.
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 */

import { describe, test, expect, vi, beforeEach } from 'vitest';
import { ContentProcessorIntegration } from '../content-processor-integration';
import fs from 'fs';
import path from 'path';

// Mock dependencies
vi.mock('../../utils/html-to-lexical', () => ({
  htmlToLexical: vi.fn(),
  validateHTMLForConversion: vi.fn(),
}));

vi.mock('../../utils/lexical', () => ({
  markdownToLexical: vi.fn(),
}));

vi.mock('../../utils/lexical-validation', () => ({
  validateLexicalData: vi.fn(),
  normalizeLexicalData: vi.fn(),
}));

describe('Content Processor Integration Service', () => {
  let processor: ContentProcessorIntegration;
  let mockHtmlToLexical: jest.MockedFunction<any>;
  let mockValidateHTMLForConversion: jest.MockedFunction<any>;
  let mockMarkdownToLexical: jest.MockedFunction<any>;
  let mockValidateLexicalData: jest.MockedFunction<any>;
  let mockNormalizeLexicalData: jest.MockedFunction<any>;

  beforeEach(() => {
    // Get fresh instance for each test
    processor = ContentProcessorIntegration.getInstance();
    processor.resetStats();

    // Setup mocks
    mockHtmlToLexical = require('../../utils/html-to-lexical').htmlToLexical;
    mockValidateHTMLForConversion =
      require('../../utils/html-to-lexical').validateHTMLForConversion;
    mockMarkdownToLexical = require('../../utils/lexical').markdownToLexical;
    mockValidateLexicalData =
      require('../../utils/lexical-validation').validateLexicalData;
    mockNormalizeLexicalData =
      require('../../utils/lexical-validation').normalizeLexicalData;

    // Default mock implementations
    mockHtmlToLexical.mockResolvedValue({
      result: {
        root: {
          type: 'root',
          version: 1,
          children: [
            {
              type: 'paragraph',
              version: 1,
              children: [
                {
                  type: 'text',
                  version: 1,
                  text: 'Converted content',
                  format: 0,
                },
              ],
            },
          ],
        },
      },
      metrics: {
        success: true,
        fallbackUsed: false,
        duration: 100,
      },
    });

    mockValidateHTMLForConversion.mockReturnValue({
      score: 80,
      hasStructuredContent: true,
      isFinancialContent: true,
      details: {
        hasGermanContent: true,
        complexity: 'medium',
      },
      recommendedFormat: 'html',
    });

    mockMarkdownToLexical.mockResolvedValue({
      root: {
        type: 'root',
        version: 1,
        children: [],
      },
    });

    mockValidateLexicalData.mockReturnValue({
      isValid: true,
      errors: [],
    });

    mockNormalizeLexicalData.mockReturnValue({
      root: {
        type: 'root',
        version: 1,
        children: [],
      },
    });
  });

  describe('Strategy Selection', () => {
    it('should select html-direct for high-quality structured content', async () => {
      mockValidateHTMLForConversion.mockReturnValue({
        score: 85,
        hasStructuredContent: true,
        isFinancialContent: true,
        details: { hasGermanContent: true, complexity: 'high' },
        recommendedFormat: 'html',
      });

      const result = await processor.processContent(
        '<h1>High Quality Content</h1><p>With structure</p>',
        'html'
      );

      expect(result.strategy).toBe('html-direct');
      expect(result.metrics.success).toBe(true);
      expect(result.metadata.hasStructuredContent).toBe(true);
    });

    it('should select html-fallback for medium quality content', async () => {
      mockValidateHTMLForConversion.mockReturnValue({
        score: 50,
        hasStructuredContent: false,
        isFinancialContent: false,
        details: { hasGermanContent: false, complexity: 'medium' },
        recommendedFormat: 'html',
      });

      const result = await processor.processContent(
        '<p>Medium quality content</p>',
        'html'
      );

      expect(result.strategy).toBe('html-fallback');
    });

    it('should select markdown for markdown content', async () => {
      const result = await processor.processContent(
        '# Markdown Title\n\nSome content',
        'markdown'
      );

      expect(result.strategy).toBe('markdown');
      expect(mockMarkdownToLexical).toHaveBeenCalled();
    });

    it('should select text-only for simple content', async () => {
      const result = await processor.processContent(
        'Simple text content',
        'text'
      );

      expect(result.strategy).toBe('text-only');
      expect(result.lexicalContent.root.children[0].children[0].text).toBe(
        'Simple text content'
      );
    });
  });

  describe('Fallback Mechanisms', () => {
    it('should fallback to markdown when HTML conversion fails', async () => {
      mockHtmlToLexical.mockResolvedValue({
        result: null,
        metrics: {
          success: false,
          fallbackUsed: false,
          duration: 100,
        },
      });

      const result = await processor.processContent(
        '<p>Content that fails HTML conversion</p>',
        'html'
      );

      expect(result.strategy).toBe('markdown');
      expect(result.metrics.fallbackUsed).toBe(true);
      expect(result.warnings).toContain(
        'HTML conversion failed, attempting markdown fallback'
      );
    });

    it('should use HTML fallback when PayloadCMS conversion uses markdown internally', async () => {
      mockHtmlToLexical.mockResolvedValue({
        result: { root: { type: 'root', children: [] } },
        metrics: {
          success: true,
          fallbackUsed: true, // PayloadCMS used markdown internally
          duration: 150,
        },
      });

      const result = await processor.processContent(
        '<p>Content with internal fallback</p>',
        'html'
      );

      expect(result.strategy).toBe('html-fallback');
      expect(result.metrics.fallbackUsed).toBe(true);
    });

    it('should disable fallback when requested', async () => {
      mockHtmlToLexical.mockResolvedValue({
        result: null,
        metrics: {
          success: false,
          fallbackUsed: false,
          duration: 100,
        },
      });

      const result = await processor.processContent('<p>Content</p>', 'html', {
        enableFallback: false,
      });

      expect(result.metrics.success).toBe(false);
      expect(result.strategy).toBe('text-only'); // Final fallback
    });
  });

  describe('Performance Monitoring', () => {
    it('should track processing metrics', async () => {
      const result = await processor.processContent(
        '<p>Test content</p>',
        'html'
      );

      expect(result.metrics).toHaveProperty('processingTime');
      expect(result.metrics).toHaveProperty('inputSize');
      expect(result.metrics).toHaveProperty('outputSize');
      expect(result.metrics).toHaveProperty('qualityScore');
      expect(result.metrics.processingTime).toBeGreaterThan(0);
    });

    it('should provide conversion statistics', async () => {
      // Process multiple items
      await processor.processContent('<p>Content 1</p>', 'html');
      await processor.processContent('# Markdown content', 'markdown');
      await processor.processContent('Text content', 'text');

      const stats = processor.getStats();
      expect(stats).toHaveProperty('html-direct');
      expect(stats).toHaveProperty('markdown');
      expect(stats).toHaveProperty('text-only');
      expect(stats['html-direct'].count).toBeGreaterThan(0);
    });

    it('should handle timeout scenarios', async () => {
      mockHtmlToLexical.mockImplementation(
        () => new Promise(resolve => setTimeout(resolve, 2000))
      );

      const result = await processor.processContent(
        '<p>Slow content</p>',
        'html',
        { maxProcessingTime: 100 }
      );

      // Should fallback due to timeout
      expect(result.metrics.fallbackUsed).toBe(true);
    });
  });

  describe('Content Quality Analysis', () => {
    it('should analyze financial content correctly', async () => {
      mockValidateHTMLForConversion.mockReturnValue({
        score: 90,
        hasStructuredContent: true,
        isFinancialContent: true,
        details: {
          hasGermanContent: true,
          complexity: 'high',
        },
        recommendedFormat: 'html',
      });

      const result = await processor.processContent(
        '<h1>DAX Analysis</h1><p>Financial content with €1.2 billion revenue</p>',
        'html'
      );

      expect(result.metadata.isFinancialContent).toBe(true);
      expect(result.metadata.hasGermanContent).toBe(true);
      expect(result.metadata.complexity).toBe('high');
      expect(result.metrics.qualityScore).toBe(90);
    });

    it('should handle empty content gracefully', async () => {
      const result = await processor.processContent('', 'html');

      expect(result.strategy).toBe('text-only');
      expect(result.metrics.success).toBe(true);
      expect(result.lexicalContent.root.children).toHaveLength(1);
    });

    it('should validate and normalize Lexical output', async () => {
      mockValidateLexicalData.mockReturnValue({
        isValid: false,
        errors: ['Invalid structure'],
        normalized: { root: { type: 'root', children: [] } },
      });

      const result = await processor.processContent(
        '<p>Content with invalid output</p>',
        'html'
      );

      expect(result.warnings).toContain(
        'Content validation failed, normalizing data'
      );
      expect(mockNormalizeLexicalData).toHaveBeenCalled();
    });
  });

  describe('Real-world Content Tests', () => {
    it('should process German financial article efficiently', async () => {
      const germanContent = `
				<h1>DAX Weekly Outlook: Starke Performance erwartet</h1>
				<p>Die deutsche Börse zeigt sich von ihrer besten Seite.</p>
				<table>
					<tr><th>Index</th><th>Wert</th></tr>
					<tr><td>DAX</td><td>15.850</td></tr>
				</table>
			`;

      const result = await processor.processContent(germanContent, 'html');

      expect(result.metrics.success).toBe(true);
      expect(result.metadata.isFinancialContent).toBe(true);
      expect(result.metadata.hasGermanContent).toBe(true);
      expect(result.metrics.processingTime).toBeLessThan(5000);
    });

    it('should handle complex financial data tables', async () => {
      const complexContent = `
				<h1>Q3 Financial Report</h1>
				<table>
					<thead>
						<tr><th>Metric</th><th>Q3 2024</th><th>Q3 2023</th><th>Change</th></tr>
					</thead>
					<tbody>
						<tr><td>Revenue</td><td>€2.45B</td><td>€2.12B</td><td>+15.3%</td></tr>
						<tr><td>EBITDA</td><td>€485M</td><td>€398M</td><td>+21.9%</td></tr>
					</tbody>
				</table>
			`;

      const result = await processor.processContent(complexContent, 'html');

      expect(result.metrics.success).toBe(true);
      expect(result.metadata.hasStructuredContent).toBe(true);
      expect(result.strategy).toBe('html-direct');
    });

    it('should process mixed language content', async () => {
      const mixedContent = `
				<h1>Market Update: Strong Performance</h1>
				<p>Today's trading session showed remarkable strength.</p>
				<h2>Deutsche Märkte</h2>
				<p>Der DAX stieg um 2,5% auf 15.850 Punkte.</p>
			`;

      const result = await processor.processContent(mixedContent, 'html');

      expect(result.metrics.success).toBe(true);
      expect(result.metadata.hasGermanContent).toBe(true);
      expect(result.metadata.isFinancialContent).toBe(true);
    });
  });

  describe('Error Handling', () => {
    it('should handle processing errors gracefully', async () => {
      mockHtmlToLexical.mockRejectedValue(new Error('Conversion failed'));
      mockMarkdownToLexical.mockRejectedValue(new Error('Markdown failed'));

      const result = await processor.processContent(
        '<p>Problematic content</p>',
        'html'
      );

      expect(result.metrics.success).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
      expect(result.strategy).toBe('text-only');
    });

    it('should provide meaningful error messages', async () => {
      mockHtmlToLexical.mockRejectedValue(
        new Error('PayloadCMS conversion failed')
      );

      const result = await processor.processContent('<p>Content</p>', 'html', {
        enableFallback: false,
      });

      expect(result.errors).toContain(
        'Content processing failed: PayloadCMS conversion failed'
      );
    });
  });

  describe('Strategy Forcing', () => {
    it('should respect forced strategy selection', async () => {
      const result = await processor.processContent(
        '<p>Simple content</p>',
        'html',
        { forceStrategy: 'markdown' }
      );

      expect(result.strategy).toBe('markdown');
      expect(mockMarkdownToLexical).toHaveBeenCalled();
    });

    it('should still apply fallback with forced strategy', async () => {
      mockMarkdownToLexical.mockRejectedValue(
        new Error('Forced strategy failed')
      );

      const result = await processor.processContent('<p>Content</p>', 'html', {
        forceStrategy: 'markdown',
        enableFallback: true,
      });

      expect(result.strategy).toBe('text-only');
      expect(result.metrics.fallbackUsed).toBe(true);
    });
  });
});
