/**
 * PayloadCMS-native Lexical node type definitions
 * Based on @payloadcms/richtext-lexical structure
 */

/**
 * PayloadCMS Lexical Link Node structure
 * Follows PayloadCMS LinkFeature node format
 */
export interface PayloadLexicalLinkNode {
  type: 'link';
  version: number;
  direction?: 'ltr' | 'rtl';
  format?: string | number;
  indent?: number;
  children: Array<any>;

  // PayloadCMS LinkFeature fields
  url?: string;
  newTab?: boolean;
  rel?: string[];

  // PayloadCMS fields structure (from LinkFeature)
  fields?: {
    linkType: 'custom' | 'internal';
    url?: string;
    newTab?: boolean;
    rel?: string[];
    doc?: {
      value: string | number;
      relationTo: string;
      data?: {
        id: string | number;
        slug?: string;
        title?: string;
      };
    };
  };

  // Alternative PayloadCMS structure (for internal links)
  attributes?: {
    linkType: 'custom' | 'internal';
    url?: string;
    newTab?: boolean;
    rel?: string[];
    doc?: {
      value: string | number;
      relationTo: string;
      data?: {
        id: string | number;
        slug?: string;
        title?: string;
      };
    };
  };
}

/**
 * PayloadCMS Lexical Upload Node structure
 */
export interface PayloadLexicalUploadNode {
  type: 'upload';
  version: number;
  value:
    | string
    | number
    | {
        id: string | number;
        url?: string;
        alt?: string;
        width?: number;
        height?: number;
        filename?: string;
      };
  relationTo: string;
  format?: string;
  fields?: any;
}

/**
 * Type guard for PayloadCMS link nodes
 */
export function isPayloadLinkNode(node: any): node is PayloadLexicalLinkNode {
  return node && typeof node === 'object' && node.type === 'link';
}

/**
 * Type guard for PayloadCMS upload nodes
 */
export function isPayloadUploadNode(
  node: any
): node is PayloadLexicalUploadNode {
  return node && typeof node === 'object' && node.type === 'upload';
}

/**
 * Extract URL from PayloadCMS link node following their conventions
 * Enhanced to handle all possible PayloadCMS link structures
 */
export function extractLinkUrl(node: any): string {
  console.log(
    '🔍 ENHANCED: Extracting URL from link node:',
    JSON.stringify(node, null, 2)
  );

  // PayloadCMS LinkFeature creates nodes with different structures
  // Let's try all possible variations:

  // 1. Standard PayloadCMS LinkFeature with fields
  if (node.fields) {
    console.log('🔍 Found fields structure:', node.fields);

    if (node.fields.linkType === 'custom' && node.fields.url) {
      console.log('✅ Found custom link in fields:', node.fields.url);
      return node.fields.url;
    }

    if (node.fields.linkType === 'internal' && node.fields.doc) {
      const doc = node.fields.doc;
      if (doc.data?.slug) {
        const url = `/${doc.relationTo}/${doc.data.slug}`;
        console.log('✅ Constructed internal URL from fields:', url);
        return url;
      }
    }
  }

  // 2. Direct URL (custom external link)
  if (node.url && node.url !== '#') {
    console.log('✅ Found direct URL:', node.url);
    return node.url;
  }

  // 3. Alternative direct properties
  if (node.href && node.href !== '#') {
    console.log('✅ Found href:', node.href);
    return node.href;
  }

  // 4. PayloadCMS attributes structure (original approach)
  if (node.attributes) {
    console.log('🔍 Found attributes structure:', node.attributes);

    if (node.attributes.linkType === 'custom' && node.attributes.url) {
      console.log(
        '✅ Found custom link URL in attributes:',
        node.attributes.url
      );
      return node.attributes.url;
    }

    if (
      node.attributes.linkType === 'internal' &&
      node.attributes.doc?.data?.slug
    ) {
      const collection = node.attributes.doc.relationTo;
      const slug = node.attributes.doc.data.slug;
      const url = `/${collection}/${slug}`;
      console.log('✅ Constructed internal URL from attributes:', url);
      return url;
    }

    // Check if attributes has direct url
    if (node.attributes.url && node.attributes.url !== '#') {
      console.log('✅ Found direct URL in attributes:', node.attributes.url);
      return node.attributes.url;
    }
  }

  // 5. PayloadCMS native structure (generated during HTML conversion)
  if (node.linkType) {
    console.log('🔍 Found linkType structure:', { linkType: node.linkType });

    if (node.linkType === 'custom' && node.linkUrl) {
      console.log('✅ Found custom linkUrl:', node.linkUrl);
      return node.linkUrl;
    }
  }

  // 6. Check all possible URL properties
  const possibleUrlProps = [
    'url',
    'href',
    'link',
    'to',
    'linkUrl',
    'src',
    'target_url',
    'destination',
  ];

  for (const prop of possibleUrlProps) {
    if (node[prop] && typeof node[prop] === 'string' && node[prop] !== '#') {
      console.log(`✅ Found URL in property '${prop}':`, node[prop]);
      return node[prop];
    }
  }

  // 7. Deep search in nested objects
  const deepSearch = (obj: any, path = ''): string | null => {
    if (!obj || typeof obj !== 'object') return null;

    for (const [key, value] of Object.entries(obj)) {
      const currentPath = path ? `${path}.${key}` : key;

      if (
        typeof value === 'string' &&
        value !== '#' &&
        value !== '' &&
        (value.startsWith('http') ||
          value.startsWith('/') ||
          value.startsWith('mailto:') ||
          value.includes('.'))
      ) {
        console.log(`🔍 Deep search found URL at ${currentPath}:`, value);
        return value;
      }

      if (typeof value === 'object' && value !== null) {
        const result = deepSearch(value, currentPath);
        if (result) return result;
      }
    }
    return null;
  };

  const deepResult = deepSearch(node);
  if (deepResult) {
    console.log('✅ Deep search found URL:', deepResult);
    return deepResult;
  }

  console.warn(
    '❌ ENHANCED: No valid URL found in link node after exhaustive search'
  );
  console.warn('❌ Node keys:', Object.keys(node));
  console.warn('❌ Full node:', JSON.stringify(node, null, 2));

  return '#';
}
