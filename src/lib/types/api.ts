/**
 * API Types for BörsenBlick
 * Replaces `any` types with proper TypeScript interfaces
 */

// Error handling types
export interface APIError extends Error {
  message: string;
  code?: string;
  statusCode?: number;
}

// PayloadCMS types
export interface LexicalContent {
  root: {
    type: string;
    children: Array<{
      type: string;
      version?: number;
      [key: string]: unknown;
    }>;
    direction?: string;
    format?: string;
    indent?: number;
    version?: number;
  };
}

export interface SourcesTab {
  originalTitle?: string;
  originalContent?: LexicalContent | string;
  sourceUrl?: string;
  language?: 'de' | 'en';
}

export interface EnglishTab {
  enhancedTitle?: string;
  enhancedContent?: LexicalContent;
  summary?: string;
  keyInsights?: Array<{ insight: string }>;
  qualityScore?: number;
  relevanceScore?: number;
  enhancementQuality?: number;
}

export interface RelatedCompany {
  name: string;
  tickerSymbol?: string;
  exchange?: string;
  sector?: string;
}

export interface Keyword {
  keyword: string;
  englishKeyword?: string;
  isActive?: boolean;
}

export interface Article {
  id: string;
  title: string;
  workflowStage:
    | 'candidate-article'
    | 'enhanced'
    | 'ready-for-review'
    | 'published'
    | 'rejected';
  articleType: 'generated' | 'manual';
  sourcesTab?: SourcesTab;
  englishTab?: EnglishTab;
  keywords?: Keyword[];
  relatedCompanies?: RelatedCompany[];
  rejectionReason?: string;
  createdAt: string;
  updatedAt: string;
}

// Enhancement API types
export interface EnhancementResult {
  success: boolean;
  data?: {
    enhancedContent: {
      title: string;
      content: string;
      summary: string;
      keyInsights: string[];
      keywords: string[];
      relatedCompanies: RelatedCompany[];
    };
    quality: {
      contentScore: number;
      relevanceScore: number;
      enhancementQuality: number;
    };
  };
  metrics: {
    processingTime: number;
    costReduction: number;
    functionsConsolidated: number;
  };
  error?: string;
}

// API Response types
export interface APIResponse<T = unknown> {
  success: boolean;
  data?: T;
  message?: string;
  error?: string;
}

export interface ArticleUpdateData {
  englishTab?: Partial<EnglishTab>;
  keywords?: Keyword[];
  relatedCompanies?: RelatedCompany[];
  workflowStage?: Article['workflowStage'];
  rejectionReason?: string;
}

// RSS and Content types
export interface RSSFeedData {
  name: string;
  url: string;
  isActive: boolean;
  language: 'de' | 'en';
  processingFrequency: number;
  priority: 'low' | 'medium' | 'high';
}

export interface CategoryData {
  title: string;
}

// Database operation types
export interface DatabaseStats {
  keywords: number;
  categories: number;
  rssFeeds: number;
  articles: number;
}

// HTML to Lexical conversion types
export interface ConversionResult {
  result: LexicalContent;
  metrics: {
    success: boolean;
    processingTime?: number;
    errors?: string[];
  };
}

// Processing metadata types
export interface ProcessingMetadata {
  startTime: number;
  endTime?: number;
  processingTime?: number;
  errors?: APIError[];
  warnings?: string[];
}
