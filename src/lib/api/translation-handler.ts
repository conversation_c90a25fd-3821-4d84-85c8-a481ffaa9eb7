/**
 * Generic Translation API Handler
 *
 * Provides standardized translation API functionality for any PayloadCMS collection
 * that supports bilingual English/German content. Extracts common patterns from
 * the Articles translation system for reuse.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-27
 */

import config from '@payload-config';
import { getPayload } from 'payload';
import { NextResponse } from 'next/server';
import fs from 'fs';
import path from 'path';
import {
  translationService,
  type TranslationCollection,
} from '@/lib/services/translation-service';

/**
 * Translation request body structure
 * Supports multiple ID field names for backward compatibility
 */
export interface TranslationRequest {
  documentId?: string;
  articleId?: string;
  pageId?: string;
  collection?: TranslationCollection;
}

/**
 * Debug logging function - controlled by environment variables
 * Set DEBUG_TRANSLATION=true for console logs
 * Set DEBUG_TRANSLATION_FILE=true for file logs
 */
function debugLog(message: string, ...args: any[]) {
  // Only log in development or when DEBUG_TRANSLATION is set
  if (process.env.NODE_ENV === 'development' || process.env.DEBUG_TRANSLATION) {
    console.log(message, ...args);

    // Optional file logging for detailed debugging
    if (process.env.DEBUG_TRANSLATION_FILE) {
      try {
        const logDir = path.join(process.cwd(), 'logs');
        if (!fs.existsSync(logDir)) {
          fs.mkdirSync(logDir, { recursive: true });
        }

        const logFile = path.join(
          logDir,
          `translation-debug-${new Date().toISOString().split('T')[0]}.log`
        );
        const timestamp = new Date().toISOString();
        const logEntry = `${timestamp} - ${message} ${args
          .map(arg =>
            typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
          )
          .join(' ')}\n`;

        fs.appendFileSync(logFile, logEntry);
      } catch (error) {
        // Don't fail translation if logging fails
        console.error('Failed to write debug log:', error);
      }
    }
  }
}

/**
 * Standardized API response format
 */
export interface StandardAPIResponse {
  success: boolean;
  message?: string;
  data?: any;
  error?: string;
  metrics?: {
    processingTime: number;
    tokenUsage?: number;
    costEstimate?: number;
    linguisticAccuracy?: number;
    culturalAdaptation?: number;
  };
}

/**
 * Generic Translation Handler Class
 */
export class TranslationHandler {
  private collection: TranslationCollection;

  constructor(collection: TranslationCollection) {
    this.collection = collection;
  }

  /**
   * Handle translation request
   */
  async handleTranslation(
    requestBody: TranslationRequest,
    options?: { timeout?: number }
  ): Promise<NextResponse> {
    const startTime = Date.now();

    try {
      // Handle multiple ID field names for backward compatibility
      const documentId =
        (requestBody as any).documentId ||
        (requestBody as any).articleId ||
        (requestBody as any).pageId;

      if (!documentId) {
        console.error(
          `❌ No documentId provided in ${this.collection} translation request`
        );
        return NextResponse.json(
          { success: false, error: 'Document ID is required' },
          { status: 400 }
        );
      }

      console.log(
        `🚀 Starting German translation for ${this.collection}: ${documentId}`
      );
      console.log(
        '📋 Working with saved database content only (save-first workflow)'
      );

      const payload = await getPayload({ config });

      // 🔧 ENHANCED DATABASE TIMING FIX: Robust fresh data validation
      // Critical: Ensure we're reading the user's latest changes, not stale data
      debugLog(
        '⏱️ TIMING DEBUG: Starting enhanced database freshness validation...'
      );

      const maxRetries = 5;
      const baseDelay = 1000; // Start with 1 second
      let document: any = null;
      let retryCount = 0;
      const startTime = Date.now();

      debugLog(
        `⏱️ TIMING DEBUG: Translation triggered at: ${new Date().toISOString()}`
      );
      debugLog(
        '⏱️ TIMING DEBUG: Will retry up to 5 times with increasing delays if document is stale'
      );

      // Convert documentId to string for PayloadCMS (handles both string and number inputs)
      const documentIdString = String(documentId);

      // RETRY LOOP: Keep fetching until we get fresh data
      while (retryCount < maxRetries) {
        const currentDelay = baseDelay * (retryCount + 1); // Exponential backoff: 1s, 2s, 3s, 4s, 5s

        if (retryCount > 0) {
          debugLog(
            `⏱️ TIMING DEBUG: Retry ${retryCount}/${maxRetries} - waiting ${currentDelay}ms...`
          );
          await new Promise(resolve => setTimeout(resolve, currentDelay));
        } else {
          debugLog(
            `⏱️ TIMING DEBUG: Initial attempt - waiting ${currentDelay}ms for database consistency...`
          );
          await new Promise(resolve => setTimeout(resolve, currentDelay));
        }

        debugLog('📊 DATABASE FETCH: Starting document retrieval...');
        debugLog(`📊 DATABASE FETCH: Collection: ${this.collection}`);
        debugLog(`📊 DATABASE FETCH: ID: ${documentIdString}`);
        debugLog(`📊 DATABASE FETCH: Attempt: ${retryCount + 1}/${maxRetries}`);
        debugLog(`📊 DATABASE FETCH: Time: ${new Date().toISOString()}`);

        try {
          document = await payload.findByID({
            collection: this.collection,
            id: documentIdString,
            // Override access control for internal API operations
            overrideAccess: true,
          });

          debugLog('📊 DATABASE FETCH: Document retrieved successfully');
          debugLog(
            `📊 DATABASE FETCH: Document updatedAt: ${document.updatedAt}`
          );
          debugLog(
            `📊 DATABASE FETCH: Fetch completed at: ${new Date().toISOString()}`
          );

          // FRESHNESS VALIDATION: Check if document was updated recently
          const docUpdated = new Date(document.updatedAt);
          const now = new Date();
          const timeDiff = now.getTime() - docUpdated.getTime();
          const diffMinutes = Math.floor(timeDiff / (1000 * 60));
          const diffHours = Math.floor(diffMinutes / 60);
          const diffDays = Math.floor(diffHours / 24);

          debugLog(
            `📊 FRESHNESS CHECK: Document age: ${diffMinutes} minutes (${diffHours}h ${diffDays}d)`
          );
          debugLog(
            `📊 FRESHNESS CHECK: Document updated: ${docUpdated.toISOString()}`
          );
          debugLog(`📊 FRESHNESS CHECK: Current time: ${now.toISOString()}`);

          // RELAXED FRESHNESS CHECK: Accept documents updated within last 24 hours OR proceed after 3 attempts
          const isRecent = timeDiff <= 86400000; // 24 hours = 86400000ms
          const shouldProceedAnyway = retryCount >= 2; // After 3 attempts, proceed anyway

          if (isRecent) {
            debugLog(
              '✅ FRESHNESS CHECK: Document is recent (within 24h) - proceeding with translation'
            );
            break;
          } else if (shouldProceedAnyway) {
            debugLog(
              `⚠️ FRESHNESS CHECK: Document is old (${diffDays} days) but proceeding after ${retryCount + 1} attempts`
            );
            debugLog(
              '💡 RECOMMENDATION: Save your changes first, then translate for best results'
            );
            break;
          } else {
            debugLog(
              `⚠️ FRESHNESS CHECK: Document is stale (${diffDays} days old) - retrying... (attempt ${retryCount + 1})`
            );
            retryCount++;
            continue;
          }
        } catch (error) {
          debugLog(
            `❌ DATABASE FETCH ERROR on attempt ${retryCount + 1}:`,
            error
          );
          retryCount++;
          if (retryCount >= maxRetries) {
            throw error;
          }
          continue;
        }
      }

      // Final validation
      if (retryCount >= maxRetries) {
        const totalTime = Date.now() - startTime;
        debugLog(
          `❌ TIMEOUT: Could not get fresh document after ${maxRetries} attempts (${totalTime}ms total)`
        );
        throw new Error(
          `Translation failed: Could not retrieve fresh document data after ${maxRetries} attempts. Please save your changes and try again.`
        );
      }

      if (!document) {
        console.error(`❌ ${this.collection} not found with ID: ${documentId}`);
        return NextResponse.json(
          { success: false, error: `${this.collection} not found` },
          { status: 404 }
        );
      }

      console.log(
        `📄 ${this.collection} found: ${document.title || 'Untitled'}`
      );

      // Extract content using the translation service
      const translatableContent = translationService.extractContent(
        document,
        this.collection
      );

      console.log('🔍 Extracted content for translation:', {
        collection: this.collection,
        title: translatableContent.title,
        hasContent: !!translatableContent.content,
        hasGermanTranslation: translatableContent.hasGermanTranslation,
      });

      // Validate content is ready for translation
      const validation =
        translationService.validateForTranslation(translatableContent);

      if (!validation.isValid) {
        console.error('❌ Content validation failed:', validation.errors);
        return NextResponse.json(
          {
            success: false,
            error: `Content validation failed: ${validation.errors.join(', ')}`,
          },
          { status: 400 }
        );
      }

      // Allow re-translation if German translation already exists
      const isReTranslation = !!translatableContent.hasGermanTranslation;
      if (isReTranslation) {
        console.log('🔄 Re-translating existing German content');
      } else {
        console.log('🆕 Initial German translation');
      }

      // Perform translation using the generic service
      const translationResult = await translationService.translateContent(
        translatableContent,
        {
          temperature: 0.3, // Lower temperature for more literal translations
          includeProcessingMetadata: true,
        }
      );

      if (!translationResult.success || !translationResult.data) {
        console.error('❌ German translation failed:', translationResult.error);
        return NextResponse.json(
          {
            success: false,
            error: `German translation failed: ${translationResult.error}`,
          },
          { status: 500 }
        );
      }

      console.log('✅ German translation successful');
      console.log(
        `📊 Performance: ${translationResult.metrics.processingTime}ms`
      );

      // Create update data using the translation service
      const updateData = translationService.createUpdateData(
        translationResult.data,
        this.collection
      );

      // Preserve important existing fields during translation
      this.preserveExistingFields(document, updateData);

      // Update workflow stage for articles (pages don't have workflow stages)
      if (this.collection === 'articles') {
        const articleDoc = document as any; // Type assertion for articles
        if (articleDoc.workflowStage === 'candidate-article') {
          updateData.workflowStage = 'translated';
        }
      }

      // Update the document
      await payload.update({
        collection: this.collection,
        id: documentIdString,
        data: updateData,
      });

      console.log(
        `✅ German ${isReTranslation ? 're-' : ''}translation completed for ${this.collection} ${documentIdString}`
      );

      // Prepare standardized API response
      const responseData = this.createResponseData(
        translationResult,
        updateData,
        document
      );

      return NextResponse.json({
        success: true,
        message: `${this.collection} ${isReTranslation ? 're-' : ''}translated successfully`,
        data: responseData,
        metrics: {
          processingTime: translationResult.metrics.processingTime,
          tokenUsage: translationResult.metrics.tokenUsage,
          costEstimate: translationResult.metrics.costEstimate,
          linguisticAccuracy: translationResult.metrics.linguisticAccuracy,
          culturalAdaptation: translationResult.metrics.culturalAdaptation,
        },
      });
    } catch (error: any) {
      const processingTime = Date.now() - startTime;
      console.error(`❌ Error translating ${this.collection}:`, error);

      return NextResponse.json(
        {
          success: false,
          error:
            error.message ||
            `Unknown error occurred during ${this.collection} translation`,
          metrics: {
            processingTime,
            tokenUsage: 0,
            costEstimate: 0,
          },
        },
        { status: 500 }
      );
    }
  }

  /**
   * Preserve important existing fields during translation
   */
  private preserveExistingFields(document: any, updateData: any): void {
    debugLog(
      '🚨 COMPREHENSIVE FIELD DEBUG: Starting complete document analysis...'
    );

    // Debug: All critical fields user mentioned losing
    const criticalFields = [
      'featuredImage',
      'categories',
      'placement',
      'featured',
      'pinned',
      'trending',
      'relatedCompanies',
      'publishedBy',
      'publishedAt',
    ];

    debugLog('🔍 CRITICAL FIELDS STATUS:');
    criticalFields.forEach(field => {
      const exists = document[field] !== undefined;
      const value = document[field];
      const type = typeof value;
      const isArray = Array.isArray(value);
      const length = isArray ? value.length : 'N/A';

      debugLog(`  🔹 ${field}:`);
      debugLog(`     - Exists: ${exists}`);
      debugLog(`     - Type: ${type}`);
      debugLog(`     - Value: ${JSON.stringify(value)}`);
      if (isArray) debugLog(`     - Length: ${length}`);
    });

    // Debug: Show ALL document fields for comparison
    debugLog('🔍 ALL DOCUMENT FIELDS:');
    const allFields = Object.keys(document);
    debugLog(`   Total fields: ${allFields.length}`);
    debugLog(`   Fields: ${allFields.join(', ')}`);

    // Debug: Document ID and timing
    debugLog('🔍 DOCUMENT INFO:');
    debugLog(`   - ID: ${document.id}`);
    debugLog(`   - Title: ${document.title}`);
    debugLog(`   - Updated At: ${document.updatedAt}`);
    debugLog(`   - Created At: ${document.createdAt}`);

    // Common fields to preserve
    const fieldsToPreserve = [
      'featuredImage',
      'categories',
      'placement',
      'featured',
      'pinned',
      'trending',
      'publishedBy',
      'publishedAt',
    ];

    fieldsToPreserve.forEach(field => {
      if (document[field] !== undefined) {
        updateData[field] = document[field];
      }
    });

    // Articles-specific fields
    if (this.collection === 'articles') {
      const articleFields = [
        'relatedCompanies',
        'hasBeenEnhanced',
        'hasOriginalSource',
        'readTimeMinutes',
        'sourceFeed',
        'sourceUrl',
        'originalPublishedAt',
        'originalTitle',
        'originalSummary',
        'originalContent',
      ];
      articleFields.forEach(field => {
        if (document[field] !== undefined) {
          updateData[field] = document[field];
        }
      });

      // Preserve sourcesTab data if it exists
      if (document.sourcesTab) {
        updateData.sourcesTab = {
          ...document.sourcesTab,
          // Don't overwrite any existing sourcesTab fields
        };
      }

      // DO NOT preserve englishTab - translation should never modify English content
      // The English content should remain untouched during translation
      // Only German fields should be updated
    }

    // Pages-specific fields - add any page-specific preservation logic here
    if (this.collection === 'pages') {
      // Pages might have specific fields to preserve in the future
    }

    debugLog(
      '✅ FIELD PRESERVATION: All critical fields processed and preserved'
    );
  }

  /**
   * Create standardized response data
   */
  private createResponseData(
    translationResult: any,
    updateData: any,
    originalDocument: any
  ): any {
    const mapping = translationService.getFieldMapping(this.collection);

    // Base response structure
    const responseData: any = {
      // Translation flag
      [mapping.hasTranslationFlag]: true,
    };

    // Create german tab structure for form updates
    if (translationResult.data) {
      responseData.germanTab = {
        germanTitle: translationResult.data.germanTitle,
        germanContent: translationResult.data.germanContent,
      };

      // Add summary if supported
      if (translationResult.data.germanSummary) {
        responseData.germanTab.germanSummary =
          translationResult.data.germanSummary;
      }

      // Articles-specific fields
      if (
        this.collection === 'articles' &&
        translationResult.data.germanKeyInsights
      ) {
        responseData.germanTab.germanKeyInsights =
          translationResult.data.germanKeyInsights;
      }

      if (
        this.collection === 'articles' &&
        translationResult.data.germanKeywords
      ) {
        responseData.germanTab.germanKeywords =
          translationResult.data.germanKeywords;
      }
    }

    // Workflow stage updates for articles
    if (this.collection === 'articles') {
      const articleDoc = originalDocument as any; // Type assertion for articles
      if (articleDoc.workflowStage === 'candidate-article') {
        responseData.workflowStage = 'translated';
      }
    }

    return responseData;
  }

  /**
   * Validate request format
   */
  static validateRequest(body: any): { isValid: boolean; error?: string } {
    if (!body || typeof body !== 'object') {
      return {
        isValid: false,
        error: 'Request body must be a valid JSON object',
      };
    }

    // Accept multiple ID field names for backward compatibility
    const id = body.documentId || body.articleId || body.pageId;

    if (!id) {
      return {
        isValid: false,
        error: 'documentId (or articleId/pageId) is required',
      };
    }

    // Accept both string and number IDs (PayloadCMS IDs can be numbers)
    if (typeof id !== 'string' && typeof id !== 'number') {
      return {
        isValid: false,
        error: 'ID must be a string or number',
      };
    }

    return { isValid: true };
  }
}

/**
 * Create translation handler for specific collection
 */
export function createTranslationHandler(
  collection: TranslationCollection
): TranslationHandler {
  return new TranslationHandler(collection);
}

/**
 * Generic translation API route handler
 * Can be used in any collection's translation API route
 */
export async function handleTranslationRequest(
  request: Request,
  collection: TranslationCollection
): Promise<NextResponse> {
  try {
    const body = await request.json();

    // Validate request format
    const validation = TranslationHandler.validateRequest(body);
    if (!validation.isValid) {
      return NextResponse.json(
        { success: false, error: validation.error },
        { status: 400 }
      );
    }

    // Create and use translation handler
    const handler = createTranslationHandler(collection);
    return await handler.handleTranslation(body);
  } catch (error) {
    console.error(`❌ Error parsing ${collection} translation request:`, error);
    return NextResponse.json(
      { success: false, error: 'Invalid request format' },
      { status: 400 }
    );
  }
}
