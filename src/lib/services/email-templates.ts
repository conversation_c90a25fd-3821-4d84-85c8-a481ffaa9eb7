/**
 * Shared Email Templates for Pipeline Notifications
 *
 * Centralised email template system for all BörsenBlick pipeline notifications.
 * This enables consistent styling and easy maintenance across all email alerts.
 */

export interface PipelineResult {
  success: boolean;
  processed: number;
  accepted: number;
  rejected: number;
  errors: string[];
  details: Array<{
    url: string;
    title: string;
    status: 'accepted' | 'rejected' | 'error' | 'rate_limited';
    reason?: string;
    articleId?: string;
  }>;
  timing: {
    processingTimeMs: number;
    processingTimeSeconds: number;
    startTime: string;
    endTime: string;
  };
  database: {
    totalArticles: number;
    candidateArticles: number;
    publishedArticles: number;
  };
  // Optional additional data for different pipeline types
  feeds?: Array<{
    id: string;
    name: string;
    url?: string;
    language?: string;
    priority?: string;
  }>;
  firecrawl?: {
    totalRequests: number;
    successfulRequests: number;
    failedRequests: number;
    successRate: number;
    errors: {
      rateLimits: number;
      configuration: number;
      authentication: number;
      timeouts: number;
    };
  };
  performance?: {
    throughputPerMinute: number;
    memoryUsageMB: number;
    parallelEfficiency: number;
  };
  costs?: {
    firecrawlCost: number;
    openaiCost: number;
    totalCost: number;
  };
  quality?: {
    averageContentQuality: number;
    duplicateFilterEfficiency: number;
    keywordMatchAccuracy: number;
  };
}

export interface ArticleSummary {
  id: string;
  title: string;
  status: string;
  createdAt: string;
  sourceUrl?: string;
  adminUrl: string;
}

/**
 * Generate email subject for pipeline notifications
 */
export function generatePipelineEmailSubject(
  pipelineType: string,
  result: PipelineResult
): string {
  const pipelineName = formatPipelineName(pipelineType);
  const status = result.success ? '✅ Completed' : '❌ Failed';
  const summary = result.success
    ? `${result.accepted} articles processed`
    : `${result.errors.length} errors`;

  return `${status}: ${pipelineName} - ${summary}`;
}

/**
 * Generate comprehensive HTML email content for pipeline notifications
 */
export function generatePipelineEmailContent(
  pipelineType: string,
  result: PipelineResult,
  newArticles: ArticleSummary[],
  candidateArticles: ArticleSummary[],
  baseUrl: string
): string {
  const pipelineName = formatPipelineName(pipelineType);
  const statusIcon = result.success ? '✅' : '❌';
  const statusColor = result.success ? '#059669' : '#DC2626';

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>BörsenBlick - Pipeline Report</title>
    <style>
        ${getEmailStyles()}
    </style>
</head>
<body>
    <div class="container">
        ${generateEmailHeader(pipelineName, statusIcon)}
        
        <div class="content">
            ${generateStatusBadge(result.success, statusColor)}
            ${generateMetricsGrid(result)}
            ${generateTimingSection(result.timing)}
            ${generateQuickLinksSection(baseUrl, result.database)}
            ${generatePerformanceSection(result)}
            ${generateFirecrawlSection(result)}
            ${generateCostsSection(result)}
            ${generateQualitySection(result)}
            ${generateFeedsSummarySection(result)}
            ${generateNewArticlesSection(newArticles)}
            ${generateCandidateArticlesSection(candidateArticles, baseUrl)}
            ${generateErrorsSection(result.errors)}
            ${generateProcessingDetailsSection(result.details, baseUrl)}
        </div>
        
        ${generateEmailFooter()}
    </div>
</body>
</html>`;
}

/**
 * Generate error notification email HTML
 */
export function generatePipelineErrorEmail(
  pipelineType: string,
  error: Error,
  baseUrl: string
): string {
  const pipelineName = formatPipelineName(pipelineType);

  return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>BörsenBlick - Pipeline Error</title>
    <style>
        ${getEmailStyles()}
    </style>
</head>
<body>
    <div style="font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
      <div style="background: #dc2626; color: white; padding: 20px; border-radius: 8px; text-align: center;">
        <h1>❌ Pipeline Failed</h1>
        <p>${pipelineName}</p>
      </div>
      
      <div style="padding: 20px; background: #fef2f2; border: 1px solid #fecaca; border-radius: 4px; margin: 20px 0;">
        <h3>Error Details:</h3>
        <pre style="background: white; padding: 16px; border-radius: 4px; overflow-x: auto;">${error.message}</pre>
        
        ${
          error.stack
            ? `
        <h3>Stack Trace:</h3>
        <pre style="background: white; padding: 16px; border-radius: 4px; overflow-x: auto; font-size: 12px;">${error.stack}</pre>
        `
            : ''
        }
      </div>
      
      <div style="text-align: center; margin: 20px 0;">
        <a href="${baseUrl}/admin" style="background: #3b82f6; color: white; padding: 12px 24px; border-radius: 6px; text-decoration: none;">
          Check Admin Panel →
        </a>
      </div>
      
      ${generateEmailFooter()}
    </div>
</body>
</html>`;
}

/**
 * Centralised CSS styles for all email templates
 */
function getEmailStyles(): string {
  return `
    body { 
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; 
        line-height: 1.6; 
        color: #374151; 
        background-color: #f9fafb; 
        margin: 0; 
        padding: 0; 
    }
    .container { 
        max-width: 600px; 
        margin: 0 auto; 
        background: white; 
        border-radius: 8px; 
        box-shadow: 0 1px 3px rgba(0,0,0,0.1); 
        overflow: hidden; 
    }
    .header { 
        background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); 
        color: white; 
        padding: 24px; 
        text-align: center; 
    }
    .header h1 { 
        margin: 0; 
        font-size: 24px; 
        font-weight: 600; 
    }
    .content { 
        padding: 24px; 
    }
    .status-badge { 
        display: inline-block; 
        padding: 8px 16px; 
        border-radius: 20px; 
        font-weight: 600; 
        font-size: 14px; 
        color: white; 
        margin-bottom: 20px; 
    }
    .metric-grid { 
        display: grid; 
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr)); 
        gap: 16px; 
        margin: 24px 0; 
    }
    .metric { 
        text-align: center; 
        padding: 16px; 
        background: #f3f4f6; 
        border-radius: 8px; 
    }
    .metric-value { 
        font-size: 24px; 
        font-weight: 700; 
        color: #1f2937; 
    }
    .metric-label { 
        font-size: 12px; 
        text-transform: uppercase; 
        font-weight: 600; 
        color: #6b7280; 
        margin-top: 4px; 
    }
    .section { 
        margin: 32px 0; 
    }
    .section h2 { 
        font-size: 18px; 
        font-weight: 600; 
        margin-bottom: 16px; 
        color: #1f2937; 
        border-bottom: 2px solid #e5e7eb; 
        padding-bottom: 8px; 
    }
    .article-list { 
        list-style: none; 
        padding: 0; 
        margin: 0; 
    }
    .article-item { 
        background: #fafafa; 
        border-left: 4px solid #3b82f6; 
        margin: 12px 0; 
        padding: 16px; 
        border-radius: 4px; 
    }
    .article-title { 
        font-weight: 600; 
        color: #1f2937; 
        margin-bottom: 8px; 
    }
    .article-meta { 
        font-size: 14px; 
        color: #6b7280; 
        margin-bottom: 8px; 
    }
    .article-link { 
        display: inline-block; 
        background: #3b82f6; 
        color: white; 
        padding: 6px 12px; 
        border-radius: 4px; 
        text-decoration: none; 
        font-size: 14px; 
        font-weight: 500; 
    }
    .error-list { 
        background: #fef2f2; 
        border: 1px solid #fecaca; 
        border-radius: 4px; 
        padding: 16px; 
    }
    .error-item { 
        color: #dc2626; 
        margin: 8px 0; 
        font-family: monospace; 
        font-size: 14px; 
    }
    .footer { 
        background: #f3f4f6; 
        padding: 24px; 
        text-align: center; 
        color: #6b7280; 
        font-size: 14px; 
    }
    .quick-links { 
        display: grid; 
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
        gap: 12px; 
        margin: 24px 0; 
    }
    .quick-link { 
        display: block; 
        background: #3b82f6; 
        color: white; 
        padding: 12px 16px; 
        border-radius: 6px; 
        text-decoration: none; 
        text-align: center; 
        font-weight: 500; 
    }
    .timing { 
        background: #f0f9ff; 
        border: 1px solid #bfdbfe; 
        border-radius: 4px; 
        padding: 16px; 
        margin: 16px 0; 
    }
    .info-box { 
        background: #f0f9ff; 
        border: 1px solid #bfdbfe; 
        border-radius: 4px; 
        padding: 16px; 
        margin: 16px 0; 
    }
    .performance-metric { 
        display: inline-block; 
        margin: 8px 16px 8px 0; 
        padding: 8px 12px; 
        background: #ecfdf5; 
        border: 1px solid #d1fae5; 
        border-radius: 4px; 
        font-size: 14px; 
    }
    .cost-metric { 
        display: inline-block; 
        margin: 8px 16px 8px 0; 
        padding: 8px 12px; 
        background: #fef3c7; 
        border: 1px solid #fde68a; 
        border-radius: 4px; 
        font-size: 14px; 
        font-weight: 600; 
    }
  `;
}

/**
 * Helper functions for generating email sections
 */
function formatPipelineName(pipelineType: string): string {
  return pipelineType.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
}

function generateEmailHeader(pipelineName: string, statusIcon: string): string {
  return `
    <div class="header">
                    <h1>${statusIcon} BörsenBlick</h1>
        <p>${pipelineName} Report</p>
    </div>
  `;
}

function generateStatusBadge(success: boolean, statusColor: string): string {
  return `
    <div class="status-badge" style="background-color: ${statusColor};">
        ${success ? 'Successfully Completed' : 'Failed'}
    </div>
  `;
}

function generateMetricsGrid(result: PipelineResult): string {
  return `
    <div class="metric-grid">
        <div class="metric">
            <div class="metric-value">${result.processed}</div>
            <div class="metric-label">Processed</div>
        </div>
        <div class="metric">
            <div class="metric-value">${result.accepted}</div>
            <div class="metric-label">Accepted</div>
        </div>
        <div class="metric">
            <div class="metric-value">${result.rejected}</div>
            <div class="metric-label">Rejected</div>
        </div>
        <div class="metric">
            <div class="metric-value">${result.errors.length}</div>
            <div class="metric-label">Errors</div>
        </div>
    </div>
  `;
}

function generateTimingSection(timing: PipelineResult['timing']): string {
  return `
    <div class="timing">
        <strong>Processing Time:</strong> ${timing.processingTimeSeconds}s<br>
        <strong>Started:</strong> ${new Date(timing.startTime).toLocaleString()}<br>
        <strong>Completed:</strong> ${new Date(timing.endTime).toLocaleString()}
    </div>
  `;
}

function generateQuickLinksSection(
  baseUrl: string,
  database: PipelineResult['database']
): string {
  return `
    <div class="quick-links">
        <a href="${baseUrl}/admin/collections/articles" class="quick-link">
            📰 All Articles (${database.totalArticles})
        </a>
        <a href="${baseUrl}/admin/collections/articles?where%5BworkflowStage%5D%5Bequals%5D=candidate-article" class="quick-link">
            🔍 Review Queue (${database.candidateArticles})
        </a>
        <a href="${baseUrl}/admin/collections/articles?where%5B_status%5D%5Bequals%5D=published" class="quick-link">
            ✅ Published (${database.publishedArticles})
        </a>
    </div>
  `;
}

function generatePerformanceSection(result: PipelineResult): string {
  if (!result.performance) return '';

  return `
    <div class="section">
        <h2>⚡ Performance Metrics</h2>
        <div class="performance-metric">
            📈 Throughput: ${result.performance.throughputPerMinute.toFixed(1)} articles/min
        </div>
        <div class="performance-metric">
            💾 Memory: ${result.performance.memoryUsageMB.toFixed(1)} MB
        </div>
        <div class="performance-metric">
            🔄 Parallel Efficiency: ${(result.performance.parallelEfficiency * 100).toFixed(1)}%
        </div>
    </div>
  `;
}

function generateFirecrawlSection(result: PipelineResult): string {
  if (!result.firecrawl || result.firecrawl.totalRequests === 0) return '';

  return `
    <div class="section">
        <h2>🔥 Firecrawl API Usage</h2>
        <div class="info-box">
            <strong>Total Requests:</strong> ${result.firecrawl.totalRequests}<br>
            <strong>Successful:</strong> ${result.firecrawl.successfulRequests} (${result.firecrawl.successRate}%)<br>
            <strong>Failed:</strong> ${result.firecrawl.failedRequests}<br>
            ${result.firecrawl.errors.rateLimits > 0 ? `<strong>Rate Limits:</strong> ${result.firecrawl.errors.rateLimits}<br>` : ''}
            ${result.firecrawl.errors.timeouts > 0 ? `<strong>Timeouts:</strong> ${result.firecrawl.errors.timeouts}<br>` : ''}
        </div>
    </div>
  `;
}

function generateCostsSection(result: PipelineResult): string {
  if (!result.costs) return '';

  return `
    <div class="section">
        <h2>💰 Cost Breakdown</h2>
        <div class="cost-metric">
            🔥 Firecrawl: $${result.costs.firecrawlCost.toFixed(3)}
        </div>
        <div class="cost-metric">
            🤖 OpenAI: $${result.costs.openaiCost.toFixed(3)}
        </div>
        <div class="cost-metric">
            💸 Total: $${result.costs.totalCost.toFixed(3)}
        </div>
    </div>
  `;
}

function generateQualitySection(result: PipelineResult): string {
  if (!result.quality) return '';

  return `
    <div class="section">
        <h2>🎯 Quality Metrics</h2>
        <div class="info-box">
            <strong>Average Content Quality:</strong> ${(result.quality.averageContentQuality * 100).toFixed(1)}%<br>
            <strong>Duplicate Filter Efficiency:</strong> ${(result.quality.duplicateFilterEfficiency * 100).toFixed(1)}%<br>
            <strong>Keyword Match Accuracy:</strong> ${(result.quality.keywordMatchAccuracy * 100).toFixed(1)}%
        </div>
    </div>
  `;
}

function generateFeedsSummarySection(result: PipelineResult): string {
  if (!result.feeds || result.feeds.length === 0) return '';

  return `
    <div class="section">
        <h2>📡 RSS Feeds Processed</h2>
        <div class="info-box">
            <strong>Total Feeds:</strong> ${result.feeds.length}<br>
            ${result.feeds
              .slice(0, 5)
              .map(
                feed =>
                  `<strong>${feed.name}</strong> (${feed.language || 'unknown'} - ${feed.priority || 'standard'})<br>`
              )
              .join('')}
            ${result.feeds.length > 5 ? `<em>...and ${result.feeds.length - 5} more feeds</em>` : ''}
        </div>
    </div>
  `;
}

function generateNewArticlesSection(newArticles: ArticleSummary[]): string {
  if (newArticles.length === 0) return '';

  return `
    <div class="section">
        <h2>🆕 Newly Created Articles (${newArticles.length})</h2>
        <ul class="article-list">
            ${newArticles
              .map(
                article => `
            <li class="article-item">
                <div class="article-title">${article.title}</div>
                <div class="article-meta">
                    Created: ${new Date(article.createdAt).toLocaleString()} | 
                    Status: ${article.status}
                    ${article.sourceUrl ? ` | <a href="${article.sourceUrl}" target="_blank">Source</a>` : ''}
                </div>
                <a href="${article.adminUrl}" class="article-link">Review Article →</a>
            </li>
            `
              )
              .join('')}
        </ul>
    </div>
  `;
}

function generateCandidateArticlesSection(
  candidateArticles: ArticleSummary[],
  baseUrl: string
): string {
  if (candidateArticles.length === 0) return '';

  return `
    <div class="section">
        <h2>📋 Articles Awaiting Review (${candidateArticles.length})</h2>
        <p>These articles are ready for editorial review and publication:</p>
        <ul class="article-list">
            ${candidateArticles
              .slice(0, 10)
              .map(
                article => `
            <li class="article-item">
                <div class="article-title">${article.title}</div>
                <div class="article-meta">
                    Created: ${new Date(article.createdAt).toLocaleString()}
                    ${article.sourceUrl ? ` | <a href="${article.sourceUrl}" target="_blank">Source</a>` : ''}
                </div>
                <a href="${article.adminUrl}" class="article-link">Review Article →</a>
            </li>
            `
              )
              .join('')}
        </ul>
        ${candidateArticles.length > 10 ? `<p><strong>...and ${candidateArticles.length - 10} more</strong> <a href="${baseUrl}/admin/collections/articles?where%5BworkflowStage%5D%5Bequals%5D=candidate-article">View all →</a></p>` : ''}
    </div>
  `;
}

function generateErrorsSection(errors: string[]): string {
  if (errors.length === 0) return '';

  return `
    <div class="section">
        <h2>⚠️ Errors (${errors.length})</h2>
        <div class="error-list">
            ${errors.map(error => `<div class="error-item">${error}</div>`).join('')}
        </div>
    </div>
  `;
}

function generateProcessingDetailsSection(
  details: PipelineResult['details'],
  baseUrl: string
): string {
  if (details.length === 0) return '';

  return `
    <div class="section">
        <h2>📊 Processing Details</h2>
        <ul class="article-list">
            ${details
              .slice(0, 10)
              .map(
                detail => `
            <li class="article-item" style="border-left-color: ${
              detail.status === 'accepted'
                ? '#059669'
                : detail.status === 'rejected'
                  ? '#d97706'
                  : '#dc2626'
            };">
                <div class="article-title">${detail.title}</div>
                <div class="article-meta">
                    Status: <strong>${detail.status.toUpperCase()}</strong>
                    ${detail.reason ? ` | ${detail.reason}` : ''}
                </div>
                ${detail.articleId ? `<a href="${baseUrl}/admin/collections/articles/${detail.articleId}" class="article-link">View Article →</a>` : ''}
            </li>
            `
              )
              .join('')}
        </ul>
        ${details.length > 10 ? `<p><strong>...and ${details.length - 10} more items processed</strong></p>` : ''}
    </div>
  `;
}

function generateEmailFooter(): string {
  return `
    <div class="footer">
                  <p>Generated by BörsenBlick Content Pipeline<br>
        ${new Date().toLocaleString()}</p>
    </div>
  `;
}
