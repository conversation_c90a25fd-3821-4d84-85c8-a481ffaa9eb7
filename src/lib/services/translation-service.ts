/**
 * Generic Translation Service
 *
 * Provides translation functionality for any PayloadCMS collection
 * that supports bilingual English/German content.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-27
 */

import { translateToGerman } from '@/lib/integrations/openai/german-translation';
import { lexicalToHTML } from '@/lib/utils/lexical';
import { htmlToLexical } from '@/lib/utils/html-to-lexical';
import { htmlToLexicalWithUploadPreservation } from '@/lib/utils/enhanced-html-to-lexical';

/**
 * Supported collections for translation
 */
export type TranslationCollection = 'articles' | 'pages';

/**
 * Content structure for translation
 */
export interface TranslatableContent {
  id: string;
  collection: TranslationCollection;
  title: string;
  content: any; // Lexical format
  summary?: string;
  keyInsights?: Array<{ insight: string }>;
  keywords?: Array<{ keyword: string }>;
  [key: string]: any;
}

/**
 * Translation result data structure
 */
export interface TranslationData {
  germanTitle: string;
  germanContent: object; // Lexical format
  germanSummary?: string;
  germanKeywords?: string[];
  germanKeyInsights?: string[];
}

/**
 * Translation metrics
 */
export interface TranslationMetrics {
  processingTime: number;
  tokenUsage: number;
  costEstimate: number;
  linguisticAccuracy?: number;
  culturalAdaptation?: number;
}

/**
 * Complete translation result
 */
export interface TranslationResult {
  success: boolean;
  data?: TranslationData;
  error?: string;
  metrics: TranslationMetrics;
  validation: {
    isValid: boolean;
    issues: string[];
    qualityScore: number;
  };
}

/**
 * Translation validation result
 */
export interface ValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  buttonText: string;
}

/**
 * Translation options
 */
export interface TranslationOptions {
  temperature?: number;
  includeProcessingMetadata?: boolean;
  timeout?: number;
}

/**
 * Collection-specific field mapping
 */
interface CollectionFieldMapping {
  titleField: string;
  contentField: string;
  summaryField?: string;
  keyInsightsField?: string;
  keywordsField?: string;
  germanTitleField: string;
  germanContentField: string;
  germanSummaryField?: string;
  hasTranslationFlag: string;
}

/**
 * Field mappings for supported collections
 */
const COLLECTION_FIELD_MAPPINGS: Record<
  TranslationCollection,
  CollectionFieldMapping
> = {
  articles: {
    titleField: 'englishTab.enhancedTitle',
    contentField: 'englishTab.enhancedContent',
    summaryField: 'englishTab.enhancedSummary',
    keyInsightsField: 'englishTab.enhancedKeyInsights',
    keywordsField: 'englishTab.keywords',
    germanTitleField: 'germanTab.germanTitle',
    germanContentField: 'germanTab.germanContent',
    germanSummaryField: 'germanTab.germanSummary',
    hasTranslationFlag: 'hasGermanTranslation',
  },
  pages: {
    titleField: 'englishTab.title',
    contentField: 'englishTab.content',
    germanTitleField: 'germanTab.germanTitle',
    germanContentField: 'germanTab.germanContent',
    hasTranslationFlag: 'hasGermanTranslation',
  },
};

/**
 * Generic Translation Service Class
 */
export class TranslationService {
  /**
   * Validate content for translation (collection-specific validation)
   */
  validateForTranslation(content: TranslatableContent): ValidationResult {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Collection-specific validation
    if (content.collection === 'articles') {
      // Articles need more substantial content
      if (!content.title || content.title.length < 20) {
        errors.push('Article title must be at least 20 characters long');
      }

      if (!content.content) {
        errors.push('Article content is required for translation');
      }

      // Check if content has substance for articles
      const hasSubstantiveContent =
        content.title?.length >= 20 ||
        (content.summary && content.summary.length >= 20);

      if (!hasSubstantiveContent) {
        errors.push(
          'Article content must have at least 20 characters for translation'
        );
      }
    } else if (content.collection === 'pages') {
      // Pages can have shorter titles but still need basic content
      if (!content.title || content.title.length < 3) {
        errors.push('Page title must be at least 3 characters long');
      }

      if (!content.content) {
        errors.push('Content is required for translation');
      }

      // Pages need minimum content length - check total content length
      const totalContentLength =
        (content.title?.length || 0) + (content.summary?.length || 0);

      if (content.content && totalContentLength < 20) {
        errors.push('Content must have at least 20 characters for translation');
      }
    }

    // General warnings
    if (content.title && content.title.length < 20) {
      warnings.push('Short titles may not translate well');
    }

    const isValid = errors.length === 0;
    const hasExistingTranslation = content.hasGermanTranslation;

    return {
      isValid,
      errors,
      warnings,
      buttonText: hasExistingTranslation
        ? 'Re-Translate to German'
        : 'Translate to German',
    };
  }

  /**
   * Extract content from collection document based on field mapping
   */
  extractContent(
    document: any,
    collection: TranslationCollection
  ): TranslatableContent {
    const mapping = COLLECTION_FIELD_MAPPINGS[collection];

    // Navigate nested object paths (e.g., 'englishTab.enhancedTitle')
    const getNestedValue = (obj: any, path: string): any => {
      return path.split('.').reduce((current, key) => current?.[key], obj);
    };

    const title =
      getNestedValue(document, mapping.titleField) || document.title;
    const content = getNestedValue(document, mapping.contentField);
    const summary = mapping.summaryField
      ? getNestedValue(document, mapping.summaryField)
      : undefined;
    const hasGermanTranslation =
      getNestedValue(document, mapping.hasTranslationFlag) || false;

    // Extract key insights and keywords for articles
    const keyInsights = mapping.keyInsightsField
      ? getNestedValue(document, mapping.keyInsightsField)
      : undefined;
    const keywords = mapping.keywordsField
      ? getNestedValue(document, mapping.keywordsField)
      : undefined;

    return {
      id: document.id,
      collection,
      title,
      content,
      summary,
      keyInsights,
      keywords,
      hasGermanTranslation,
      // Include raw document for additional context
      _document: document,
    };
  }

  /**
   * Prepare content for OpenAI translation
   */
  private prepareTranslationInput(content: TranslatableContent) {
    // Convert Lexical content to HTML for translation
    const contentHTML = content.content ? lexicalToHTML(content.content) : '';

    // Extract key insights - convert from PayloadCMS array format to simple strings
    const keyInsights = content.keyInsights
      ? content.keyInsights
          .map(item => (typeof item === 'string' ? item : item.insight || ''))
          .filter(insight => insight.trim() !== '')
      : [];

    // Extract keywords - convert from PayloadCMS array format to simple strings
    const keywords = content.keywords
      ? content.keywords
          .map(item => (typeof item === 'string' ? item : item.keyword || ''))
          .filter(keyword => keyword.trim() !== '')
      : [];

    // DEBUG: Log key translation metrics including extracted fields
    console.log('🔍 Translation input prepared:', {
      hasImages: contentHTML.includes('<img'),
      contentLength: contentHTML.length,
      keyInsightsCount: keyInsights.length,
      keywordsCount: keywords.length,
      collection: content.collection,
    });

    console.log('🔍 Extracted key insights for translation:', keyInsights);
    console.log('🔍 Extracted keywords for translation:', keywords);

    return {
      title: content.title,
      summary: content.summary || '',
      content: contentHTML,
      keyInsights,
      keywords,
    };
  }

  /**
   * Process translation result for collection
   */
  private async processTranslationResult(
    translationResult: any,
    collection: TranslationCollection
  ): Promise<TranslationData> {
    console.log('🔍 TRANSLATION DEBUG: Processing translation result...');
    console.log(
      '🔍 TRANSLATION DEBUG: Translation result keys:',
      Object.keys(translationResult)
    );
    console.log(
      '🔍 TRANSLATION DEBUG: German key insights:',
      translationResult.germanKeyInsights
    );
    console.log(
      '🔍 TRANSLATION DEBUG: German keywords:',
      translationResult.germanKeywords
    );

    const { germanTitle, germanSummary, germanContent } = translationResult;

    // Convert HTML back to Lexical format using enhanced conversion that preserves uploads
    // Use the WORKING enhanced converter that preserves uploads
    const htmlResult = await htmlToLexicalWithUploadPreservation(germanContent);

    if (!htmlResult.metrics.success) {
      console.warn(
        'Enhanced HTML→Lexical conversion failed, trying fallback...'
      );

      // Fallback to standard conversion
      const fallbackResult = await htmlToLexical(germanContent);
      if (!fallbackResult.metrics.success) {
        throw new Error(
          'Both enhanced and standard HTML→Lexical conversion failed'
        );
      }

      console.log('✅ Fallback HTML→Lexical conversion successful');

      const result: TranslationData = {
        germanTitle,
        germanContent: fallbackResult.result,
      };

      // Add optional fields if they exist
      if (germanSummary) {
        result.germanSummary = germanSummary;
      }

      // Articles-specific fields
      if (collection === 'articles' && translationResult.germanKeyInsights) {
        result.germanKeyInsights = translationResult.germanKeyInsights;
      }

      if (collection === 'articles' && translationResult.germanKeywords) {
        result.germanKeywords = translationResult.germanKeywords;
      }

      return result;
    }

    console.log(
      '✅ Enhanced HTML→Lexical conversion successful - uploads preserved'
    );

    // DEBUG: Check what's actually in the enhanced result
    const resultString = JSON.stringify(htmlResult.result);
    console.log(
      '🔍 Enhanced result contains upload nodes:',
      resultString.includes('"type":"upload"')
    );

    const result: TranslationData = {
      germanTitle,
      germanContent: htmlResult.result,
    };

    // Add optional fields if they exist
    if (germanSummary) {
      result.germanSummary = germanSummary;
    }

    // Articles-specific fields
    if (collection === 'articles' && translationResult.germanKeyInsights) {
      console.log(
        '✅ TRANSLATION DEBUG: Adding German key insights to result:',
        translationResult.germanKeyInsights
      );
      result.germanKeyInsights = translationResult.germanKeyInsights;
    } else if (collection === 'articles') {
      console.log(
        '⚠️ TRANSLATION DEBUG: No German key insights found in translation result'
      );
    }

    if (collection === 'articles' && translationResult.germanKeywords) {
      console.log(
        '✅ TRANSLATION DEBUG: Adding German keywords to result:',
        translationResult.germanKeywords
      );
      result.germanKeywords = translationResult.germanKeywords;
    } else if (collection === 'articles') {
      console.log(
        '⚠️ TRANSLATION DEBUG: No German keywords found in translation result'
      );
    }

    return result;
  }

  /**
   * Main translation function
   */
  async translateContent(
    content: TranslatableContent,
    options: TranslationOptions = {}
  ): Promise<TranslationResult> {
    const startTime = Date.now();

    try {
      // Validate content first
      const validation = this.validateForTranslation(content);
      if (!validation.isValid) {
        return {
          success: false,
          error: `Validation failed: ${validation.errors.join(', ')}`,
          metrics: {
            processingTime: Date.now() - startTime,
            tokenUsage: 0,
            costEstimate: 0,
          },
          validation: {
            isValid: false,
            issues: validation.errors,
            qualityScore: 0,
          },
        };
      }

      // Prepare input for OpenAI
      const translationInput = this.prepareTranslationInput(content);

      // Call OpenAI translation service
      const translationResult = await translateToGerman(translationInput, {
        temperature: options.temperature || 0.3,
        includeProcessingMetadata: true,
      });

      if (!translationResult.success || !translationResult.data) {
        return {
          success: false,
          error: translationResult.error || 'Translation service failed',
          metrics: translationResult.metrics,
          validation: translationResult.validation,
        };
      }

      // Process result for the specific collection
      const processedData = await this.processTranslationResult(
        translationResult.data,
        content.collection
      );

      return {
        success: true,
        data: processedData,
        metrics: translationResult.metrics,
        validation: translationResult.validation,
      };
    } catch (error) {
      return {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown translation error',
        metrics: {
          processingTime: Date.now() - startTime,
          tokenUsage: 0,
          costEstimate: 0,
        },
        validation: {
          isValid: false,
          issues: [error instanceof Error ? error.message : 'Unknown error'],
          qualityScore: 0,
        },
      };
    }
  }

  /**
   * Get field mapping for collection
   */
  getFieldMapping(collection: TranslationCollection): CollectionFieldMapping {
    return COLLECTION_FIELD_MAPPINGS[collection];
  }

  /**
   * Create update data structure for PayloadCMS
   */
  createUpdateData(
    translationData: TranslationData,
    collection: TranslationCollection
  ): Record<string, any> {
    const mapping = this.getFieldMapping(collection);
    const updateData: Record<string, any> = {};

    // Set the translation flag
    updateData[mapping.hasTranslationFlag] = true;

    // Create nested structure for tab fields
    if (mapping.germanTitleField.includes('.')) {
      const [tabName, fieldName] = mapping.germanTitleField.split('.');
      updateData[tabName] = updateData[tabName] || {};
      updateData[tabName][fieldName] = translationData.germanTitle;
    } else {
      updateData[mapping.germanTitleField] = translationData.germanTitle;
    }

    if (mapping.germanContentField.includes('.')) {
      const [tabName, fieldName] = mapping.germanContentField.split('.');
      updateData[tabName] = updateData[tabName] || {};
      updateData[tabName][fieldName] = translationData.germanContent;
    } else {
      updateData[mapping.germanContentField] = translationData.germanContent;
    }

    // Add summary if supported
    if (mapping.germanSummaryField && translationData.germanSummary) {
      if (mapping.germanSummaryField.includes('.')) {
        const [tabName, fieldName] = mapping.germanSummaryField.split('.');
        updateData[tabName] = updateData[tabName] || {};
        updateData[tabName][fieldName] = translationData.germanSummary;
      } else {
        updateData[mapping.germanSummaryField] = translationData.germanSummary;
      }
    }

    // Articles-specific fields
    console.log('🔍 UPDATE DEBUG: Processing articles-specific fields...');
    console.log(
      '🔍 UPDATE DEBUG: Translation data keys:',
      Object.keys(translationData)
    );
    console.log(
      '🔍 UPDATE DEBUG: German key insights in translationData:',
      translationData.germanKeyInsights
    );
    console.log(
      '🔍 UPDATE DEBUG: German keywords in translationData:',
      translationData.germanKeywords
    );

    if (collection === 'articles' && translationData.germanKeyInsights) {
      console.log(
        '✅ UPDATE DEBUG: Adding German key insights to updateData:',
        translationData.germanKeyInsights
      );
      updateData.germanTab = updateData.germanTab || {};
      updateData.germanTab.germanKeyInsights =
        translationData.germanKeyInsights.map((insight: string) => ({
          insight,
        }));
    } else if (collection === 'articles') {
      console.log(
        '⚠️ UPDATE DEBUG: No German key insights found in translationData'
      );
    }

    if (collection === 'articles' && translationData.germanKeywords) {
      console.log(
        '✅ UPDATE DEBUG: Adding German keywords to updateData:',
        translationData.germanKeywords
      );
      updateData.germanTab = updateData.germanTab || {};
      updateData.germanTab.germanKeywords = translationData.germanKeywords.map(
        (keyword: string) => ({ keyword })
      );
    } else if (collection === 'articles') {
      console.log(
        '⚠️ UPDATE DEBUG: No German keywords found in translationData'
      );
    }

    console.log(
      '🔍 UPDATE DEBUG: Final updateData structure:',
      JSON.stringify(updateData, null, 2)
    );
    return updateData;
  }
}

/**
 * Singleton instance for reuse
 */
export const translationService = new TranslationService();
