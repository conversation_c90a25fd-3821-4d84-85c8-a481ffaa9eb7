import { getPayload } from 'payload';
import config from '@payload-config';
import {
  generatePipelineEmailSubject,
  generatePipelineEmailContent,
  generatePipelineErrorEmail,
  type PipelineResult,
  type ArticleSummary,
} from './email-templates';

/**
 * Email Reporting Service for Content Pipeline
 *
 * Provides comprehensive email notifications for content pipeline operations
 * including processing summaries, article links, and actionable insights.
 *
 * Now uses shared email template components for consistent styling across all pipelines.
 */

// Re-export types and functions for backward compatibility
export type { PipelineResult, ArticleSummary };
export {
  generatePipelineEmailSubject,
  generatePipelineEmailContent,
  generatePipelineErrorEmail,
};

/**
 * Send comprehensive pipeline completion email
 */
export async function sendPipelineReport(
  pipelineType:
    | 'test-content-pipeline'
    | 'production-pipeline'
    | 'single-pipeline',
  result: PipelineResult,
  baseUrl: string = 'http://localhost:3001'
): Promise<void> {
  try {
    console.log('📧 Generating pipeline email report...');

    const payload = await getPayload({ config });

    // Get draft articles (using native PayloadCMS status, not editorial workflow)
    const draftArticles = await payload.find({
      collection: 'articles',
      where: { _status: { equals: 'draft' } },
      limit: 50,
      sort: '-createdAt',
    });

    // Get recently accepted articles from this run
    const acceptedIds = result.details
      .filter(d => d.status === 'accepted' && d.articleId)
      .map(d => d.articleId!);

    let newArticles: ArticleSummary[] = [];
    if (acceptedIds.length > 0) {
      const newArticleResults = await payload.find({
        collection: 'articles',
        where: {
          id: { in: acceptedIds },
        },
        limit: 50,
      });

      newArticles = newArticleResults.docs.map((article: any) => ({
        id: article.id.toString(),
        title: article.title || 'Untitled Article',
        status: article._status || 'unknown', // Use native PayloadCMS status
        createdAt: article.createdAt,
        sourceUrl: article.sourceUrl,
        adminUrl: `${baseUrl}/admin/collections/articles/${article.id}`,
      }));
    }

    // Generate email content using shared templates
    const emailSubject = generatePipelineEmailSubject(pipelineType, result);
    const emailContent = generatePipelineEmailContent(
      pipelineType,
      result,
      newArticles,
      draftArticles.docs.map((article: any) => ({
        id: article.id.toString(),
        title: article.title || 'Untitled Article',
        status: article._status || 'unknown', // Use native PayloadCMS status instead of workflowStage
        createdAt: article.createdAt,
        sourceUrl: article.sourceUrl,
        adminUrl: `${baseUrl}/admin/collections/articles/${article.id}`,
      })),
      baseUrl
    );

    // Send email using PayloadCMS email system
    // Support multiple email addresses (comma-separated)
    const recipients =
      process.env.PIPELINE_NOTIFICATION_EMAIL ||
      process.env.RESEND_DEFAULT_FROM_ADDRESS ||
      '<EMAIL>';

    const emailAddresses = recipients
      .split(',')
      .map(email => email.trim())
      .filter(email => email);

    await payload.sendEmail({
      to: emailAddresses,
      subject: emailSubject,
      html: emailContent,
    });

    console.log('✅ Pipeline email report sent successfully');
  } catch (error) {
    console.error('❌ Failed to send pipeline email report:', error);
    // Don't throw - email failure shouldn't break the pipeline
  }
}

/**
 * Send error notification email
 */
export async function sendPipelineErrorEmail(
  pipelineType: string,
  error: Error,
  baseUrl: string = 'http://localhost:3001'
): Promise<void> {
  try {
    const payload = await getPayload({ config });

    // Support multiple email addresses (comma-separated)
    const recipients =
      process.env.PIPELINE_NOTIFICATION_EMAIL ||
      process.env.RESEND_DEFAULT_FROM_ADDRESS ||
      '<EMAIL>';

    const emailAddresses = recipients
      .split(',')
      .map(email => email.trim())
      .filter(email => email);

    await payload.sendEmail({
      to: emailAddresses,
      subject: `❌ Pipeline Failed: ${pipelineType}`,
      html: generatePipelineErrorEmail(pipelineType, error, baseUrl),
    });

    console.log('✅ Pipeline error email sent');
  } catch (emailError) {
    console.error('❌ Failed to send pipeline error email:', emailError);
  }
}
