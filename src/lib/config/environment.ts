/**
 * Environment Configuration
 *
 * Centralised configuration for environment-specific settings,
 * including domain detection and URL generation.
 */

export type Environment = 'development' | 'preview' | 'production';

interface EnvironmentConfig {
  name: Environment;
  domain: string;
  isSecure: boolean;
}

/**
 * Detect current environment based on hostname and environment variables
 */
export const detectEnvironment = (): Environment => {
  // Server-side environment detection
  if (typeof window === 'undefined') {
    if (process.env.NODE_ENV === 'development') return 'development';
    if (process.env.VERCEL_ENV === 'preview') return 'preview';
    return 'production';
  }

  // Client-side environment detection
  const hostname = window.location.hostname;

  if (hostname.includes('localhost') || hostname.includes('127.0.0.1')) {
    return 'development';
  }

  if (hostname.includes('vercel.app') || hostname.includes('preview')) {
    return 'preview';
  }

  return 'production';
};

/**
 * Get domain configuration for current environment
 */
export const getEnvironmentConfig = (): EnvironmentConfig => {
  const env = detectEnvironment();

  switch (env) {
    case 'development':
      return {
        name: 'development',
        domain: process.env.NEXT_PUBLIC_DEV_DOMAIN || 'http://localhost:3000',
        isSecure: false,
      };

    case 'preview':
      return {
        name: 'preview',
        domain:
          typeof window !== 'undefined'
            ? `https://${window.location.hostname}`
            : process.env.VERCEL_URL
              ? `https://${process.env.VERCEL_URL}`
              : 'https://preview.boersenblick.com',
        isSecure: true,
      };

    case 'production':
    default:
      return {
        name: 'production',
        domain:
          process.env.NEXT_PUBLIC_PRODUCTION_DOMAIN ||
          'https://boersenblick.com',
        isSecure: true,
      };
  }
};

/**
 * Generate live URL for a given collection and slug
 */
export const generateLiveUrl = (
  collection: 'articles' | 'pages' | string,
  slug: string,
  customPathPattern?: string
): string => {
  const config = getEnvironmentConfig();
  const baseDomain = config.domain;

  if (customPathPattern) {
    return `${baseDomain}${customPathPattern.replace('[slug]', slug)}`;
  }

  // Default URL patterns for each collection
  switch (collection) {
    case 'articles':
      return `${baseDomain}/artikel/${slug}`;
    case 'pages':
      return `${baseDomain}/${slug}`;
    default:
      return `${baseDomain}/${collection}/${slug}`;
  }
};

/**
 * Check if we're in a secure environment (HTTPS)
 */
export const isSecureEnvironment = (): boolean => {
  return getEnvironmentConfig().isSecure;
};

/**
 * Get environment-specific configuration for admin components
 */
export const getAdminConfig = () => {
  const config = getEnvironmentConfig();

  return {
    environment: config.name,
    domain: config.domain,
    showEnvironmentBadge: config.name !== 'production',
    enableDebugMode: config.name === 'development',
  };
};
