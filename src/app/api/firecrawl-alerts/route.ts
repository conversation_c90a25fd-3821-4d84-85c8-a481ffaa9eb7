/**
 * Firecrawl Alerts API Endpoint
 * Provides access to Firecrawl alerting data and health status
 *
 * <AUTHOR> Development Team
 * @created 2025-06-30
 */

import { NextResponse } from 'next/server';
import { firecrawlAlerting } from '@/lib/integrations/firecrawl/alerting';

export async function GET(request: Request) {
  const { searchParams } = new URL(request.url);
  const action = searchParams.get('action') || 'summary';
  const hours = parseInt(searchParams.get('hours') || '24');

  try {
    switch (action) {
      case 'summary':
        return NextResponse.json({
          success: true,
          data: firecrawlAlerting.getAlertSummary(),
          timestamp: new Date().toISOString(),
        });

      case 'recent':
        return NextResponse.json({
          success: true,
          data: firecrawlAlerting.getRecentAlerts(hours),
          timestamp: new Date().toISOString(),
        });

      case 'health': {
        const summary = firecrawlAlerting.getAlertSummary();
        const health = {
          status: determineHealthStatus(summary),
          failureRate: summary.failureRate,
          consecutiveFailures: summary.consecutiveFailures,
          criticalAlerts: summary.bySeverity.critical || 0,
          highAlerts: summary.bySeverity.high || 0,
          recommendations: generateRecommendations(summary),
        };

        return NextResponse.json({
          success: true,
          data: health,
          timestamp: new Date().toISOString(),
        });
      }

      case 'dashboard': {
        const dashboardData = {
          summary: firecrawlAlerting.getAlertSummary(),
          recentAlerts: firecrawlAlerting.getRecentAlerts(24),
          health: determineHealthStatus(firecrawlAlerting.getAlertSummary()),
        };

        return NextResponse.json({
          success: true,
          data: dashboardData,
          timestamp: new Date().toISOString(),
        });
      }

      default:
        return NextResponse.json(
          {
            success: false,
            error: 'Invalid action. Use: summary, recent, health, or dashboard',
          },
          { status: 400 }
        );
    }
  } catch (error: any) {
    console.error('Firecrawl alerts API error:', error);
    return NextResponse.json(
      {
        success: false,
        error: error.message || 'Failed to retrieve Firecrawl alerts',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

function determineHealthStatus(
  summary: any
): 'healthy' | 'warning' | 'critical' {
  // Critical if we have critical alerts or very high failure rate
  if (summary.bySeverity.critical > 0 || summary.failureRate > 50) {
    return 'critical';
  }

  // Warning if we have high alerts, moderate failure rate, or consecutive failures
  if (
    summary.bySeverity.high > 0 ||
    summary.failureRate > 25 ||
    summary.consecutiveFailures >= 3
  ) {
    return 'warning';
  }

  return 'healthy';
}

function generateRecommendations(summary: any): string[] {
  const recommendations: string[] = [];

  if (summary.bySeverity.critical > 0) {
    recommendations.push(
      '🔴 Critical alerts detected - immediate attention required'
    );
  }

  if (summary.failureRate > 50) {
    recommendations.push(
      '🔴 Very high failure rate - check Firecrawl API status and configuration'
    );
  } else if (summary.failureRate > 25) {
    recommendations.push(
      '🟡 High failure rate - monitor closely and consider fallback strategies'
    );
  }

  if (summary.consecutiveFailures >= 5) {
    recommendations.push(
      '🔴 Multiple consecutive failures - check API key and network connectivity'
    );
  } else if (summary.consecutiveFailures >= 3) {
    recommendations.push(
      '🟡 Consecutive failures detected - monitor for patterns'
    );
  }

  if (summary.byType.rate_limited > 0) {
    recommendations.push(
      '⏳ Rate limiting detected - consider implementing request throttling'
    );
  }

  if (summary.byType.configuration_error > 0) {
    recommendations.push(
      '⚙️ Configuration errors detected - review site-specific settings'
    );
  }

  if (summary.byType.timeout > 0) {
    recommendations.push(
      '⏱️ Timeout errors detected - consider increasing timeout values'
    );
  }

  if (recommendations.length === 0) {
    recommendations.push('✅ Firecrawl is operating normally');
  }

  return recommendations;
}
