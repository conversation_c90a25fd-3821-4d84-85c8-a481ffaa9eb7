/**
 * Health Check API Endpoint
 *
 * REST API endpoint for system health monitoring and status assessment.
 * Provides detailed component status, dependency health, and overall
 * system health metrics for monitoring dashboards and alerting systems.
 *
 * Endpoints:
 * - GET /api/health - Overall system health status
 * - GET /api/health?detailed=true - Detailed health information
 * - GET /api/health?component=name - Specific component health
 *
 * <AUTHOR> Development Team
 * @created 2025-06-29
 * @sprint Sprint 6: Monitoring & Production Readiness
 */

import { type NextRequest, NextResponse } from 'next/server';
import { getSystemHealth, HealthStatus } from '@/lib/monitoring/health-checks';
import { getPerformanceStats } from '@/lib/monitoring/performance-monitor';
import { getGlobalErrorStats } from '@/lib/monitoring/logger';

/**
 * GET /api/health
 *
 * Returns system health status with optional detailed information
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const detailed = searchParams.get('detailed') === 'true';
    const component = searchParams.get('component');
    const format = searchParams.get('format') || 'json';

    // Get system health
    const systemHealth = await getSystemHealth();

    // If specific component requested
    if (component) {
      const componentHealth =
        systemHealth.components.find(c => c.name === component) ||
        systemHealth.dependencies.find(d => d.name === component);

      if (!componentHealth) {
        return NextResponse.json(
          { error: `Component '${component}' not found` },
          { status: 404 }
        );
      }

      return NextResponse.json(componentHealth, {
        status:
          componentHealth.status === HealthStatus.HEALTHY
            ? 200
            : componentHealth.status === HealthStatus.DEGRADED
              ? 200
              : 503,
      });
    }

    // Determine HTTP status based on health
    const httpStatus =
      systemHealth.status === HealthStatus.HEALTHY
        ? 200
        : systemHealth.status === HealthStatus.DEGRADED
          ? 200
          : 503;

    // Basic health response
    if (!detailed) {
      const basicResponse = {
        status: systemHealth.status,
        score: systemHealth.score,
        message: systemHealth.message,
        timestamp: systemHealth.timestamp,
        summary: systemHealth.summary,
      };

      if (format === 'prometheus') {
        return new NextResponse(formatPrometheusMetrics(systemHealth), {
          status: httpStatus,
          headers: { 'Content-Type': 'text/plain' },
        });
      }

      return NextResponse.json(basicResponse, { status: httpStatus });
    }

    // Detailed health response
    const performanceStats = getPerformanceStats(60 * 60 * 1000); // Last hour
    const errorStats = getGlobalErrorStats();

    const detailedResponse = {
      ...systemHealth,
      performance: {
        avgProcessingTime: performanceStats.avgProcessingTime,
        successRate: performanceStats.successRate,
        errorRate: performanceStats.errorRate,
        cacheHitRate: performanceStats.cacheHitRate,
        sampleCount: performanceStats.sampleCount,
      },
      errors: {
        total: errorStats.total,
        byCategory: errorStats.byCategory,
        byComponent: errorStats.byComponent,
      },
      system: {
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        version: process.version,
        platform: process.platform,
      },
    };

    return NextResponse.json(detailedResponse, { status: httpStatus });
  } catch (error) {
    console.error('❌ Health check API error:', error);

    const errorResponse = {
      status: HealthStatus.UNHEALTHY,
      score: 0,
      message: 'Health check failed',
      timestamp: new Date(),
      error: error instanceof Error ? error.message : 'Unknown error',
    };

    return NextResponse.json(errorResponse, { status: 500 });
  }
}

/**
 * POST /api/health
 *
 * Trigger manual health check (force refresh)
 */
export async function POST(request: NextRequest) {
  try {
    // Force fresh health check
    const systemHealth = await getSystemHealth(false);

    const httpStatus =
      systemHealth.status === HealthStatus.HEALTHY
        ? 200
        : systemHealth.status === HealthStatus.DEGRADED
          ? 200
          : 503;

    return NextResponse.json(
      {
        ...systemHealth,
        message: 'Health check completed',
      },
      { status: httpStatus }
    );
  } catch (error) {
    console.error('❌ Manual health check failed:', error);

    return NextResponse.json(
      {
        status: HealthStatus.UNHEALTHY,
        score: 0,
        message: 'Manual health check failed',
        timestamp: new Date(),
        error: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Format health metrics for Prometheus monitoring
 */
function formatPrometheusMetrics(systemHealth: any): string {
  const metrics: string[] = [];

  // System health score
  metrics.push(
    `# HELP system_health_score Overall system health score (0-100)`
  );
  metrics.push(`# TYPE system_health_score gauge`);
  metrics.push(`system_health_score ${systemHealth.score}`);

  // System health status
  metrics.push(
    `# HELP system_health_status System health status (0=unknown, 1=healthy, 2=degraded, 3=unhealthy)`
  );
  metrics.push(`# TYPE system_health_status gauge`);
  const statusValue =
    systemHealth.status === HealthStatus.HEALTHY
      ? 1
      : systemHealth.status === HealthStatus.DEGRADED
        ? 2
        : systemHealth.status === HealthStatus.UNHEALTHY
          ? 3
          : 0;
  metrics.push(`system_health_status ${statusValue}`);

  // Component health scores
  metrics.push(`# HELP component_health_score Component health score (0-100)`);
  metrics.push(`# TYPE component_health_score gauge`);
  systemHealth.components.forEach((component: any) => {
    metrics.push(
      `component_health_score{component="${component.name}"} ${component.score}`
    );
  });

  // Dependency health scores
  metrics.push(
    `# HELP dependency_health_score Dependency health score (0-100)`
  );
  metrics.push(`# TYPE dependency_health_score gauge`);
  systemHealth.dependencies.forEach((dependency: any) => {
    metrics.push(
      `dependency_health_score{dependency="${dependency.name}"} ${dependency.score}`
    );
  });

  // Component counts
  metrics.push(`# HELP healthy_components_total Number of healthy components`);
  metrics.push(`# TYPE healthy_components_total gauge`);
  metrics.push(
    `healthy_components_total ${systemHealth.summary.healthyComponents}`
  );

  metrics.push(
    `# HELP degraded_components_total Number of degraded components`
  );
  metrics.push(`# TYPE degraded_components_total gauge`);
  metrics.push(
    `degraded_components_total ${systemHealth.summary.degradedComponents}`
  );

  metrics.push(
    `# HELP unhealthy_components_total Number of unhealthy components`
  );
  metrics.push(`# TYPE unhealthy_components_total gauge`);
  metrics.push(
    `unhealthy_components_total ${systemHealth.summary.unhealthyComponents}`
  );

  return metrics.join('\n') + '\n';
}
