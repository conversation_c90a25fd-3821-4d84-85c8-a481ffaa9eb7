/**
 * Articles Translation API Route
 *
 * Provides German translation functionality for Articles collection
 * using the enhanced translation handler with upload node preservation.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 * @updated Uses enhanced converter to preserve images during translation
 */

import { NextRequest, NextResponse } from 'next/server';
import { handleTranslationRequest } from '@/lib/api/translation-handler';
import { withAuth } from '@/lib/auth/simple-auth';

/**
 * Internal POST handler (before auth wrapper)
 */
async function _POST(request: NextRequest): Promise<NextResponse> {
  try {
    // Use the generic translation handler with enhanced converter for Articles collection
    return await handleTranslationRequest(request, 'articles');
  } catch (error) {
    console.error('❌ Error in Articles translation route:', error);

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error ? error.message : 'Unknown error occurred',
      },
      { status: 500 }
    );
  }
}

/**
 * Export authenticated POST route
 * Articles translation requires authentication (only editors can translate)
 */
export const POST = withAuth(_POST);
