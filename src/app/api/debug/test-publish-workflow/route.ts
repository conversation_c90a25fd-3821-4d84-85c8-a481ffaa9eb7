/**
 * TEST PUBLISH WORKFLOW API
 *
 * Provides debugging tools to test the complete publish workflow:
 * 1. Database persistence verification
 * 2. Cache invalidation testing
 * 3. Frontend data retrieval testing
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 * @priority CRITICAL - Publish workflow debugging
 */

import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';
import { withAuth } from '@/lib/auth/simple-auth';

/**
 * Internal POST handler for publish workflow testing
 */
async function _POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const articleId = searchParams.get('articleId');
    const action = searchParams.get('action') || 'full-test';

    if (!articleId) {
      return NextResponse.json(
        { success: false, error: 'articleId parameter required' },
        { status: 400 }
      );
    }

    console.log(`🧪 TESTING PUBLISH WORKFLOW for article ${articleId}`);

    const payload = await getPayload({ config });
    const results: any = {
      timestamp: new Date().toISOString(),
      articleId,
      action,
      tests: {},
    };

    // Test 1: Article data access (using PayloadCMS native methods)
    try {
      console.log('🔍 Test 1: Article data access...');
      const article = await payload.findByID({
        collection: 'articles',
        id: articleId,
      });

      results.tests.articleAccess = {
        success: !!article,
        status: (article as any)?._status,
        title: article?.title,
        slug: article?.slug,
        hasContent:
          !!(article as any)?.germanTab?.germanContent ||
          !!(article as any)?.englishTab?.enhancedContent,
      };

      console.log('✅ Article access test:', results.tests.articleAccess);
    } catch (error) {
      results.tests.articleAccess = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      console.error('❌ Article access test failed:', error);
    }

    // Test 2: Cache status check
    try {
      console.log('🔍 Test 2: Cache invalidation status...');
      const { validateCacheHealth } = await import(
        '@/lib/cache/vercel-cache-fix'
      );
      const cacheHealth = await validateCacheHealth();

      results.tests.cacheHealth = cacheHealth;
      console.log('✅ Cache health test:', cacheHealth);
    } catch (error) {
      results.tests.cacheHealth = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      console.error('❌ Cache health test failed:', error);
    }

    // Test 3: Cache behavior test
    try {
      console.log('🔍 Test 3: Cache behavior test...');
      const { getCachedDocument } = await import('@/utilities/getDocument');

      if (results.tests.articleAccess?.slug) {
        const getCachedDoc = getCachedDocument(
          'articles',
          results.tests.articleAccess.slug,
          2
        );
        const cachedResult = await getCachedDoc();

        results.tests.cacheBehavior = {
          success: !!cachedResult,
          isCacheWorking: !!cachedResult,
          cacheTimestamp: new Date().toISOString(),
        };

        console.log('✅ Cache behavior test:', results.tests.cacheBehavior);
      } else {
        results.tests.cacheBehavior = {
          success: false,
          error: 'No slug available from article access test',
        };
      }
    } catch (error) {
      results.tests.cacheBehavior = {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
      };
      console.error('❌ Cache behavior test failed:', error);
    }

    // Test 4: Publish workflow simulation (if action is simulate-publish)
    if (action === 'simulate-publish' && results.tests.articleAccess?.success) {
      try {
        console.log('🔍 Test 4: Simulating publish workflow...');

        const article = results.tests.articleAccess;
        if (article.status !== 'published') {
          console.log('⚠️ Article is not published, simulating publish...');

          // Simulate what happens during publish
          const { comprehensiveArticleInvalidation } = await import(
            '@/lib/cache/vercel-cache-fix'
          );

          await comprehensiveArticleInvalidation(
            parseInt(articleId, 10),
            article.slug,
            [], // categories would be here in real scenario
            {
              isNewArticle: false,
              isPublished: true,
              forceEdgeInvalidation: true,
            }
          );

          results.tests.publishSimulation = {
            success: true,
            message: 'Cache invalidation simulation completed',
            timestamp: new Date().toISOString(),
          };

          console.log('✅ Publish simulation completed');
        } else {
          results.tests.publishSimulation = {
            success: true,
            message: 'Article already published - no simulation needed',
          };
        }
      } catch (error) {
        results.tests.publishSimulation = {
          success: false,
          error: error instanceof Error ? error.message : 'Unknown error',
        };
        console.error('❌ Publish simulation failed:', error);
      }
    }

    // Summary
    const successfulTests = Object.values(results.tests).filter(
      (test: any) => test.success
    ).length;
    const totalTests = Object.keys(results.tests).length;

    results.summary = {
      totalTests,
      successfulTests,
      failedTests: totalTests - successfulTests,
      overallSuccess: successfulTests === totalTests,
    };

    console.log(
      `🧪 PUBLISH WORKFLOW TEST COMPLETE: ${successfulTests}/${totalTests} tests passed`
    );

    return NextResponse.json({
      success: true,
      message: `Publish workflow test completed: ${successfulTests}/${totalTests} tests passed`,
      data: results,
    });
  } catch (error) {
    console.error('❌ Publish workflow test error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for quick status check
 */
async function _GET(request: NextRequest): Promise<NextResponse> {
  return NextResponse.json({
    success: true,
    message: 'Publish workflow test API ready',
    usage: {
      post: 'POST /api/debug/test-publish-workflow?articleId=123&action=full-test',
      actions: ['full-test', 'simulate-publish'],
    },
    timestamp: new Date().toISOString(),
  });
}

export const POST = withAuth(_POST);
export const GET = withAuth(_GET);
