/**
 * Debug endpoint to test enhanced HTML-to-Lexical conversion
 * Use this to validate image preservation without full translation
 */

import { NextRequest, NextResponse } from 'next/server';
import { enhancedHtmlToLexical } from '@/lib/utils/enhanced-html-to-lexical';
import { withAuth } from '@/lib/auth/simple-auth';

async function _POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { html } = await request.json();

    if (!html) {
      return NextResponse.json(
        { success: false, error: 'HTML parameter is required' },
        { status: 400 }
      );
    }

    console.log('🧪 Testing enhanced HTML-to-Lexical conversion...');
    console.log('📄 Input HTML:', html.substring(0, 500) + '...');

    const result = await enhancedHtmlToLexical(html);

    console.log('📊 Conversion metrics:', result.metrics);
    console.log('✅ Test conversion completed');

    return NextResponse.json({
      success: true,
      result: result.result,
      metrics: result.metrics,
      analysis: {
        hasImages: html.includes('<img'),
        preservedUploads: result.metrics.preservedUploads || 0,
        method: result.metrics.method,
        fallbackUsed: result.metrics.fallbackUsed,
        resultStructure: {
          hasRoot: !!(result.result as any)?.root,
          childrenCount: (result.result as any)?.root?.children?.length || 0,
          containsUploads: JSON.stringify(result.result).includes(
            '"type":"upload"'
          ),
        },
      },
    });
  } catch (error) {
    console.error('❌ Test conversion failed:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined,
      },
      { status: 500 }
    );
  }
}

// Export authenticated endpoint
export const POST = withAuth(_POST);
