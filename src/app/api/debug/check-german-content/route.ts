/**
 * Debug endpoint to check what's actually saved in German content
 */

import { NextRequest, NextResponse } from 'next/server';
import { getPayload } from 'payload';
import config from '@payload-config';
import { withAuth } from '@/lib/auth/simple-auth';

async function _POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { articleId } = await request.json();

    if (!articleId) {
      return NextResponse.json(
        { success: false, error: 'articleId is required' },
        { status: 400 }
      );
    }

    const payload = await getPayload({ config });

    // Get the article
    const article = await payload.findByID({
      collection: 'articles',
      id: articleId,
    });

    if (!article) {
      return NextResponse.json(
        { success: false, error: 'Article not found' },
        { status: 404 }
      );
    }

    console.log('🔍 GERMAN CONTENT DEBUG:');
    console.log('📄 Article:', article.title);

    // Check German content in germanTab
    const germanContent = article.germanTab?.germanContent;
    console.log('🔍 Has German Content:', !!germanContent);
    console.log('🔍 Has German Tab:', !!article.germanTab);
    console.log(
      '🔍 Has German Translation Flag:',
      !!article.hasGermanTranslation
    );

    if (germanContent) {
      console.log('📊 German Content Structure:');
      console.log(JSON.stringify(germanContent, null, 2));

      // Check for upload nodes
      const contentString = JSON.stringify(germanContent);
      const hasUploadNodes = contentString.includes('"type":"upload"');
      console.log('🔍 German content has upload nodes:', hasUploadNodes);

      if (hasUploadNodes) {
        console.log('✅ Upload nodes found in German content!');
      } else {
        console.log('❌ No upload nodes found in German content');
      }
    } else {
      console.log('❌ No German content found');
      console.log('🔍 Full article structure:');
      console.log(JSON.stringify(article, null, 2));
    }

    return NextResponse.json({
      success: true,
      data: {
        title: article.title,
        hasGermanContent: !!germanContent,
        hasGermanTab: !!article.germanTab,
        hasGermanTranslation: !!article.hasGermanTranslation,
        germanContentStructure: germanContent
          ? JSON.stringify(germanContent, null, 2)
          : null,
        hasUploadNodes: germanContent
          ? JSON.stringify(germanContent).includes('"type":"upload"')
          : false,
      },
    });
  } catch (error) {
    console.error('❌ Error checking German content:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export const POST = withAuth(_POST);
