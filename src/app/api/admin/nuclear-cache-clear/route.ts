/**
 * EMERGENCY: Nuclear Cache Clear API
 *
 * Provides emergency cache clearing when normal invalidation fails.
 * Accessible only to authenticated admin users.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 * @priority CRITICAL - Emergency cache clearing
 */

import { NextRequest, NextResponse } from 'next/server';
import { withAuth } from '@/lib/auth/simple-auth';
import {
  nuclearCacheInvalidation,
  validateCacheHealth,
  forceVercelCacheInvalidation,
} from '@/lib/cache/vercel-cache-fix';

/**
 * Internal POST handler for nuclear cache clearing
 */
async function _POST(request: NextRequest): Promise<NextResponse> {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'nuclear';

    console.log(`🚨 EMERGENCY CACHE CLEAR requested: ${action}`);

    let result;
    let message;

    switch (action) {
      case 'nuclear':
        result = await nuclearCacheInvalidation();
        message = 'Nuclear cache invalidation completed';
        break;

      case 'vercel-edge':
        result = await forceVercelCacheInvalidation([
          '/',
          '/artikel/[slug]',
          '/kategorien/[slug]',
          '/[slug]',
        ]);
        message = 'Vercel edge cache invalidation completed';
        break;

      case 'health-check':
        const health = await validateCacheHealth();
        return NextResponse.json({
          success: true,
          message: 'Cache health check completed',
          data: health,
        });

      default:
        return NextResponse.json(
          { success: false, error: 'Invalid action' },
          { status: 400 }
        );
    }

    if (result) {
      console.log(`✅ ${message}`);
      return NextResponse.json({
        success: true,
        message,
        timestamp: new Date().toISOString(),
        action,
      });
    } else {
      console.error(`❌ ${message} failed`);
      return NextResponse.json(
        {
          success: false,
          error: `${message} failed - check server logs`,
          timestamp: new Date().toISOString(),
          action,
        },
        { status: 500 }
      );
    }
  } catch (error) {
    console.error('❌ Nuclear cache clear API error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

/**
 * GET handler for cache health check
 */
async function _GET(request: NextRequest): Promise<NextResponse> {
  try {
    const health = await validateCacheHealth();

    return NextResponse.json({
      success: true,
      message: 'Cache health check completed',
      data: health,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Cache health check error:', error);

    return NextResponse.json(
      {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: new Date().toISOString(),
      },
      { status: 500 }
    );
  }
}

// Export authenticated handlers
export const POST = withAuth(_POST);
export const GET = withAuth(_GET);
