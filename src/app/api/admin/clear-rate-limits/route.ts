/**
 * Development Rate Limit Clear API
 *
 * Provides immediate relief from rate limiting issues during development.
 * Only works in development environment for security.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 */

import { NextRequest, NextResponse } from 'next/server';

/**
 * Clear rate limits for development work
 */
export async function POST(request: NextRequest) {
  // Only allow in development
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { success: false, error: 'Only available in development' },
      { status: 403 }
    );
  }

  try {
    // Import the clearRateLimits function
    const { clearRateLimits } = await import('@/middleware');

    // Get client IP for targeted clearing
    const clientIP =
      request.headers.get('x-forwarded-for')?.split(',')[0] ||
      request.headers.get('x-real-ip') ||
      'unknown';

    // Clear rate limits
    clearRateLimits(clientIP);

    console.log(
      `🧹 Rate limits cleared for development work (IP: ${clientIP})`
    );

    return NextResponse.json({
      success: true,
      message: 'Rate limits cleared successfully',
      clientIP,
      timestamp: new Date().toISOString(),
    });
  } catch (error) {
    console.error('❌ Failed to clear rate limits:', error);

    return NextResponse.json(
      {
        success: false,
        error: 'Failed to clear rate limits',
        details: error instanceof Error ? error.message : 'Unknown error',
      },
      { status: 500 }
    );
  }
}

/**
 * Get current rate limit status
 */
export async function GET() {
  if (process.env.NODE_ENV !== 'development') {
    return NextResponse.json(
      { success: false, error: 'Only available in development' },
      { status: 403 }
    );
  }

  return NextResponse.json({
    success: true,
    message: 'Rate limit clear API is available',
    usage: {
      clear: 'POST /api/admin/clear-rate-limits',
      description: 'Clears rate limits for your IP address during development',
    },
  });
}
