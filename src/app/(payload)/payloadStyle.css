/* MINIMAL PayloadCMS CSS - Horizontal scrolling preserved */

/*
 * CRITICAL: PayloadCMS table scrolling is extremely sensitive to ANY CSS interference
 * Even "safe" Tailwind utilities and CSS custom properties can break table layout
 * Keep this file absolutely minimal to preserve core functionality
 */

/* Animation for unified translate button spinner */
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}
