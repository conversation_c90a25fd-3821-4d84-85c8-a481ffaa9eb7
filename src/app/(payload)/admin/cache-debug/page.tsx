/**
 * Cache Debug Admin Page
 *
 * Dedicated admin page for cache debugging and emergency clearing.
 * Accessible via /admin/cache-debug
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 */

import React from 'react';
import { CacheDebugPanel } from '@/components/admin/cache-debugging/CacheDebugPanel';

export default function CacheDebugPage() {
  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900">Cache Debugging</h1>
          <p className="mt-2 text-gray-600">
            Diagnose and fix cache issues that prevent content updates from
            appearing on the frontend.
          </p>
        </div>

        <CacheDebugPanel />

        <div className="mt-8 bg-white border border-gray-200 rounded-lg p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            🧠 Understanding the Cache System
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h3 className="font-semibold text-gray-800 mb-2">
                Cache Layers (in order):
              </h3>
              <ol className="text-sm text-gray-600 space-y-1 list-decimal list-inside">
                <li>
                  <strong>Browser Cache</strong> - User&apos;s browser
                </li>
                <li>
                  <strong>Vercel Edge Cache</strong> - CDN layer (main culprit)
                </li>
                <li>
                  <strong>Next.js Cache</strong> - Application cache (5 min)
                </li>
                <li>
                  <strong>PayloadCMS Cache</strong> - Database queries
                </li>
                <li>
                  <strong>Database Cache</strong> - Supabase internal
                </li>
              </ol>
            </div>

            <div>
              <h3 className="font-semibold text-gray-800 mb-2">
                Common Symptoms:
              </h3>
              <ul className="text-sm text-gray-600 space-y-1 list-disc list-inside">
                <li>Changes don&apos;t appear after publishing</li>
                <li>Old images showing instead of new ones</li>
                <li>Text updates not reflecting</li>
                <li>Works sometimes but not others</li>
                <li>Incognito mode shows updates but normal doesn&apos;t</li>
              </ul>
            </div>
          </div>

          <div className="mt-6 p-4 bg-gray-50 rounded-lg">
            <p className="text-sm text-gray-700">
              <strong>💡 Pro Tip:</strong> Most issues are fixed by clearing the
              Vercel Edge Cache. This is the layer that sits in front of your
              Next.js app and can cache responses for hours even after your app
              cache is cleared.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}
