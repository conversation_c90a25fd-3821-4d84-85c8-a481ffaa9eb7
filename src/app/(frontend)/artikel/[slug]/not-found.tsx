import { NotFoundComponent } from '@/components/ui/not-found';

export default function ArticleNotFound() {
  return (
    <NotFoundComponent
      title="Artikel nicht gefunden"
      description="Der gesuchte Artikel existiert nicht oder wurde entfernt."
      reasons={[
        'Der Artikel wurde möglicherweise archiviert',
        'Die URL wurde falsch eingegeben',
        'Der Artikel ist noch nicht veröffentlicht',
      ]}
    />
  );
}
