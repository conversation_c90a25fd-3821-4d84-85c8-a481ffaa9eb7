import { SpeedInsights } from '@vercel/speed-insights/next';
import Script from 'next/script';
import './global.css';

import { ThemeProvider } from '@/components/theme-provider';
import HeaderNavigation from '@/components/navigation/header-navigation';
import FooterNavigation from '@/components/footer/FooterNavigation';
import { EnvironmentBanner } from '@/components/dev/EnvironmentBanner';
import { TickerTapeProvider } from '@/components/ticker-tape/TickerTapeContext';
import { LayoutTicker } from '@/components/ticker-tape/LayoutTicker';
import { AdminBarProvider, AdminBarClientWrapper } from '@/components/admin';
import { draftMode } from 'next/headers';

export default async function FrontendLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const draft = await draftMode();
  const isDraftMode = draft.isEnabled;

  return (
    <>
      {/* Prevent theme flash by applying theme before hydration */}
      <Script id="theme-script" strategy="beforeInteractive">
        {`
          (function() {
            function getTheme() {
              try {
                const stored = localStorage.getItem('theme');
                if (stored) return stored;
                
                // Check system preference
                if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
                  return 'dark';
                }
                return 'light';
              } catch (e) {
                return 'light';
              }
            }
            
            function applyTheme(theme) {
              const effectiveTheme = theme === 'system' 
                ? (window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light')
                : theme;
                
              if (effectiveTheme === 'dark') {
                document.documentElement.classList.add('dark');
              } else {
                document.documentElement.classList.remove('dark');
              }
            }
            
            applyTheme(getTheme());
          })();
        `}
      </Script>

      <Script id="gtm" strategy="beforeInteractive">
        {`(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
        new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
        j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
        'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
      })(window,document,'script','dataLayer','GTM-NV29SM87');`}
      </Script>
      <ThemeProvider
        attribute="class"
        defaultTheme="system"
        enableSystem
        disableTransitionOnChange
        storageKey="theme"
      >
        <AdminBarProvider>
          <TickerTapeProvider>
            <div className="min-h-dvh flex flex-col bg-background">
              {/* Admin bar positioned above ticker in document flow */}
              <AdminBarClientWrapper
                preview={isDraftMode}
                devMode={process.env.NODE_ENV === 'development'}
              />

              {/* Page-specific ticker above navigation */}
              <LayoutTicker />

              <header className="sticky top-0 z-50 w-full border-b border-t border-border/40 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
                <div className="flex h-16 items-center">
                  <HeaderNavigation />
                </div>
              </header>
              <main className="flex-1">{children}</main>
              <FooterNavigation />
            </div>
          </TickerTapeProvider>
        </AdminBarProvider>
        <EnvironmentBanner />
      </ThemeProvider>

      <SpeedInsights />
    </>
  );
}
