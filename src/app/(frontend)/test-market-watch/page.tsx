import { MarketWatch } from '@/components/market-watch';
import { MARKET_WATCH_TAB_SETS } from '@/components/ticker-tape/TickerTape.config';

export default function TestMarketWatchPage() {
  return (
    <div className="bg-background">
      {/* Header section in container */}
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            MarketWatch Component Demo
          </h1>
          <p className="text-muted-foreground">
            TradingView Market Overview widgets with different configurations
          </p>
        </div>
      </div>

      {/* Homepage - Default Configuration */}
      <section className="mb-12">
        <div className="container mx-auto px-4 mb-6">
          <h2 className="text-2xl font-semibold mb-2">Homepage Market Watch</h2>
          <p className="text-sm mb-4">
            4 tabs: Indices, Futures, Bonds, Forex - clean appearance without
            company logos
          </p>
        </div>
        <div className="container mx-auto px-4">
          <div className="bg-card rounded-lg overflow-hidden max-w-md mx-auto">
            <MarketWatch
              tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
              height={400}
              showSymbolLogo={false}
              className="min-h-96"
            />
          </div>
        </div>
      </section>

      {/* Investment Category - Dark Theme */}
      <section className="mb-12">
        <div className="container mx-auto px-4 mb-6">
          <h2 className="text-2xl font-semibold mb-2">
            Investment Category - Dark Theme
          </h2>
          <p className="text-sm mb-4">
            German stocks and indices with dark color scheme and custom colors
          </p>
        </div>
        <div className="container mx-auto px-4">
          <div className="bg-card rounded-lg overflow-hidden max-w-md mx-auto">
            <MarketWatch
              tabs={MARKET_WATCH_TAB_SETS.investment.tabs}
              colorTheme="dark"
              height={350}
              showFloatingTooltip={false}
              colors={{
                plotLineColorGrowing: 'rgba(34, 197, 94, 1)', // Emerald green
                plotLineColorFalling: 'rgba(239, 68, 68, 1)', // Red
                symbolActiveColor: 'rgba(59, 130, 246, 1)', // Blue
              }}
              className="min-h-[22rem]"
            />
          </div>
        </div>
      </section>

      {/* Technology Category - Compact */}
      <section className="mb-12">
        <div className="container mx-auto px-4 mb-6">
          <h2 className="text-2xl font-semibold mb-2">
            Technology Category - Compact
          </h2>
          <p className="text-sm mb-4">
            Tech stocks and crypto with no chart, compact layout
          </p>
        </div>
        <div className="container mx-auto px-4">
          <div className="bg-card rounded-lg overflow-hidden max-w-md mx-auto">
            <MarketWatch
              tabs={MARKET_WATCH_TAB_SETS.technology.tabs}
              height={300}
              showChart={false}
              showSymbolLogo={false}
              dateRange="1M"
              isTransparent={false}
              className="min-h-72"
            />
          </div>
        </div>
      </section>

      {/* Custom Colors Example */}
      <section className="mb-12">
        <div className="container mx-auto px-4 mb-6">
          <h2 className="text-2xl font-semibold mb-2">Custom Brand Colors</h2>
          <p className="text-sm mb-4">
            Homepage data with custom brand-themed colors
          </p>
        </div>
        <div className="container mx-auto px-4">
          <div className="bg-card rounded-lg overflow-hidden max-w-md mx-auto">
            <MarketWatch
              tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
              height={400}
              locale="en"
              timezone="Europe/London"
              isTransparent={false}
              colors={{
                plotLineColorGrowing: 'rgba(16, 185, 129, 1)', // Teal green
                plotLineColorFalling: 'rgba(245, 101, 101, 1)', // Coral red
                symbolActiveColor: 'rgba(99, 102, 241, 1)', // Indigo
                scaleFontColor: 'rgba(55, 65, 81, 1)', // Gray-700
                gridLineColor: 'rgba(229, 231, 235, 0.5)', // Gray-200
                belowLineFillColorGrowing: 'rgba(16, 185, 129, 0.1)', // Light teal
                belowLineFillColorFalling: 'rgba(245, 101, 101, 0.1)', // Light coral
              }}
              showFloatingTooltip={true}
              showSymbolLogo={false}
              className="min-h-96"
            />
          </div>
        </div>
      </section>

      {/* Different Sizes */}
      <section className="mb-12">
        <div className="container mx-auto px-4 mb-6">
          <h2 className="text-2xl font-semibold mb-2">Different Sizes</h2>
          <p className="text-sm mb-4">
            Same component at different heights and widths
          </p>
        </div>
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Small */}
            <div className="bg-card rounded-lg overflow-hidden">
              <h3 className="text-lg font-medium p-4 border-b">
                Small (250px height)
              </h3>
              <MarketWatch
                tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
                height={250}
                showChart={false}
                className="min-h-64"
              />
            </div>

            {/* Large */}
            <div className="bg-card rounded-lg overflow-hidden">
              <h3 className="text-lg font-medium p-4 border-b">
                Large (500px height)
              </h3>
              <MarketWatch
                tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
                height={500}
                dateRange="6M"
                className="min-h-[32rem]"
              />
            </div>
          </div>
        </div>
      </section>

      {/* Configuration Summary */}
      <section className="mb-12">
        <div className="container mx-auto px-4">
          <div className="bg-muted/50 rounded-lg p-6">
            <h2 className="text-xl font-semibold mb-4">
              Available Configuration Options
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 text-sm">
              <div>
                <h3 className="font-medium mb-3">Basic Settings</h3>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• height: number</li>
                  <li>• width: string | number</li>
                  <li>
                    • colorTheme: &apos;light&apos; | &apos;dark&apos; |
                    undefined
                  </li>
                  <li>• dateRange: &apos;1D&apos; to &apos;ALL&apos;</li>
                  <li>• locale: string</li>
                  <li>• largeChartUrl: string</li>
                  <li>• supportHost: string</li>
                  <li>• timezone: string</li>
                  <li>• autosize: boolean</li>
                  <li>• container_id: string</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-3">Display Options</h3>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• isTransparent: boolean</li>
                  <li>• showFloatingTooltip: boolean</li>
                  <li>• showSymbolLogo: boolean</li>
                  <li>• showChart: boolean</li>
                  <li>• className: string</li>
                </ul>
              </div>
              <div>
                <h3 className="font-medium mb-3">Custom Colors</h3>
                <ul className="space-y-1 text-muted-foreground">
                  <li>• plotLineColorGrowing</li>
                  <li>• plotLineColorFalling</li>
                  <li>• symbolActiveColor</li>
                  <li>• scaleFontColor</li>
                  <li>• gridLineColor</li>
                  <li>• belowLineFillColorGrowing</li>
                  <li>• belowLineFillColorFalling</li>
                  <li>• belowLineFillColorGrowingBottom</li>
                  <li>• belowLineFillColorFallingBottom</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
