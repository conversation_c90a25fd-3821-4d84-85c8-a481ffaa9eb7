import { NotFoundComponent } from '@/components/ui/not-found';
import { getCachedAllCategories } from '@/lib/cache/categories';

export default async function CategoryNotFound() {
  // Fetch all available categories dynamically
  const categories = await getCachedAllCategories();

  // Transform categories to match NotFoundComponent interface
  const transformedCategories = categories
    .filter(category => category.slug) // Filter out categories without slugs
    .map(category => ({
      id: category.id.toString(),
      slug: category.slug as string, // Safe to cast after filter
      title: category.title,
    }));

  return (
    <NotFoundComponent
      title="Kategorie nicht gefunden"
      description="Die angeforderte Kategorie existiert nicht oder wurde entfernt."
      reasons={[
        'Die Kategorie wurde umbenannt',
        'Der Link ist veraltet',
        '<PERSON>s gab einen Tippfehler in der URL',
      ]}
      categories={transformedCategories}
      showCategories={transformedCategories.length > 0}
    />
  );
}
