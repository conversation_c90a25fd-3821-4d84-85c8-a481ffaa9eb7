import React from 'react';
import { NotFoundComponent } from '@/components/ui/not-found';
import { getCachedAllCategories } from '@/lib/cache/categories';

export default async function GlobalNotFound() {
  // Fetch available categories dynamically
  const availableCategories = await getCachedAllCategories();
  // Limit to 4 popular categories and transform to match interface
  const popularCategories = availableCategories
    .filter(category => category.slug) // Filter out categories without slugs
    .slice(0, 4)
    .map(category => ({
      id: category.id.toString(),
      slug: category.slug as string, // Safe to cast after filter
      title: category.title,
    }));

  return (
    <NotFoundComponent
      categories={popularCategories}
      showCategories={popularCategories.length > 0}
    />
  );
}
