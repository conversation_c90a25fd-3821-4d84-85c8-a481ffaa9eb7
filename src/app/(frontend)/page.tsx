import { Suspense } from 'react';
import type { Metadata, Viewport } from 'next';
import { AccessibilityNav } from '@/components/homepage/AccessibilityNav';
import { HomepageClient } from '@/components/homepage/HomepageClient';
import TierOneSection from '@/components/homepage/TierOneSection';
import TierTwoSection from '@/components/homepage/TierTwoSection';
import TierThreeSection from '@/components/homepage/TierThreeSection';
import {
  TierOneSkeleton,
  TierTwoSkeleton,
  TierThreeSkeleton,
} from '@/components/homepage/TierSkeletons';
import { DEFAULT_HOMEPAGE_CONFIG } from '@/components/homepage/types';
import {
  SYMBOL_SETS,
  MARKET_WATCH_TAB_SETS,
} from '@/components/ticker-tape/TickerTape.config';
import { MarketWatch } from '@/components/market-watch';
import FeaturedArticlesSection from '@/components/articles/FeaturedArticlesSection';
import './global.css';

// Enable Next.js ISR (Incremental Static Regeneration)
export const revalidate = 300; // 5 minutes

// Page metadata
export const metadata: Metadata = {
  metadataBase: new URL('https://boersenblick.com'),
  title: 'BörsenBlick – Schnell. Scharf. Voraus.',
  description:
    'BörsenBlick ist eine neue Website, die darauf spezialisiert ist, sowohl privaten als auch institutionellen Anlegern die besten und prägnantesten Anlageempfehlungen zu bieten.',
  keywords:
    'Börse,Finanzen,Wirtschaft,Deutschland,Marktanalyse,Aktien,Investment',
  authors: [{ name: 'BörsenBlick Team' }],
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  openGraph: {
    title: 'BörsenBlick – Schnell. Scharf. Voraus.',
    description:
      'BörsenBlick ist eine neue Website, die darauf spezialisiert ist, sowohl privaten als auch institutionellen Anlegern die besten und prägnantesten Anlageempfehlungen zu bieten.',
    url: 'https://boersenblick.com',
    siteName: 'BörsenBlick',
    locale: 'de_DE',
    type: 'website',
    images: [
      {
        url: '/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'BörsenBlick – Schnell. Scharf. Voraus.',
      },
    ],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BörsenBlick',
    description: 'BörsenBlick – Schnell. Scharf. Voraus.',
    images: ['/twitter-image.jpg'],
  },
  alternates: {
    canonical: 'https://boersenblick.com',
  },
  category: 'news',
};

export const viewport: Viewport = {
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: 'white' },
    { media: '(prefers-color-scheme: dark)', color: 'black' },
  ],
};

export default async function HomePage() {
  const config = DEFAULT_HOMEPAGE_CONFIG;

  return (
    <div className="bg-background">
      {/* Set up homepage ticker via context (renders above navigation) */}
      <HomepageClient symbols={SYMBOL_SETS.homepage.symbols} />

      {/* Accessibility Navigation */}
      <AccessibilityNav
        config={config.accessibility}
        isLoading={false}
        hasError={false}
      />

      {/* Main Content */}
      <main
        id="main-content"
        className="focus:outline-none"
        tabIndex={-1}
        role="main"
        aria-label="BörsenBlick Homepage"
      >
        {/* Page Title - Hidden but accessible */}
        <h1 className="sr-only">
          BörsenBlick - Deutsche Finanz- und Wirtschaftsnachrichten
        </h1>

        {/* Homepage Grid Layout */}
        <div className="max-w-[1440px] mx-auto px-4 py-4 sm:py-6 md:py-8 lg:py-10">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-0">
            {/* Column 1 - Market Analysis (Tier 2) */}
            <aside
              className="sm:border-r lg:border-r xl:border-r sm:border-border lg:border-border xl:border-border sm:pr-2 lg:pr-3 xl:pr-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-3 sm:order-1"
              aria-labelledby="market-analysis-sidebar-heading"
            >
              <h2 id="market-analysis-sidebar-heading" className="sr-only">
                Marktanalyse
              </h2>
              <Suspense fallback={<TierTwoSkeleton />}>
                <TierTwoSection />
              </Suspense>
            </aside>

            {/* Columns 2-3 - Tier 1 Only (Enhanced Featured Content) */}
            <section
              id="featured-articles"
              className="sm:col-span-1 lg:col-span-2 xl:col-span-2 sm:border-r lg:border-r xl:border-r sm:border-border lg:border-border xl:border-border sm:px-2 lg:px-3 xl:px-4 mb-8 sm:mb-10 lg:mb-12 xl:mb-0 order-1 sm:order-2"
              aria-labelledby="featured-articles-heading"
            >
              <h2 id="featured-articles-heading" className="sr-only">
                Wichtige Artikel
              </h2>

              {/* Enhanced Tier 1 - Prominent Post + Grid + Horizontal */}
              <Suspense fallback={<TierOneSkeleton />}>
                <TierOneSection />
              </Suspense>
            </section>

            {/* Column 4 - Market Watch & Latest Updates */}
            <aside
              className="xl:pl-4 order-2 sm:order-3 hidden xl:block space-y-3"
              aria-labelledby="market-watch-updates-heading"
            >
              <h2 id="market-watch-updates-heading" className="sr-only">
                Marktübersicht und aktuelle Meldungen
              </h2>

              {/* Featured Articles Section */}
              <FeaturedArticlesSection
                maxArticles={4}
                locale="de"
                className="mb-6"
              />

              {/* Market Watch Widget */}
              <section
                aria-labelledby="market-watch-heading"
                className="bg-card rounded-lg overflow-hidden"
              >
                <h3 id="market-watch-heading" className="sr-only">
                  Marktübersicht
                </h3>
                <MarketWatch
                  tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
                  height={config.marketWatch.height}
                  width={config.marketWatch.width}
                  colorTheme={config.marketWatch.colorTheme}
                  dateRange={config.marketWatch.dateRange}
                  locale={config.marketWatch.locale}
                  largeChartUrl={config.marketWatch.largeChartUrl}
                  isTransparent={config.marketWatch.isTransparent}
                  showFloatingTooltip={config.marketWatch.showFloatingTooltip}
                  showChart={config.marketWatch.showChart}
                  showSymbolLogo={config.marketWatch.showSymbolLogo}
                  supportHost={config.marketWatch.supportHost}
                  colors={config.marketWatch.colors}
                  timezone={config.marketWatch.timezone}
                  autosize={config.marketWatch.autosize}
                  container_id={config.marketWatch.container_id}
                  className={config.marketWatch.className}
                />
              </section>

              {/* Latest Updates Articles */}
              <Suspense fallback={<TierThreeSkeleton />}>
                <TierThreeSection />
              </Suspense>
            </aside>
          </div>
        </div>
      </main>
    </div>
  );
}
