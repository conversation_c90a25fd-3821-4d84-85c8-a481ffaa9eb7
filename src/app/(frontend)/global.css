@import 'tailwindcss';
@import 'tw-animate-css';

@custom-variant dark (&:is(.dark *));

@theme {
  /* Override the default serif font to use Merriweather */
  --font-serif: var(--font-merriweather), serif;

  /* Override the default sans font to use Roboto */
  --font-sans: var(--font-roboto), sans-serif;
}

:root {
  --background: oklch(1 0 0);
  --foreground: oklch(0.2842 0.0095 355.534);
  --card: oklch(1 0 0);
  --card-foreground: oklch(0.2842 0.0095 355.534);
  --popover: oklch(1 0 0);
  --popover-foreground: oklch(0.2842 0.0095 355.534);
  --primary: oklch(0.2842 0.0095 355.534);
  --primary-foreground: oklch(1 0 0);
  --secondary: oklch(0.2842 0.0095 355.534);
  --secondary-foreground: oklch(1 0 0);
  --muted: oklch(0.9846 0.0017 247.8389);
  --muted-foreground: oklch(0.4532 0 0);
  --accent: oklch(0.9846 0.0017 247.8389);
  --accent-foreground: oklch(0.2842 0.0095 355.534);
  --destructive: oklch(0.4819 0.0883 18.0031);
  --destructive-foreground: oklch(0.9846 0.0017 247.8389);
  --border: oklch(0.9219 0 0);
  --input: oklch(0.9219 0 0);
  --ring: oklch(0.9219 0 0);
  --chart-1: oklch(0.4819 0.0883 18.0031);
  --chart-2: oklch(0.5179 0.0728 113.5402);
  --chart-3: oklch(0.6641 0.0831 77.4038);
  --chart-4: oklch(0.4532 0 0);
  --chart-5: oklch(0.7492 0.0834 79.4311);
  --sidebar: oklch(1 0 0);
  --sidebar-foreground: oklch(0.2842 0.0095 355.534);
  --sidebar-primary: oklch(0.2842 0.0095 355.534);
  --sidebar-primary-foreground: oklch(0.9846 0.0017 247.8389);
  --sidebar-accent: oklch(0.9846 0.0017 247.8389);
  --sidebar-accent-foreground: oklch(0.2842 0.0095 355.534);
  --sidebar-border: oklch(0.9219 0 0);
  --sidebar-ring: oklch(0.9219 0 0);
  --font-sans: Roboto, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  --radius: 0.35rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;
}

.dark {
  --background: oklch(0.2393 0 0);
  --foreground: oklch(0.9911 0 0);
  --card: oklch(0.2393 0 0);
  --card-foreground: oklch(0.9911 0 0);
  --popover: oklch(0.2393 0 0);
  --popover-foreground: oklch(0.9911 0 0);
  --primary: oklch(0.9911 0 0);
  --primary-foreground: oklch(0.2842 0.0095 355.534);
  --secondary: oklch(0.2842 0.0095 355.534);
  --secondary-foreground: oklch(0.9911 0 0);
  --muted: oklch(0.2393 0 0);
  --muted-foreground: oklch(0.4532 0 0);
  --accent: oklch(0.4532 0 0);
  --accent-foreground: oklch(0.9911 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --destructive-foreground: oklch(0.985 0 0);
  --border: oklch(0.4091 0 0);
  --input: oklch(0.4091 0 0);
  --ring: oklch(0.4091 0 0);
  --chart-1: oklch(0.6348 0.082 16.6264);
  --chart-2: oklch(0.8066 0.0697 112.4278);
  --chart-3: oklch(0.6316 0.0826 76.7364);
  --chart-4: oklch(0.5503 0.0843 77.4782);
  --chart-5: oklch(0.7316 0 0);
  --sidebar: oklch(0.2393 0 0);
  --sidebar-foreground: oklch(0.9911 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.4091 0 0);
  --sidebar-accent-foreground: oklch(0.9911 0 0);
  --sidebar-border: oklch(0.4091 0 0);
  --sidebar-ring: oklch(0.4091 0 0);
  --font-sans: Roboto, sans-serif;
  --font-serif: Merriweather, serif;
  --font-mono:
    ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono',
    'Courier New', monospace;
  --radius: 0.35rem;
  --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
  --shadow-sm:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow: 0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
  --shadow-md:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
  --shadow-lg:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
  --shadow-xl:
    0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
  --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
}

@theme inline {
  --color-lion-50: oklch(97.39% 0.011 63.39);
  --color-lion-100: oklch(94.12% 0.029 72.47);
  --color-lion-200: oklch(88.89% 0.059 72.76);
  --color-lion-300: oklch(83.03% 0.103 77.43);
  --color-lion-400: oklch(77.85% 0.098 77.25);
  --color-lion-500: oklch(71.87% 0.091 77.3);
  --color-lion-600: oklch(66.41% 0.083 77.4); /* True colour */
  --color-lion-700: oklch(53.65% 0.067 78.56);
  --color-lion-800: oklch(39.84% 0.05 78.48);
  --color-lion-900: oklch(26.84% 0.033 77.52);
  --color-lion-950: oklch(20.66% 0.025 76.79);
  --color-ecru-50: oklch(82.99% 0.105 80.11);
  --color-ecru-100: oklch(81.24% 0.099 80.3);
  --color-ecru-200: oklch(79.54% 0.095 80.52);
  --color-ecru-300: oklch(76.92% 0.089 80.01);
  --color-ecru-400: oklch(74.92% 0.083 79.42); /* True colour */
  --color-ecru-500: oklch(64.89% 0.075 79.95);
  --color-ecru-600: oklch(54.47% 0.065 80.12);
  --color-ecru-700: oklch(44.14% 0.054 81.73);
  --color-ecru-800: oklch(34.55% 0.044 81);
  --color-ecru-900: oklch(24.12% 0.031 80.88);
  --color-ecru-950: oklch(18.74% 0.025 80.93);
  --color-cordovan-50: oklch(96.58% 0.006 17.54);
  --color-cordovan-100: oklch(92.34% 0.014 12.16);
  --color-cordovan-200: oklch(84.87% 0.029 15.15);
  --color-cordovan-300: oklch(78.17% 0.048 14.97);
  --color-cordovan-400: oklch(70.72% 0.071 16.74);
  --color-cordovan-500: oklch(63.23% 0.103 17.26);
  --color-cordovan-600: oklch(55.54% 0.101 18.42);
  --color-cordovan-700: oklch(48.19% 0.088 18.01); /* True colour */
  --color-cordovan-800: oklch(37.11% 0.068 18.25);
  --color-cordovan-900: oklch(25.51% 0.046 18.68);
  --color-cordovan-950: oklch(19.19% 0.036 18.03);
  --color-dark-green-moss-50: oklch(95.1% 0.134 113.67);
  --color-dark-green-moss-100: oklch(91.65% 0.129 113.44);
  --color-dark-green-moss-200: oklch(83.06% 0.117 113.52);
  --color-dark-green-moss-300: oklch(75.49% 0.106 113.52);
  --color-dark-green-moss-400: oklch(67.73% 0.095 113.55);
  --color-dark-green-moss-500: oklch(60.05% 0.085 113.5);
  --color-dark-green-moss-600: oklch(51.79% 0.073 113.53); /* True colour */
  --color-dark-green-moss-700: oklch(42.1% 0.06 113.67);
  --color-dark-green-moss-800: oklch(32.59% 0.046 113.83);
  --color-dark-green-moss-900: oklch(23.15% 0.033 113.93);
  --color-dark-green-moss-950: oklch(18.58% 0.025 112.27);
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

/* @layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
  }
} */

@layer utilities {
  /* Hide scrollbar for horizontal navigation on mobile */
  .scrollbar-hide {
    scrollbar-width: none; /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none; /* Chrome, Safari and Opera */
  }
}
