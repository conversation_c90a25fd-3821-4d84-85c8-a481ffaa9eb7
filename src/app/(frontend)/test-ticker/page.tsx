import { TickerTape } from '@/components/ticker-tape';
import { SYMBOL_SETS } from '@/components/ticker-tape/TickerTape.config';

export default function TestTickerPage() {
  return (
    <div className="bg-background">
      {/* Header section in container */}
      <div className="container mx-auto py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold tracking-tight mb-4">
            TickerTape Component Demo
          </h1>
          <p className="text-muted-foreground">
            Four category pages with their specific ticker symbols
          </p>
        </div>
      </div>

      {/* Homepage - Full width ticker */}
      <section className="mb-8">
        <div className="container mx-auto px-4 mb-4">
          <h2 className="text-2xl font-semibold mb-2">Homepage</h2>
          <p className="text-sm mb-4">
            14 major global indices and cryptocurrencies
          </p>
        </div>
        <TickerTape symbols={SYMBOL_SETS.homepage.symbols} />
      </section>

      {/* Investment Category Page - Full width ticker */}
      <section className="mb-8">
        <div className="container mx-auto px-4 mb-4">
          <h2 className="text-2xl font-semibold mb-2">
            Investment Category Page
          </h2>
          <p className="text-sm mb-4">29 German DAX stocks (FWB exchange)</p>
        </div>
        <TickerTape symbols={SYMBOL_SETS.investment.symbols} />
      </section>

      {/* Technology Category Page - Full width ticker */}
      <section className="mb-8">
        <div className="container mx-auto px-4 mb-4">
          <h2 className="text-2xl font-semibold mb-2">
            Technology Category Page
          </h2>
          <p className="text-sm mb-4">
            22 tech stocks and cryptocurrencies from various exchanges
          </p>
        </div>
        <TickerTape symbols={SYMBOL_SETS.technology.symbols} />
      </section>

      {/* International Category Page - Full width ticker */}
      <section className="mb-8">
        <div className="container mx-auto px-4 mb-4">
          <h2 className="text-2xl font-semibold mb-2">
            International Category Page
          </h2>
          <p className="text-sm mb-4">8 major forex currency pairs</p>
        </div>
        <TickerTape symbols={SYMBOL_SETS.international.symbols} />
      </section>

      {/* Economics Page - Full width ticker */}
      <section className="mb-8">
        <div className="container mx-auto px-4 mb-4">
          <h2 className="text-2xl font-semibold mb-2">Economics Page</h2>
          <p className="text-sm mb-4">
            11 commodities including precious metals, energy, and agricultural
            products
          </p>
        </div>
        <TickerTape symbols={SYMBOL_SETS.wirtschaft.symbols} />
      </section>
    </div>
  );
}
