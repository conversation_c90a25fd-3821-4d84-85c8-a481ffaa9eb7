import { ThemeProvider } from '@/components/theme-provider';
import { NotFoundComponent } from '@/components/ui/not-found';
// Import the styles directly
import './(frontend)/global.css';

export default function GlobalNotFound() {
  return (
    <html lang="de" className="min-h-dvh" suppressHydrationWarning>
      <head>
        <meta name="robots" content="noindex" />
        <title>404 - Seite nicht gefunden | BörsenBlick</title>
      </head>
      <body className="min-h-dvh">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <NotFoundComponent />
        </ThemeProvider>
      </body>
    </html>
  );
}
