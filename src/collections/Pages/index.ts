import type { CollectionConfig } from 'payload';
import { authenticated } from '../../access/authenticated';
import { authenticatedOrPublished } from '../../access/authenticatedOrPublished';
import { slugField } from '../../fields/slug';
import { defaultLexical } from '../../fields/defaultLexical';
import { revalidatePage, revalidateDelete } from './hooks/revalidatePage';
import {
  createPageInvalidationHook,
  createPageDeletionHook,
} from '@/lib/cache/invalidation';
import {
  MetaDescriptionField,
  MetaImageField,
  MetaTitleField,
  OverviewField,
  PreviewField,
} from '@payloadcms/plugin-seo/fields';

export const Pages: CollectionConfig = {
  slug: 'pages',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  defaultPopulate: {
    title: true,
    slug: true,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: ['title', 'slug', '_status', 'updatedAt'],
    description: '',
    group: 'Content',
    // Add preview configuration using PayloadCMS pattern
    preview: ({ slug, collection }) => {
      if (!slug) return null;

      const params = new URLSearchParams({
        slug: String(slug),
        collection: 'pages',
        path: `/${String(slug)}`, // Updated for new URL structure
        previewSecret: process.env.PAYLOAD_PUBLIC_DRAFT_SECRET || '',
      });

      return `/api/preview?${params.toString()}`;
    },
    // Custom dropdown menu items next to Publish button
    components: {
      edit: {
        editMenuItems: [
          '@/components/admin/shared/ViewLiveMenuItem#ViewLiveMenuItem',
        ],
      },
    },
  },
  fields: [
    // Top-level title field (required by PayloadCMS for useAsTitle)
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        hidden: true, // Hide from form since we'll use englishTab.title for editing
      },
    },

    // Featured Image field
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Featured image for the page',
      },
      filterOptions: {
        mimeType: { contains: 'image' },
      },
    },

    // Translation controls in sidebar
    {
      type: 'ui',
      name: 'translationControls',
      admin: {
        position: 'sidebar',
        components: {
          Field:
            '@/components/admin/pages/PageDocumentControls#PageDocumentControls',
        },
      },
    },

    // Tab structure with English and German content
    {
      type: 'tabs',
      tabs: [
        {
          label: 'English Content',
          name: 'englishTab',
          fields: [
            {
              name: 'title',
              type: 'text',
              required: true,
              admin: {
                description: 'English page title for translation',
              },
            },
            {
              name: 'content',
              type: 'richText',
              editor: defaultLexical,
              required: true,
              admin: {
                description: 'The main English content of the page',
              },
            },
          ],
        },
        {
          label: 'German Translation',
          name: 'germanTab',
          admin: {
            condition: data => data?.hasGermanTranslation === true,
          },
          fields: [
            {
              name: 'germanTitle',
              label: 'German Title',
              type: 'text',
              admin: {
                description: 'AI-translated German title for the page',
              },
            },
            {
              name: 'germanContent',
              label: 'German Content',
              type: 'richText',
              editor: defaultLexical,
              admin: {
                description: 'AI-translated German content for the page',
              },
            },
          ],
        },
        {
          name: 'meta',
          label: 'SEO',
          fields: [
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
              hasGenerateFn: true,
            }),
            MetaDescriptionField({
              hasGenerateFn: true,
            }),
            PreviewField({
              hasGenerateFn: true,
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
          ],
        },
      ],
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar',
        description: 'When this page was published',
      },
    },
    ...slugField('germanTab.germanTitle'),

    // Parent page relationship for hierarchy
    {
      name: 'parent',
      type: 'relationship',
      relationTo: 'pages',
      admin: {
        position: 'sidebar',
        description: 'Select a parent page to create a hierarchy (optional)',
      },
    },

    // Breadcrumbs toggle
    {
      name: 'enableBreadcrumbs',
      type: 'checkbox',
      label: 'Enable Breadcrumbs',
      defaultValue: true,
      admin: {
        position: 'sidebar',
        description: 'Show breadcrumb navigation for this page',
      },
    },

    // German translation flag (positioned after slug in sidebar)
    {
      name: 'hasGermanTranslation',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Indicates if German translation is available',
      },
    },
  ],
  hooks: {
    beforeChange: [
      // Sync englishTab.title to top-level title for admin display
      ({ data }) => {
        if (data.englishTab?.title) {
          data.title = data.englishTab.title;
        }
        return data;
      },
      // Auto-populate publishedAt when page is published
      ({ data, originalDoc, operation }) => {
        // If page is being published for the first time, set publishedAt
        if (
          data._status === 'published' &&
          (!originalDoc || originalDoc._status !== 'published') &&
          !data.publishedAt
        ) {
          data.publishedAt = new Date().toISOString();
        }
        return data;
      },
    ],
    afterChange: [revalidatePage, createPageInvalidationHook()],
    afterDelete: [revalidateDelete, createPageDeletionHook()], // ✅ Use proper deletion hook
  },
  versions: {
    drafts: true, // Simple drafts like Articles - manual save behavior
  },
};
