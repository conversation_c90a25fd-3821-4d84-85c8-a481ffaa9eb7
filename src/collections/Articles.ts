import type { CollectionConfig } from 'payload';

import { authenticated } from '../access/authenticated';
import {
  MetaTitleField,
  MetaDescriptionField,
  MetaImageField,
  PreviewField,
  OverviewField,
} from '@payloadcms/plugin-seo/fields';
import { authenticatedOrPublished } from '../access/authenticatedOrPublished';
import { defaultLexical } from '../fields/defaultLexical';
import {
  prepareLexicalForReading,
  prepareLexicalForStorage,
} from '../lib/utils/lexical-validation';
import { formatSlug, formatSlugWithTimestamp } from '../fields/slug/formatSlug';
import {
  validateArticleForPublication,
  formatValidationError,
  logValidationWarnings,
} from '../lib/utils/article-validation';
import { calculateArticleReadingTime } from '../lib/utils/readtime'; // ✅ PHASE 2: Import readtime utility

export const Articles: CollectionConfig = {
  slug: 'articles',
  access: {
    create: authenticated,
    delete: authenticated,
    read: authenticatedOrPublished,
    update: authenticated,
  },
  admin: {
    useAsTitle: 'title',
    defaultColumns: [
      'title',
      'articleType',
      'featured',
      '_status',
      'updatedAt',
    ], // Fixed field names
    group: 'Content',
    // Add preview configuration using PayloadCMS pattern
    preview: ({ slug }) => {
      if (!slug) return null;

      const params = new URLSearchParams({
        slug: String(slug),
        collection: 'articles',
        path: `/artikel/${String(slug)}`,
        previewSecret: process.env.PAYLOAD_PUBLIC_DRAFT_SECRET || '',
      });

      return `/api/preview?${params.toString()}`;
    },
    // Custom dropdown menu items next to Publish button
    components: {
      edit: {
        editMenuItems: [
          '@/components/admin/shared/ViewLiveMenuItem#ViewLiveMenuItem',
        ],
      },
    },
  },
  fields: [
    // FIXED: Simplified title logic using beforeValidate
    {
      name: 'title',
      type: 'text',
      required: true,
      admin: {
        description:
          'Article title - Used within the backend to identify the article. Not used in the frontend.',
      },
      hooks: {
        beforeValidate: [
          ({ value, data }) => {
            try {
              // Only auto-populate if no title provided
              if (!value || (typeof value === 'string' && !value.trim())) {
                // Priority: German title > English title > existing value
                return (
                  data?.germanTab?.germanTitle ||
                  data?.englishTab?.enhancedTitle ||
                  value
                );
              }
              return value;
            } catch (error) {
              console.error('Error in title hook:', error);
              return value; // Fallback to existing value
            }
          },
        ],
      },
    },
    // Slug field - moved to main area under title
    {
      name: 'slug',
      type: 'text',
      admin: {
        description: 'URL-friendly version of the title',
      },
      hooks: {
        beforeValidate: [
          async ({ value, data, operation, req }) => {
            try {
              // On create: Generate slug from enhanced English title or main title
              if (!value && operation === 'create') {
                const titleSource =
                  data?.englishTab?.enhancedTitle || data?.title;

                if (titleSource) {
                  // TEMPORARY: Skip database check to test if this causes transaction rollback
                  console.log(
                    '🔍 SLUG GENERATION: Skipping database check to test transaction rollback'
                  );
                  return formatSlugWithTimestamp(titleSource, true);

                  // DISABLED: Database query that might cause deadlock
                  // const baseSlug = formatSlug(titleSource);
                  // const payload = req.payload;
                  // const existingArticles = await payload.find({
                  //   collection: 'articles',
                  //   where: { slug: { equals: baseSlug } },
                  //   limit: 1,
                  // });
                  // if (existingArticles.docs.length > 0) {
                  //   return formatSlugWithTimestamp(titleSource, true);
                  // }
                  // return baseSlug;
                }
              }

              // DISABLED: On update slug switching - testing if database query causes rollback
              if (
                operation === 'update' &&
                data?.germanTab?.germanTitle &&
                data?.hasGermanTranslation
              ) {
                console.log(
                  '🔍 SLUG UPDATE: Skipping German slug database check to test transaction rollback'
                );
                return formatSlugWithTimestamp(
                  data.germanTab.germanTitle,
                  true
                );

                // DISABLED: Database query that might cause deadlock during update
                // const baseSlug = formatSlug(data.germanTab.germanTitle);
                // const payload = req.payload;
                // const existingArticles = await payload.find({
                //   collection: 'articles',
                //   where: {
                //     and: [
                //       { slug: { equals: baseSlug } },
                //       { id: { not_equals: data.id } }
                //     ]
                //   },
                //   limit: 1,
                // });
                // if (existingArticles.docs.length > 0) {
                //   return formatSlugWithTimestamp(data.germanTab.germanTitle, true);
                // }
                // return baseSlug;
              }

              // On update: Update to English enhanced title if it becomes available and no German exists
              if (
                operation === 'update' &&
                data?.englishTab?.enhancedTitle &&
                !data?.hasGermanTranslation &&
                !value
              ) {
                return formatSlug(data.englishTab.enhancedTitle);
              }

              return value;
            } catch (error) {
              console.error('Error in slug generation:', error);
              return value; // Fallback to existing value
            }
          },
        ],
      },
    },
    // Featured Image field
    {
      name: 'featuredImage',
      type: 'upload',
      relationTo: 'media',
      admin: {
        description: 'Featured image for the article',
      },
      filterOptions: {
        mimeType: { contains: 'image' },
      },
    },

    // Translate button at the very top of sidebar
    {
      type: 'ui',
      name: 'documentControls',
      admin: {
        position: 'sidebar',
        components: {
          Field:
            '@/components/admin/article-actions/DocumentControls#ArticleDocumentControls',
        },
      },
    },

    // 1. Article Type at top of sidebar
    {
      name: 'articleType',
      type: 'select',
      required: true,
      defaultValue: 'curated',
      options: [
        { label: 'Generated (from RSS)', value: 'generated' },
        { label: 'Curated (manual)', value: 'curated' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Type of article - determines source and workflow',
      },
    },
    // 2. Workflow Stage (optional editorial bookmark - publication uses native Draft/Publish)
    {
      name: 'workflowStage',
      label: 'Editorial Stage (Optional)',
      type: 'select',
      defaultValue: 'curated-draft',
      options: [
        { label: 'Curated Draft', value: 'curated-draft' },
        { label: 'Enhanced English (Candidate)', value: 'candidate-article' },
        { label: 'Enhanced & Translated', value: 'translated' },
        { label: 'Ready for Publication', value: 'ready-for-review' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Editorial stage',
      },
      hooks: {
        beforeChange: [
          ({ value, data }) => {
            // Set appropriate default workflow stage based on article type
            if (!value) {
              return data?.articleType === 'curated'
                ? 'curated-draft'
                : 'candidate-article';
            }
            return value;
          },
        ],
      },
    },
    // 3. Categories
    {
      name: 'categories',
      type: 'relationship',
      relationTo: 'categories',
      hasMany: true,
      admin: {
        position: 'sidebar',
        description: 'Article categories for organization and filtering',
      },
    },
    // 4. Placement
    {
      name: 'placement',
      type: 'select',
      options: [
        { label: 'Tier 1', value: 'tier-1' },
        { label: 'Tier 2', value: 'tier-2' },
        { label: 'Tier 3', value: 'tier-3' },
      ],
      admin: {
        position: 'sidebar',
        description: 'Article placement tier for prioritisation',
      },
    },
    // 5. Featured
    {
      name: 'featured',
      type: 'checkbox',
      defaultValue: false,
      index: true, // Enable database indexing for efficient querying
      admin: {
        position: 'sidebar',
        description:
          'Mark this article as featured for prominent display on homepage and article pages',
      },
    },
    // 6. Pinned
    {
      name: 'pinned',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        description: 'Pin this article to the top',
      },
    },
    // 7. Trending (manual)
    {
      name: 'trending',
      type: 'checkbox',
      defaultValue: false,
      index: true, // Enable database indexing for efficient querying
      // Note: Could add custom validation here for business rules
      // Example: Only allow trending for published articles
      access: {
        read: () => true, // Anyone can read trending status
        update: ({ req: { user } }) => Boolean(user), // Only authenticated users can modify
      },
      admin: {
        position: 'sidebar',
        description: 'Mark this article as trending (manual override)',
      },
    },
    // ✅ PHASE 2: Pre-computed read time field
    {
      name: 'readTimeMinutes',
      label: 'Reading Time (Minutes)',
      type: 'number',
      min: 1,
      max: 60,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description:
          'Pre-computed reading time in minutes (auto-calculated from content)',
      },
    },
    // 9. Publication readiness indicator
    {
      name: 'publicationReadiness',
      type: 'ui',
      admin: {
        position: 'sidebar',
        components: {
          Field:
            '@/components/admin/PublicationReadinessIndicator#PublicationReadinessIndicator',
        },
      },
    },
    // 10. Related Companies
    {
      name: 'relatedCompanies',
      label: 'Related Companies',
      type: 'array',
      fields: [
        {
          name: 'name',
          label: 'Company Name',
          type: 'text',
          required: true,
        },
        {
          name: 'ticker',
          label: 'Ticker Symbol',
          type: 'text',
          admin: {
            description: 'Stock ticker symbol (e.g., AAPL, MSFT)',
          },
        },
        {
          name: 'exchange',
          label: 'Exchange Symbol',
          type: 'text',
          admin: {
            description: 'Stock exchange code (e.g., NYSE, NASDAQ, LSE)',
          },
        },
        {
          name: 'relevance',
          label: 'Relevance Level',
          type: 'select',
          options: [
            { label: 'High', value: 'high' },
            { label: 'Medium', value: 'medium' },
            { label: 'Low', value: 'low' },
          ],
          defaultValue: 'medium',
        },
        {
          name: 'confidence',
          label: 'Confidence Score',
          type: 'number',
          min: 0,
          max: 100,
          defaultValue: 100,
          admin: {
            description: 'AI confidence score (0-100)',
          },
        },
        {
          name: 'featured',
          label: 'Featured Company',
          type: 'checkbox',
          defaultValue: false,
          admin: {
            description: 'Mark as a featured company for prominent display',
          },
        },
      ],
      admin: {
        position: 'sidebar',
        description: 'Companies extracted from the article content',
      },
    },
    // Publishing metadata
    {
      name: 'publishedBy',
      type: 'relationship',
      relationTo: 'users',
      admin: {
        position: 'sidebar',
        description: 'User who published this article',
      },
    },
    {
      name: 'publishedAt',
      type: 'date',
      admin: {
        position: 'sidebar',
        date: {
          pickerAppearance: 'dayAndTime',
        },
        description: 'When this article was published',
      },
    },
    // Enhancement flag at bottom of sidebar
    {
      name: 'hasBeenEnhanced',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Indicates if content has been AI-enhanced',
      },
    },
    // German translation flag at bottom of sidebar
    {
      name: 'hasGermanTranslation',
      type: 'checkbox',
      defaultValue: false,
      admin: {
        position: 'sidebar',
        readOnly: true,
        description: 'Indicates if German translation is available',
      },
    },
    // ✅ SPRINT 5: Original source flag for tab visibility control
    {
      name: 'hasOriginalSource',
      type: 'checkbox',
      label: 'Has Original Source',
      admin: {
        description:
          'Indicates if this article originated from RSS feed and has source content',
        readOnly: true, // Set programmatically, not user-editable
        position: 'sidebar',
      },
      defaultValue: false,
    },
    // Tab structure
    {
      type: 'tabs',
      tabs: [
        {
          label: 'English Content',
          name: 'englishTab',
          fields: [
            {
              name: 'enhancedTitle',
              label: 'Enhanced English Title',
              type: 'text',
              admin: {
                description:
                  'AI-enhanced English title optimised for international markets',
              },
            },
            {
              name: 'enhancedSummary',
              label: 'Enhanced English Summary',
              type: 'textarea',
              admin: {
                description: 'AI-enhanced English summary (100-150 characters)',
              },
            },
            {
              type: 'collapsible',
              label: 'Enhanced English Content',
              admin: {
                initCollapsed: false,
                description:
                  'AI-enhanced English content optimised for international markets (600-750 words)',
              },
              fields: [
                // FIXED: Optimized Lexical field hooks
                {
                  name: 'enhancedContent',
                  label: 'Content',
                  type: 'richText',
                  editor: defaultLexical,
                  admin: {
                    description:
                      'Enhanced English content for international markets',
                  },
                  hooks: {
                    afterRead: [
                      ({ value }) => {
                        try {
                          return prepareLexicalForReading(value);
                        } catch (error) {
                          console.error('Error in Lexical afterRead:', error);
                          return value;
                        }
                      },
                    ],
                    beforeChange: [
                      ({ value }) => {
                        try {
                          // Only process if value needs normalization
                          if (!value || typeof value === 'string') {
                            return prepareLexicalForStorage(value);
                          }
                          return value;
                        } catch (error) {
                          console.error(
                            'Error in Lexical beforeChange:',
                            error
                          );
                          return value;
                        }
                      },
                    ],
                  },
                },
              ],
            },
            {
              name: 'enhancedKeyInsights',
              label: 'Enhanced Key Insights',
              type: 'array',
              fields: [
                {
                  name: 'insight',
                  type: 'text',
                  required: true,
                },
              ],
              admin: {
                description:
                  'AI-enhanced key insights highlighting the most important takeaways',
              },
            },
            {
              type: 'collapsible',
              label: 'Enhanced Keywords',
              admin: {
                initCollapsed: true,
                description: 'AI-enhanced keywords for English content',
              },
              fields: [
                {
                  name: 'keywords',
                  label: 'Keywords',
                  type: 'array',
                  fields: [
                    {
                      name: 'keyword',
                      type: 'text',
                    },
                  ],
                  admin: {
                    description: 'AI-enhanced keywords for English content',
                  },
                },
              ],
            },
          ],
        },
        {
          label: 'Sources',
          name: 'sourcesTab',
          // ✅ SPRINT 5: Conditional tab visibility based on article origin
          admin: {
            condition: (data: any) => {
              // Show Sources tab for:
              // 1. Generated articles (always have original source)
              // 2. Curated articles that were converted from generated (hasOriginalSource = true)
              return (
                data.articleType === 'generated' ||
                (data.articleType === 'curated' &&
                  data.hasOriginalSource === true)
              );
            },
          },
          fields: [
            // RSS Feed Source - shows which feed this article came from
            {
              name: 'sourceFeed',
              label: 'Source RSS Feed',
              type: 'relationship',
              relationTo: 'rss-feeds',
              admin: {
                readOnly: true,
                description: 'The RSS feed that provided this article',
              },
            },
            // Source URL moved from sidebar to Sources tab
            {
              name: 'sourceUrl',
              label: 'Source URL',
              type: 'text',
              admin: {
                description: 'Original article URL from RSS feed',
              },
            },
            {
              name: 'originalPublishedAt',
              label: 'Source Published Date',
              type: 'date',
              admin: {
                readOnly: true,
                date: {
                  pickerAppearance: 'dayAndTime',
                },
                description: 'When the original source article was published',
              },
            },
            {
              name: 'originalTitle',
              label: 'Original Title',
              type: 'text',
              admin: {
                readOnly: true,
                description: 'Original article title from the source',
              },
            },
            {
              name: 'originalSummary',
              label: 'Original Summary',
              type: 'textarea',
              admin: {
                readOnly: true,
                description:
                  'Original article summary from Firecrawl extraction',
              },
            },
            {
              type: 'collapsible',
              label: 'Original Content',
              admin: {
                initCollapsed: true,
                description: 'Original scraped content from source article',
              },
              fields: [
                // FIXED: Minimal hooks for read-only content
                {
                  name: 'originalContent',
                  label: 'Content',
                  type: 'richText',
                  editor: defaultLexical,
                  admin: {
                    readOnly: true,
                    description:
                      'Original scraped content from Firecrawl, converted to Lexical format',
                  },
                  hooks: {
                    afterRead: [
                      ({ value }) => {
                        try {
                          return prepareLexicalForReading(value);
                        } catch (error) {
                          console.error(
                            'Error in original content afterRead:',
                            error
                          );
                          return value;
                        }
                      },
                    ],
                  },
                },
              ],
            },
          ],
        },
        // FIXED: Proper conditional tab visibility
        {
          label: 'German Translation',
          name: 'germanTab',
          admin: {
            condition: data => data?.hasGermanTranslation === true,
          },
          fields: [
            {
              name: 'germanTitle',
              label: 'German Title',
              type: 'text',
              admin: {
                description:
                  'AI-translated German title. This is the main title of the article.',
              },
            },
            {
              name: 'germanSummary',
              label: 'German Summary',
              type: 'textarea',
              admin: {
                description:
                  'AI-translated German summary (150-200 words). Used in the main Article Desc',
              },
            },
            // FIXED: Consistent Lexical hook pattern
            {
              name: 'germanContent',
              label: 'German Content',
              type: 'richText',
              editor: defaultLexical,
              admin: {
                description: 'AI-translated German content (600-750 words)',
              },
              hooks: {
                afterRead: [
                  ({ value }) => {
                    try {
                      return prepareLexicalForReading(value);
                    } catch (error) {
                      console.error(
                        'Error in German content afterRead:',
                        error
                      );
                      return value;
                    }
                  },
                ],
                beforeChange: [
                  ({ value }) => {
                    try {
                      if (!value || typeof value === 'string') {
                        return prepareLexicalForStorage(value);
                      }
                      return value;
                    } catch (error) {
                      console.error(
                        'Error in German content beforeChange:',
                        error
                      );
                      return value;
                    }
                  },
                ],
              },
            },
            {
              name: 'germanKeyInsights',
              label: 'German Key Insights',
              type: 'array',
              fields: [
                {
                  name: 'insight',
                  type: 'text',
                  required: true,
                },
              ],
              admin: {
                description: 'AI-translated German key insights',
              },
            },
            {
              type: 'collapsible',
              label: 'German Keywords',
              admin: {
                initCollapsed: true,
                description: 'AI-translated German keywords',
              },
              fields: [
                {
                  name: 'germanKeywords',
                  label: 'Keywords',
                  type: 'array',
                  fields: [
                    {
                      name: 'keyword',
                      type: 'text',
                    },
                  ],
                  admin: {
                    description: 'AI-translated German keywords',
                  },
                },
              ],
            },
            // Translation quality metrics removed - no longer needed
          ],
        },
        {
          label: 'SEO',
          name: 'meta',
          fields: [
            MetaTitleField({
              hasGenerateFn: true,
            }),
            MetaDescriptionField({
              hasGenerateFn: true,
            }),
            MetaImageField({
              relationTo: 'media',
              hasGenerateFn: true,
            }),
            PreviewField({
              hasGenerateFn: true,
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
            }),
            OverviewField({
              titlePath: 'meta.title',
              descriptionPath: 'meta.description',
              imagePath: 'meta.image',
            }),
          ],
        },
      ],
    },
  ],
  // CONSOLIDATED: All collection-level hooks in one place
  hooks: {
    beforeChange: [
      async ({ data, operation, req, originalDoc }) => {
        try {
          // 🔍 SAVE DEBUGGING - Track all save attempts
          console.log('🔍 SAVE ATTEMPT STARTED:', {
            articleId: originalDoc?.id || 'new',
            operation,
            title: data?.title,
            articleType: data?.articleType,
            hasSourceUrl: !!data?.sourcesTab?.sourceUrl,
            timestamp: new Date().toISOString(),
          });

          // 🔍 COMPREHENSIVE PUBLISH DEBUGGING
          const isNativePublishing =
            data._status === 'published' &&
            originalDoc?._status !== 'published';

          if (isNativePublishing) {
            console.log('🚀 PUBLISH WORKFLOW STARTED for article:', data.title);
            console.log(
              '📊 Status transition:',
              originalDoc?._status,
              '→',
              data._status
            );
            console.log('🔍 Complete publish debug:', {
              articleId: originalDoc?.id || 'new',
              title: data.title,
              slug: data.slug,
              operation,
              statusTransition: `${originalDoc?._status} → ${data._status}`,
              workflowStage: data.workflowStage,
              hasGermanContent: !!data.germanTab?.germanContent,
              hasEnglishContent: !!data.englishTab?.enhancedContent,
              hasFeaturedImage: !!data.featuredImage,
              categories: data.categories?.length || 0,
              publishedAt: data.publishedAt,
              publishedBy: data.publishedBy,
              userId: req?.user?.id,
              timestamp: new Date().toISOString(),
            });
          }

          // 🔍 SPRINT 3: Debug Lexical content reception (DISABLED - potential circular reference issue)
          if (data?.englishTab?.enhancedContent) {
            console.log('🔍 SPRINT 3: Lexical Content Debug:', {
              operation,
              hasEnhancedContent: !!data.englishTab.enhancedContent,
              contentType: typeof data.englishTab.enhancedContent,
              contentStructure:
                'Lexical content present (JSON.stringify disabled to prevent issues)',
              textExtraction:
                data.englishTab.enhancedContent?.root?.children?.[0]
                  ?.children || 'no-text-nodes',
            });
          }

          // ✅ SPRINT 5: Handle article type switching and hasOriginalSource field
          if (operation === 'update' && originalDoc) {
            const wasGenerated = originalDoc.articleType === 'generated';
            const nowCurated = data.articleType === 'curated';

            // If switching from generated to curated, preserve hasOriginalSource
            if (wasGenerated && nowCurated) {
              data.hasOriginalSource = true;

              console.log('🔄 Article Type Switch:', {
                from: 'generated',
                to: 'curated',
                preservingSourceAccess: true,
                articleId: originalDoc.id,
              });
            }

            // If creating new curated article, ensure hasOriginalSource is false
            if (
              data.articleType === 'curated' &&
              !originalDoc.hasOriginalSource
            ) {
              data.hasOriginalSource = false;
            }

            // Generated articles always have original source
            if (data.articleType === 'generated') {
              data.hasOriginalSource = true;
            }
          }

          // For new articles
          if (operation === 'create') {
            data.hasOriginalSource = data.articleType === 'generated';
          }

          // Validate article for publication before processing
          const validationResult = validateArticleForPublication({
            data,
            operation,
            req,
            originalDoc, // beforeChange has access to originalDoc for state transition detection
          });

          // Debug validation result
          if (isNativePublishing) {
            console.log('✅ Validation result:', {
              isValid: validationResult.isValid,
              errorCount: validationResult.errors.length,
              warningCount: validationResult.warnings.length,
            });
          }

          // If validation fails, throw an error to prevent save
          if (!validationResult.isValid) {
            console.error(
              '🚫 BLOCKING PUBLISH - Validation failed:',
              validationResult.errors
            );
            throw formatValidationError(validationResult);
          }

          // Log any warnings
          logValidationWarnings(validationResult, data?.title);

          // Set published metadata when transitioning to published via native system
          const isPublishing =
            data?._status === 'published' &&
            originalDoc?._status !== 'published';

          if (isPublishing) {
            // Set published metadata only if not already set
            if (!data.publishedAt) {
              data.publishedAt = new Date().toISOString();
            }
            if (!data.publishedBy && req?.user?.id) {
              data.publishedBy = req.user.id;
            }
          }

          // ✅ PHASE 2: Auto-calculate reading time when content changes
          if (data) {
            const germanContent = data.germanTab?.germanContent;
            const englishContent = data.englishTab?.enhancedContent;

            // Calculate reading time if content exists (prioritising German, then English)
            if (germanContent || englishContent) {
              const readTimeMinutes = calculateArticleReadingTime(
                germanContent || englishContent
              );
              if (readTimeMinutes > 0) {
                data.readTimeMinutes = readTimeMinutes;
              }
            }
          }

          // 🔍 SAVE DEBUGGING - Track successful completion
          console.log('✅ SAVE ATTEMPT COMPLETED:', {
            articleId: originalDoc?.id || 'new',
            operation,
            title: data?.title,
            readTimeMinutes: data?.readTimeMinutes,
            timestamp: new Date().toISOString(),
          });

          return data;
        } catch (error) {
          console.error('❌ SAVE ATTEMPT FAILED:', {
            articleId: originalDoc?.id || 'new',
            operation,
            error: error instanceof Error ? error.message : String(error),
            timestamp: new Date().toISOString(),
          });
          console.error('Error in collection beforeChange hook:', error);
          // Re-throw validation errors to prevent save
          throw error;
        }
      },
    ],
    // ✅ PHASE 2: Add ENHANCED cache invalidation when articles change
    afterChange: [
      // Enhanced cache invalidation with Vercel edge cache support + DATABASE VERIFICATION
      async ({ doc, previousDoc, operation }) => {
        try {
          // 🔍 DATABASE VERIFICATION - Check if save actually persisted
          console.log('🔍 AFTER CHANGE - DATABASE SAVE COMPLETED:', {
            articleId: doc.id,
            operation,
            title: doc.title,
            articleType: doc.articleType,
            updatedAt: doc.updatedAt,
            timestamp: new Date().toISOString(),
          });

          // DISABLED: Database verification query - testing if this causes rollback
          console.log(
            '🔍 DB VERIFICATION DISABLED - Testing if verification query causes rollback'
          );

          // DISABLED: This query might be causing the transaction rollback
          // try {
          //   const { getPayload } = await import('payload');
          //   const config = await import('@payload-config');
          //   const payload = await getPayload({ config: config.default });
          //   const dbDoc = await payload.findByID({
          //     collection: 'articles',
          //     id: doc.id,
          //     depth: 0,
          //   });
          //   console.log('🔍 DB VERIFICATION IMMEDIATELY AFTER SAVE:', {
          //     articleId: doc.id,
          //     docUpdatedAt: doc.updatedAt,
          //     dbUpdatedAt: dbDoc.updatedAt,
          //     timestampsMatch: doc.updatedAt === dbDoc.updatedAt,
          //     docTitle: doc.title,
          //     dbTitle: dbDoc.title,
          //     titlesMatch: doc.title === dbDoc.title,
          //     timestamp: new Date().toISOString(),
          //   });
          // } catch (dbVerifyError) {
          //   console.error('❌ DB VERIFICATION ERROR:', dbVerifyError);
          // }
          // Detect PUBLISH TRANSITION specifically (when user clicks "publish")
          const isPublishTransition =
            (doc as any)._status === 'published' &&
            (previousDoc as any)?._status !== 'published';

          // Detect updates to already published articles
          const isPublishedUpdate =
            (doc as any)._status === 'published' &&
            (previousDoc as any)?._status === 'published' &&
            operation === 'update';

          // Detect unpublishing (published → draft)
          const isUnpublishing =
            (previousDoc as any)?._status === 'published' &&
            (doc as any)._status !== 'published';

          if (isPublishTransition) {
            console.log('✅ PUBLISH SUCCESS - Data persisted to database:', {
              articleId: doc.id,
              title: doc.title,
              slug: doc.slug,
              status: (doc as any)._status,
              publishedAt: doc.publishedAt,
              publishedBy: doc.publishedBy,
              updatedAt: doc.updatedAt,
              createdAt: doc.createdAt,
              timestamp: new Date().toISOString(),
            });
          }

          console.log('🔍 Cache invalidation analysis:', {
            articleId: doc.id,
            operation,
            currentStatus: (doc as any)._status,
            previousStatus: (previousDoc as any)?._status,
            isPublishTransition,
            isPublishedUpdate,
            isUnpublishing,
          });

          // Only invalidate cache for meaningful changes
          if (isPublishTransition || isPublishedUpdate || isUnpublishing) {
            // Import the enhanced cache invalidation
            const { comprehensiveArticleInvalidation } = await import(
              '@/lib/cache/vercel-cache-fix'
            );

            const categories = (doc.categories || []).map((cat: any) => ({
              id: typeof cat === 'object' ? cat.id : cat,
              slug: typeof cat === 'object' ? cat.slug : 'unknown',
            }));

            // CRITICAL: Use AGGRESSIVE cache invalidation for publish transitions
            // Standard invalidation for published updates
            const forceEdgeInvalidation = isPublishTransition || isUnpublishing;

            await comprehensiveArticleInvalidation(
              doc.id,
              doc.slug,
              categories,
              {
                isNewArticle: operation === 'create',
                isPublished: (doc as any)._status === 'published',
                forceEdgeInvalidation, // CRITICAL: Only force Vercel edge cache clear on publish/unpublish
              }
            );

            if (isPublishTransition) {
              console.log(
                `🚀 PUBLISH DETECTED: Enhanced cache invalidation completed for article ${doc.id}`
              );
            } else if (isPublishedUpdate) {
              console.log(
                `📝 UPDATE DETECTED: Standard cache invalidation completed for published article ${doc.id}`
              );
            } else if (isUnpublishing) {
              console.log(
                `📤 UNPUBLISH DETECTED: Enhanced cache invalidation completed for article ${doc.id}`
              );
            }
          } else {
            console.log(
              `⏭️ SKIP: No cache invalidation needed for article ${doc.id} (draft updates or non-meaningful changes)`
            );
          }

          return doc;
        } catch (error) {
          console.error('❌ Enhanced cache invalidation hook error:', error);

          // Fallback to original system if enhanced fails
          try {
            console.log('🔄 Falling back to standard cache invalidation...');
            const { invalidateArticleCaches } = await import(
              '@/lib/cache/invalidation'
            );
            const categories = doc.categories || [];
            await invalidateArticleCaches(doc.id, categories, {
              isNewArticle: operation === 'create',
              isPublished: (doc as any)._status === 'published',
              revalidatePaths: true,
            });
          } catch (fallbackError) {
            console.error(
              '❌ Fallback cache invalidation also failed:',
              fallbackError
            );
          }

          return doc; // Don't fail the operation if cache invalidation fails
        }
      },
    ],
    afterDelete: [
      async ({ doc, req }) => {
        // Import server-only functions within the hook context
        const { revalidateTag, revalidatePath } = await import('next/cache');

        try {
          // Only invalidate cache if not explicitly disabled
          if (!req.context?.disableRevalidate) {
            // Always invalidate cache when deleting published articles
            if (doc?._status === 'published') {
              req.payload.logger.info(
                `Invalidating homepage cache for deleted article: ${doc.title}`
              );

              // Clear homepage cache
              revalidatePath('/');

              // Clear all article caches
              revalidateTag('articles');

              // Clear specific tier caches
              revalidateTag('tier-1');
              revalidateTag('tier-2');
              revalidateTag('tier-3');

              // Clear layout cache for tier-1 advanced layout
              revalidateTag('layout');

              // Clear tier-specific cache if placement was set
              if (doc.placement) {
                revalidateTag(`tier-${doc.placement.split('-')[1]}`);
              }
            }
          }
        } catch (error) {
          // Don't fail the delete if cache invalidation fails
          console.error('Error in cache invalidation hook:', error);
        }

        return doc;
      },
    ],
  },
  // Enable versioning with drafts for preview functionality
  // Using both workflowStage field and native drafts for comprehensive lifecycle management
  versions: {
    drafts: true,
  },
};

export default Articles;
