import { Suspense } from 'react';
import { getCachedFeaturedArticles } from '@/lib/cache/articles';
import NewsCard from '@/components/NewsCard';
import { Skeleton } from '@/components/ui/skeleton';

interface FeaturedArticlesSectionProps {
  maxArticles?: number;
  locale?: 'de' | 'en';
  className?: string;
  currentArticleId?: string | number; // Exclude this article from featured list
}

// Loading skeleton component for featured articles
function FeaturedArticlesSkeleton({
  maxArticles = 4,
}: {
  maxArticles?: number;
}) {
  return (
    <div className="space-y-4">
      <Skeleton className="h-4 w-20 ml-4" /> {/* Empfohlen heading */}
      <div className="space-y-3">
        {Array.from({ length: maxArticles }).map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-3 w-16" /> {/* Category */}
            <Skeleton className="h-4 w-full" /> {/* Title line 1 */}
            <Skeleton className="h-4 w-4/5" /> {/* Title line 2 */}
            <Skeleton className="h-3 w-20" /> {/* Date */}
          </div>
        ))}
      </div>
    </div>
  );
}

// Async wrapper that handles conditional rendering of the entire section
async function FeaturedArticlesSectionAsync({
  maxArticles = 4,
  locale = 'de',
  className = '',
  currentArticleId,
}: FeaturedArticlesSectionProps) {
  try {
    // Pre-check if there are any featured articles to show
    const fetchCount = currentArticleId ? maxArticles + 2 : maxArticles;
    const getFeaturedArticles = getCachedFeaturedArticles(fetchCount);
    const result = await getFeaturedArticles();

    let articles = result.docs;

    // Filter out the current article if we're on an article page
    if (currentArticleId && articles) {
      articles = articles.filter(article => {
        const articleIdStr = String(article.id);
        const currentIdStr = String(currentArticleId);
        return articleIdStr !== currentIdStr;
      });
    }

    // If no articles after filtering, don't render anything
    if (!articles || articles.length === 0) {
      return null;
    }

    // Limit to the requested number of articles after filtering
    if (articles.length > maxArticles) {
      articles = articles.slice(0, maxArticles);
    }

    return (
      <section className={className} aria-label="Empfohlene Artikel">
        <div className="space-y-4">
          {/* Featured heading - only shown when there are articles */}
          <h3
            id="featured-articles-heading"
            className="font-sans font-medium text-foreground dark:text-gray-400 text-sm pl-4"
          >
            Empfohlen
          </h3>

          <div className="space-y-3">
            {articles.map((article, index) => (
              <NewsCard
                key={article.id}
                article={article}
                variant="default"
                showDescription={false}
                locale={locale}
                priority={index === 0} // Priority for first featured article
                className="border-0 bg-[#f8f8f0] dark:bg-neutral-900 shadow-none hover:bg-[#e9e9d6] dark:hover:bg-neutral-900 transition-colors"
              />
            ))}
          </div>
        </div>
      </section>
    );
  } catch (error) {
    console.error('Error fetching featured articles:', error);
    return null;
  }
}

// Main featured articles section component
export default function FeaturedArticlesSection({
  maxArticles = 4,
  locale = 'de',
  className = '',
  currentArticleId,
}: FeaturedArticlesSectionProps) {
  return (
    <Suspense fallback={<FeaturedArticlesSkeleton maxArticles={maxArticles} />}>
      <FeaturedArticlesSectionAsync
        maxArticles={maxArticles}
        locale={locale}
        className={className}
        currentArticleId={currentArticleId}
      />
    </Suspense>
  );
}

// Export the skeleton for use in other loading states
export { FeaturedArticlesSkeleton };
