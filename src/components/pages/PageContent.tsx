import React from 'react';
import Image from 'next/image';
import type { Page, Media } from '@/payload-types';
import { extractLinkUrl } from '@/lib/types/lexical-nodes';

interface PageContentProps {
  page: Page;
  locale?: 'de' | 'en';
}

// Helper function to check if node types are inline-only
const isInlineNodeType = (nodeType: string): boolean => {
  const inlineTypes = ['text', 'linebreak', 'link'];
  return inlineTypes.includes(nodeType);
};

// Helper function to check if all children are inline elements
const areAllChildrenInline = (children: any[]): boolean => {
  if (!Array.isArray(children)) return true;

  return children.every(child => {
    if (!child || typeof child !== 'object') return true;
    return isInlineNodeType(child.type);
  });
};

// Comprehensive Lexical content renderer supporting all configured node types
function renderLexicalContent(lexicalData: any): React.ReactNode {
  if (!lexicalData || typeof lexicalData !== 'object') {
    return null;
  }

  const root = lexicalData.root;
  if (!root || !Array.isArray(root.children)) {
    return null;
  }

  const renderNode = (node: any, index: number): React.ReactNode => {
    if (!node || typeof node !== 'object') {
      return null;
    }

    switch (node.type) {
      case 'paragraph':
        // Check if paragraph contains only inline elements
        const hasInlineChildrenOnly = areAllChildrenInline(node.children);
        const paragraphChildren = Array.isArray(node.children)
          ? node.children.map((child: any, i: number) => renderNode(child, i))
          : null;

        // Use <p> for inline-only content, <div> for mixed/block content
        if (hasInlineChildrenOnly) {
          return (
            <p key={index} className="mb-4 font-serif text-base/8">
              {paragraphChildren}
            </p>
          );
        } else {
          return (
            <div key={index} className="mb-4 font-serif text-base/8">
              {paragraphChildren}
            </div>
          );
        }

      case 'heading':
        const tag = node.tag || 'h3';
        const headingClasses = {
          h1: 'text-2xl md:text-3xl font-bold mb-6 mt-8 text-gray-900 dark:text-gray-100',
          h2: 'text-xl md:text-2xl font-semibold mb-5 mt-7 text-gray-900 dark:text-gray-100',
          h3: 'text-lg md:text-xl font-semibold mb-4 mt-6 text-gray-900 dark:text-gray-100',
          h4: 'text-base md:text-lg font-medium mb-3 mt-5 text-gray-800 dark:text-gray-200',
          h5: 'text-sm md:text-base font-medium mb-3 mt-4 text-gray-800 dark:text-gray-200',
          h6: 'text-sm font-medium mb-2 mt-3 text-gray-800 dark:text-gray-200',
        };

        const className = `font-serif ${headingClasses[tag as keyof typeof headingClasses] || headingClasses.h3}`;
        const headingChildren = Array.isArray(node.children)
          ? node.children.map((child: any, i: number) => renderNode(child, i))
          : null;

        return React.createElement(
          tag,
          { key: index, className },
          headingChildren
        );

      case 'text':
        let textElement: React.ReactNode = node.text || '';

        // Handle text formatting using Lexical format flags
        if (node.format && typeof node.format === 'number') {
          if (node.format & 1) {
            // Bold
            textElement = <strong key={index}>{textElement}</strong>;
          }
          if (node.format & 2) {
            // Italic
            textElement = <em key={index}>{textElement}</em>;
          }
          if (node.format & 4) {
            // Underline
            textElement = <u key={index}>{textElement}</u>;
          }
          if (node.format & 8) {
            // Strikethrough
            textElement = <del key={index}>{textElement}</del>;
          }
        }

        // Return text directly without unnecessary span wrapper
        return textElement;

      case 'linebreak':
        return <br key={index} />;

      // List support (ordered and unordered)
      case 'list':
        const ListTag = node.listType === 'number' ? 'ol' : 'ul';
        const listClassName =
          node.listType === 'number'
            ? 'list-decimal list-inside mb-4 ml-4 space-y-2'
            : 'list-disc list-inside mb-4 ml-4 space-y-2';

        return (
          <ListTag key={index} className={listClassName}>
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </ListTag>
        );

      case 'listitem':
        return (
          <li
            key={index}
            className="font-serif text-base/7 text-gray-800 dark:text-gray-200"
          >
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </li>
        );

      // Blockquote support
      case 'quote':
        return (
          <blockquote
            key={index}
            className="border-l-4 border-[#B08D57] dark:border-[#D4AF37] pl-4 py-2 my-6 italic font-serif text-gray-700 dark:text-gray-300 bg-gray-50 dark:bg-gray-800/50 rounded-r-lg"
          >
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </blockquote>
        );

      // Horizontal rule support
      case 'horizontalrule':
        return (
          <hr
            key={index}
            className="my-8 border-0 h-px bg-linear-to-r from-transparent via-gray-300 dark:via-gray-600 to-transparent"
          />
        );

      // Upload/Image support
      case 'upload':
        const uploadValue = node.value;
        if (!uploadValue || typeof uploadValue !== 'object') {
          return null;
        }

        // Handle both ID reference and full media object
        const mediaUrl =
          typeof uploadValue === 'object' && uploadValue.url
            ? uploadValue.url
            : null;

        const altText = uploadValue.alt || 'Page image';
        const caption = uploadValue.caption;

        if (!mediaUrl) {
          return null;
        }

        return (
          <figure key={index} className="my-6">
            <div className="overflow-hidden rounded-lg">
              <Image
                src={mediaUrl}
                alt={altText}
                width={800}
                height={400}
                className="w-full h-auto object-cover"
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 800px"
              />
            </div>
            {caption && (
              <figcaption className="mt-2 text-sm text-gray-600 dark:text-gray-400 font-sans italic text-center">
                {caption}
              </figcaption>
            )}
          </figure>
        );

      // Link support - PayloadCMS native handling
      case 'link':
        console.log('🔗 Processing link node in PageContent:', node);
        const linkUrl = extractLinkUrl(node);
        // Extract rel and newTab from all possible PayloadCMS structures
        let linkRel = '';
        let target: string | undefined;

        if (node.fields?.rel) {
          linkRel = Array.isArray(node.fields.rel)
            ? node.fields.rel.join(' ')
            : node.fields.rel;
        } else if (node.rel) {
          linkRel = Array.isArray(node.rel) ? node.rel.join(' ') : node.rel;
        } else if (node.attributes?.rel) {
          linkRel = Array.isArray(node.attributes.rel)
            ? node.attributes.rel.join(' ')
            : node.attributes.rel;
        }

        if (node.fields?.newTab || node.newTab || node.attributes?.newTab) {
          target = '_blank';
        }

        // Link attributes extracted

        console.log('🔗 Extracted link URL:', linkUrl);

        // Don't render as link if no valid URL found, but still render children
        if (!linkUrl || linkUrl === '#') {
          console.warn(
            'PayloadCMS link node found but no valid URL, rendering children directly:',
            node
          );
          // Render children directly without wrapper to avoid nesting issues
          return Array.isArray(node.children)
            ? node.children.map((child: any, i: number) => renderNode(child, i))
            : null;
        }

        return (
          <a
            key={index}
            href={linkUrl}
            target={target}
            rel={linkRel}
            className="text-[#B08D57] dark:text-[#D4AF37] hover:underline font-medium transition-colors"
          >
            {Array.isArray(node.children)
              ? node.children.map((child: any, i: number) =>
                  renderNode(child, i)
                )
              : null}
          </a>
        );

      default:
        // Enhanced fallback for unknown node types
        console.warn(`Unknown Lexical node type: ${node.type}`, node);

        // If it has children, try to render them
        if (Array.isArray(node.children)) {
          return (
            <div key={index} className="unknown-lexical-node">
              {node.children.map((child: any, i: number) =>
                renderNode(child, i)
              )}
            </div>
          );
        }

        // If it's a text-like node, try to render the text directly
        if (node.text && typeof node.text === 'string') {
          return node.text;
        }

        return null;
    }
  };

  return (
    <div className="lexical-content">
      {root.children.map((child: any, index: number) =>
        renderNode(child, index)
      )}
    </div>
  );
}

export default function PageContent({ page, locale = 'de' }: PageContentProps) {
  // Handle featuredImage which can be number (ID) or Media object
  const featuredImage =
    typeof page.featuredImage === 'object'
      ? (page.featuredImage as Media)
      : null;

  // Prioritise content: German → English → fallback
  const content =
    page.germanTab?.germanContent || page.englishTab?.content || null;

  // Get title for alt text fallback
  const title =
    page.germanTab?.germanTitle || page.englishTab?.title || page.title;

  return (
    <div className="space-y-6 lg:space-y-8">
      {/* Prominent hero image */}
      {featuredImage?.url && (
        <div className="overflow-hidden rounded-lg">
          <Image
            src={featuredImage.sizes?.hero?.url || featuredImage.url}
            alt={featuredImage.alt || title}
            width={1456}
            height={816}
            className="w-full h-auto object-cover"
            priority={true}
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 66vw, 800px"
          />
        </div>
      )}

      {/* Page content with proper typography */}
      <article className="prose prose-base max-w-none dark:prose-invert">
        <div className="font-serif text-gray-800 dark:text-gray-200 text-base/8">
          {content ? (
            renderLexicalContent(content)
          ) : (
            <div className="text-gray-500 dark:text-gray-400 italic font-sans">
              <p>Content is being processed and will be available soon.</p>
            </div>
          )}
        </div>
      </article>
    </div>
  );
}
