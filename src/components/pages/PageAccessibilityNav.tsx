'use client';

import Link from 'next/link';
import { useRef } from 'react';

interface PageAccessibilityNavProps {
  isLoading?: boolean;
  hasError?: boolean;
}

export default function PageAccessibilityNav({
  isLoading = false,
  hasError = false,
}: PageAccessibilityNavProps) {
  const liveRegionRef = useRef<HTMLDivElement>(null);

  // Smooth scroll function with error handling and screen reader announcements
  const handleSkipToContent = (targetId: string) => {
    try {
      const element = document.getElementById(targetId);
      if (element) {
        element.scrollIntoView({ behavior: 'smooth', block: 'start' });

        // Announce navigation to screen readers
        if (liveRegionRef.current) {
          liveRegionRef.current.textContent = `Navigated to ${targetId.replace('-', ' ')}`;
        }
      }
    } catch (error) {
      console.warn(`Failed to navigate to ${targetId}:`, error);
    }
  };

  return (
    <>
      {/* Skip Links */}
      <nav
        className="sr-only focus-within:not-sr-only"
        aria-label="Skip navigation"
      >
        <div className="fixed top-0 left-0 z-50 bg-background border border-border p-4 m-4 rounded-md shadow-lg">
          <ul className="flex flex-col gap-2 text-sm">
            <li>
              <a
                href="#page-content"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
                onClick={e => {
                  e.preventDefault();
                  handleSkipToContent('page-content');
                }}
              >
                Skip to page content
              </a>
            </li>
            <li>
              <a
                href="#related-articles"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
                onClick={e => {
                  e.preventDefault();
                  handleSkipToContent('related-articles');
                }}
              >
                Skip to related articles
              </a>
            </li>
            <li>
              <Link
                href="/"
                className="text-primary hover:text-primary/80 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 rounded-sm px-2 py-1"
              >
                Return to homepage
              </Link>
            </li>
          </ul>
        </div>
      </nav>

      {/* ARIA Live Region */}
      <div
        ref={liveRegionRef}
        className="sr-only"
        aria-live="polite"
        aria-atomic="true"
        aria-relevant="text"
      />

      {/* Loading/Error States */}
      {isLoading && (
        <div className="sr-only" aria-live="polite">
          Page content is loading...
        </div>
      )}
      {hasError && (
        <div className="sr-only" aria-live="assertive">
          Error loading page content. Please try refreshing the page.
        </div>
      )}
    </>
  );
}
