import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen } from '@testing-library/react';
import '@testing-library/jest-dom';
import { MarketWatch } from '../MarketWatch';
import { MARKET_WATCH_TAB_SETS } from '../../ticker-tape/TickerTape.config';

// Mock next-themes
vi.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    systemTheme: 'light',
    resolvedTheme: 'light',
  }),
}));

describe('MarketWatch Component', () => {
  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders without crashing with valid tabs', () => {
    render(<MarketWatch tabs={MARKET_WATCH_TAB_SETS.homepage.tabs} />);

    // Check for the main container
    const container = screen.getByRole('region', {
      name: /market overview widget/i,
    });
    expect(container).toBeInTheDocument();
  });

  it('renders error message for empty tabs array', () => {
    render(<MarketWatch tabs={[]} />);

    expect(
      screen.getByText(/error: invalid market watch tabs provided/i)
    ).toBeInTheDocument();
    expect(
      screen.getByText(
        /please check that all tabs have valid titles and symbols/i
      )
    ).toBeInTheDocument();
  });

  it('renders error message for invalid tabs', () => {
    const invalidTabs = [
      {
        title: '',
        originalTitle: '',
        symbols: [], // Empty symbols array
      },
      {
        title: 'Valid Title',
        originalTitle: 'Valid Title',
        symbols: [
          { s: '', d: 'Invalid Symbol' }, // Empty symbol
        ],
      },
    ];

    render(<MarketWatch tabs={invalidTabs} />);

    expect(
      screen.getByText(/error: invalid market watch tabs provided/i)
    ).toBeInTheDocument();
  });

  it('applies custom className', () => {
    const customClass = 'my-custom-market-watch';
    render(
      <MarketWatch
        tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
        className={customClass}
      />
    );

    const wrapper = screen.getByRole('region').parentElement;
    expect(wrapper).toHaveClass(customClass);
  });

  it('renders with homepage tab set', () => {
    render(<MarketWatch tabs={MARKET_WATCH_TAB_SETS.homepage.tabs} />);

    const container = screen.getByRole('region', {
      name: /market overview widget/i,
    });
    expect(container).toBeInTheDocument();
    expect(container).toHaveClass('tradingview-widget-container');
  });

  it('renders with investment tab set', () => {
    render(<MarketWatch tabs={MARKET_WATCH_TAB_SETS.investment.tabs} />);

    const container = screen.getByRole('region', {
      name: /market overview widget/i,
    });
    expect(container).toBeInTheDocument();
  });

  it('renders with technology tab set', () => {
    render(<MarketWatch tabs={MARKET_WATCH_TAB_SETS.technology.tabs} />);

    const container = screen.getByRole('region', {
      name: /market overview widget/i,
    });
    expect(container).toBeInTheDocument();
  });

  it('has proper accessibility attributes', () => {
    render(<MarketWatch tabs={MARKET_WATCH_TAB_SETS.homepage.tabs} />);

    const container = screen.getByRole('region', {
      name: /market overview widget/i,
    });

    expect(container).toHaveAttribute('aria-label', 'Market overview widget');
    expect(container).toHaveAttribute('aria-live', 'polite');
  });

  it('accepts all supported props', () => {
    render(
      <MarketWatch
        tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
        colorTheme="dark"
        dateRange="1M"
        locale="de"
        largeChartUrl="https://example.com"
        isTransparent={false}
        showFloatingTooltip={false}
        showSymbolLogo={false}
        showChart={false}
        width="500px"
        height="300px"
        supportHost="https://custom.host.com"
        className="test-class"
        colors={{
          plotLineColorGrowing: 'rgba(0, 255, 0, 1)',
          plotLineColorFalling: 'rgba(255, 0, 0, 1)',
        }}
      />
    );

    const container = screen.getByRole('region', {
      name: /market overview widget/i,
    });
    expect(container).toBeInTheDocument();
  });

  it('renders loading skeleton while loading', () => {
    const { container } = render(
      <MarketWatch tabs={MARKET_WATCH_TAB_SETS.homepage.tabs} />
    );

    // Should show loading text
    expect(screen.getByText(/loading market overview/i)).toBeInTheDocument();

    // Should show animated dots
    const loadingDots = container.querySelectorAll('.animate-bounce');
    expect(loadingDots).toHaveLength(3);
  });

  it('hides TradingView copyright link', () => {
    const { container } = render(
      <MarketWatch tabs={MARKET_WATCH_TAB_SETS.homepage.tabs} />
    );

    // Copyright should be hidden
    const copyrightDiv = container.querySelector(
      '.tradingview-widget-copyright'
    );
    expect(copyrightDiv).toHaveClass('hidden');

    // Text should not be visible to users
    const copyrightText = screen.queryByText(
      /track all markets on tradingview/i
    );
    expect(copyrightText).not.toBeInTheDocument();
  });

  it('properly remounts with different themes', () => {
    const { rerender } = render(
      <MarketWatch
        tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
        colorTheme="light"
      />
    );

    const lightWrapper = screen.getByRole('region').parentElement;
    expect(lightWrapper).toHaveClass('market-watch-wrapper');

    rerender(
      <MarketWatch
        tabs={MARKET_WATCH_TAB_SETS.homepage.tabs}
        colorTheme="dark"
      />
    );

    const darkWrapper = screen.getByRole('region').parentElement;
    expect(darkWrapper).toHaveClass('market-watch-wrapper');
    // Component should still be present and functional after theme change
    expect(screen.getByRole('region')).toBeInTheDocument();
  });

  it('handles theme detection when no explicit theme provided', () => {
    // This test relies on the mocked useTheme hook returning 'light'
    render(<MarketWatch tabs={MARKET_WATCH_TAB_SETS.homepage.tabs} />);

    const container = screen.getByRole('region');
    expect(container).toBeInTheDocument();
    // The component should render without errors when theme is auto-detected
  });
});
