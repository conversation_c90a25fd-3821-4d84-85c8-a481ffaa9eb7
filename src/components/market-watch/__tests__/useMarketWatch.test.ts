import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook } from '@testing-library/react';
import { useMarketWatch, useMarketWatchTheme } from '../useMarketWatch';
import { MARKET_WATCH_TAB_SETS } from '../../ticker-tape/TickerTape.config';
import { buildMarketWatchConfig } from '../MarketWatch.config';

// Mock next-themes
vi.mock('next-themes', () => ({
  useTheme: () => ({
    theme: 'light',
    systemTheme: 'light',
    resolvedTheme: 'light',
  }),
}));

// Mock DOM methods
Object.defineProperty(document, 'createElement', {
  writable: true,
  value: vi.fn(() => ({
    src: '',
    type: '',
    async: false,
    innerHTML: '',
    onload: null,
    onerror: null,
    remove: vi.fn(),
  })),
});

describe('useMarketWatchTheme', () => {
  it('returns light theme by default before mounting', () => {
    const { result } = renderHook(() => useMarketWatchTheme());
    expect(result.current).toBe('light');
  });

  it('detects theme correctly after mounting', async () => {
    const { result, rerender } = renderHook(() => useMarketWatchTheme());

    // Initially returns light (before mount)
    expect(result.current).toBe('light');

    // After rerender (simulating mount), should still be light based on mock
    rerender();
    expect(result.current).toBe('light');
  });
});

describe('useMarketWatch', () => {
  let mockContainer: HTMLDivElement;

  beforeEach(() => {
    vi.clearAllMocks();

    // Create a mock container element
    mockContainer = document.createElement('div');
    mockContainer.className = 'tradingview-widget-container';

    // Mock querySelector to return our mock elements
    mockContainer.querySelector = vi.fn((selector: string) => {
      if (selector === '.tradingview-widget-container__widget') {
        const widget = document.createElement('div');
        widget.className = 'tradingview-widget-container__widget';
        widget.innerHTML = '';
        return widget;
      }
      return null;
    });

    mockContainer.querySelectorAll = vi.fn(() => []);
    mockContainer.appendChild = vi.fn();
    mockContainer.classList = {
      add: vi.fn(),
    } as any;
  });

  afterEach(() => {
    vi.restoreAllMocks();
  });

  it('initializes with loading state', () => {
    const config = buildMarketWatchConfig(
      {
        tabs: MARKET_WATCH_TAB_SETS.homepage.tabs,
      },
      'light'
    );

    const { result } = renderHook(() => useMarketWatch(config));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(null);
    expect(result.current.containerRef).toBeDefined();
  });

  it('handles missing container gracefully', () => {
    const config = buildMarketWatchConfig(
      {
        tabs: MARKET_WATCH_TAB_SETS.homepage.tabs,
      },
      'light'
    );

    const { result } = renderHook(() => useMarketWatch(config));

    // Should not throw error even without container
    expect(result.current.containerRef).toBeDefined();
    expect(result.current.isLoading).toBe(true);
  });

  it('waits for proper theme before loading widget', () => {
    const config = buildMarketWatchConfig(
      {
        tabs: MARKET_WATCH_TAB_SETS.homepage.tabs,
      },
      '' as any // Invalid theme
    );

    const { result } = renderHook(() => useMarketWatch(config));

    expect(result.current.isLoading).toBe(true);
    // Should wait for proper theme resolution
  });

  it('creates script element with correct configuration', () => {
    const config = buildMarketWatchConfig(
      {
        tabs: MARKET_WATCH_TAB_SETS.homepage.tabs,
        dateRange: '1M',
        locale: 'de',
      },
      'dark'
    );

    // Mock the container ref to return our mock container
    const { result } = renderHook(() => useMarketWatch(config));

    if (result.current.containerRef.current) {
      result.current.containerRef.current = mockContainer;
    }

    expect(result.current.containerRef).toBeDefined();
  });

  it('handles different configuration options', () => {
    const customConfig = buildMarketWatchConfig(
      {
        tabs: MARKET_WATCH_TAB_SETS.technology.tabs,
        dateRange: '6M',
        locale: 'en',
        isTransparent: false,
        showFloatingTooltip: false,
        showSymbolLogo: false,
        showChart: false,
      },
      'light'
    );

    const { result } = renderHook(() => useMarketWatch(customConfig));

    expect(result.current.isLoading).toBe(true);
    expect(result.current.error).toBe(null);
  });

  it('recreates widget when configuration changes', () => {
    const initialConfig = buildMarketWatchConfig(
      {
        tabs: MARKET_WATCH_TAB_SETS.homepage.tabs,
      },
      'light'
    );

    const { result, rerender } = renderHook(
      ({ config }) => useMarketWatch(config),
      {
        initialProps: { config: initialConfig },
      }
    );

    const initialRef = result.current.containerRef;

    // Change configuration
    const newConfig = buildMarketWatchConfig(
      {
        tabs: MARKET_WATCH_TAB_SETS.investment.tabs,
      },
      'dark'
    );

    rerender({ config: newConfig });

    // Container ref should remain the same, but internal state should reset
    expect(result.current.containerRef).toBe(initialRef);
    expect(result.current.isLoading).toBe(true);
  });
});
