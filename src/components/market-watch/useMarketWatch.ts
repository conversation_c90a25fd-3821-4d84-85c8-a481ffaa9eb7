'use client';

import { useEffect, useRef, useState, useMemo } from 'react';
import { useTheme } from 'next-themes';
import type {
  MarketWatchConfig,
  UseMarketWatchReturn,
} from './MarketWatch.types';

/**
 * Hook for theme-aware market watch configuration
 */
export function useMarketWatchTheme() {
  const { theme, systemTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  return useMemo(() => {
    // Return light theme until mounted to prevent hydration mismatch
    if (!mounted) {
      return 'light';
    }

    // Use resolvedTheme first as it's the most reliable
    if (resolvedTheme) {
      return resolvedTheme === 'dark' ? 'dark' : 'light';
    }

    // Fallback to manual resolution
    const effectiveTheme = theme === 'system' ? systemTheme : theme;
    return effectiveTheme === 'dark' ? 'dark' : 'light';
  }, [mounted, theme, systemTheme, resolvedTheme]);
}

/**
 * Hook that manages the TradingView Market Watch widget lifecycle
 */
export function useMarketWatch(
  config: MarketWatchConfig
): UseMarketWatchReturn {
  const containerRef = useRef<HTMLDivElement>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!containerRef.current) return;

    // Don't load widget until we have a proper theme
    if (!config.colorTheme) {
      console.log('MarketWatch: Waiting for theme to be resolved...');
      return;
    }

    const container = containerRef.current;
    let timeoutId: NodeJS.Timeout | null = null;

    const loadWidget = () => {
      try {
        setIsLoading(true);
        setError(null);

        // Complete DOM cleanup: TradingView widgets inject their own DOM and styles
        // We need to fully reset the container to prevent theme artifacts
        const widgetElement = container.querySelector(
          '.tradingview-widget-container__widget'
        );

        // Clear any TradingView injected content
        if (widgetElement) {
          widgetElement.innerHTML = '';
        }

        // Remove any existing scripts
        const existingScripts = container.querySelectorAll('script');
        existingScripts.forEach(script => script.remove());

        // Create and configure the script element
        const scriptElement = document.createElement('script');
        scriptElement.type = 'text/javascript';
        scriptElement.src =
          'https://s3.tradingview.com/external-embedding/embed-widget-market-overview.js';
        scriptElement.async = true;

        // Build the complete configuration
        const widgetConfig = {
          colorTheme: config.colorTheme,
          dateRange: config.dateRange,
          locale: config.locale,
          largeChartUrl: config.largeChartUrl,
          isTransparent: config.isTransparent,
          showFloatingTooltip: config.showFloatingTooltip,
          plotLineColorGrowing: config.plotLineColorGrowing,
          plotLineColorFalling: config.plotLineColorFalling,
          gridLineColor: config.gridLineColor,
          scaleFontColor: config.scaleFontColor,
          belowLineFillColorGrowing: config.belowLineFillColorGrowing,
          belowLineFillColorFalling: config.belowLineFillColorFalling,
          belowLineFillColorGrowingBottom:
            config.belowLineFillColorGrowingBottom,
          belowLineFillColorFallingBottom:
            config.belowLineFillColorFallingBottom,
          symbolActiveColor: config.symbolActiveColor,
          tabs: config.tabs,
          support_host: config.supportHost,
          width: config.width,
          height: config.height,
          showSymbolLogo: config.showSymbolLogo,
          showChart: config.showChart,
          // Additional optional properties
          ...(config.timezone && { timezone: config.timezone }),
          ...(config.autosize !== undefined && { autosize: config.autosize }),
          ...(config.container_id && { container_id: config.container_id }),
        };

        console.log('MarketWatch: Loading widget with config:', {
          colorTheme: config.colorTheme,
          isTransparent: config.isTransparent,
          tabs: config.tabs.length + ' tabs',
          dateRange: config.dateRange,
        });

        // Set the configuration as the script's content
        scriptElement.innerHTML = JSON.stringify(widgetConfig);

        // Set timeout for error handling
        timeoutId = setTimeout(() => {
          setError('Market Watch widget failed to load');
          setIsLoading(false);
        }, 15000);

        // Handle load events
        scriptElement.onload = () => {
          console.log('MarketWatch: TradingView script loaded successfully');
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }

          // Give the widget time to render
          setTimeout(() => {
            container.classList.add('loaded');
            setIsLoading(false);
          }, 1500); // Market overview takes a bit longer to load than ticker tape
        };

        scriptElement.onerror = () => {
          if (timeoutId) {
            clearTimeout(timeoutId);
            timeoutId = null;
          }
          setError('Failed to load MarketWatch TradingView script');
          setIsLoading(false);
        };

        // Important: Add the script to the same container as the widget
        container.appendChild(scriptElement);
      } catch (err) {
        console.error('MarketWatch widget initialization error:', err);
        setError(err instanceof Error ? err.message : 'Unknown error');
        setIsLoading(false);
      }
    };

    // Load the widget
    loadWidget();

    return () => {
      if (timeoutId) clearTimeout(timeoutId);
      if (container) {
        // Only clear TradingView injected content, preserve React structure
        const widgetElement = container.querySelector(
          '.tradingview-widget-container__widget'
        );
        if (widgetElement) {
          widgetElement.innerHTML = '';
        }
        // Remove any scripts
        const scripts = container.querySelectorAll('script');
        scripts.forEach(script => script.remove());
      }
    };
  }, [
    // Recreate when any of these change
    JSON.stringify(config),
    // eslint-disable-next-line react-hooks/exhaustive-deps
  ]);

  return {
    containerRef,
    isLoading,
    error,
  };
}
