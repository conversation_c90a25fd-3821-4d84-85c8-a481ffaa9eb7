'use client';

import React, { memo } from 'react';
import { cn } from '@/lib/utils';
import { useMarketWatch, useMarketWatchTheme } from './useMarketWatch';
import { buildMarketWatchConfig } from './MarketWatch.config';
import { validateMarketWatchTabs } from '../ticker-tape/TickerTape.config';
import type { MarketWatchProps } from './MarketWatch.types';

/**
 * TradingView Market Watch Component
 *
 * A reusable component that displays a TradingView market overview widget with
 * automatic theme detection, configurable tabs, and comprehensive customisation options.
 */
function MarketWatchComponent({
  tabs,
  className,
  colorTheme,
  dateRange = '12M',
  locale = 'en',
  largeChartUrl = '',
  isTransparent = true,
  showFloatingTooltip = true,
  showSymbolLogo = true,
  showChart = true,
  width = '100%',
  height = '100%',
  supportHost = 'https://www.tradingview.com',
  colors,
  timezone,
  autosize,
  container_id,
}: MarketWatchProps) {
  const detectedTheme = useMarketWatchTheme();

  // Use provided theme or detected theme
  const effectiveTheme = colorTheme || detectedTheme;

  // Smart transparency: Force opaque in dark mode to ensure proper theming if not explicitly set
  const smartTransparent =
    effectiveTheme === 'dark' && isTransparent === true ? false : isTransparent;

  // Build complete configuration
  const config = buildMarketWatchConfig(
    {
      tabs,
      dateRange,
      locale,
      largeChartUrl,
      isTransparent: smartTransparent,
      showFloatingTooltip,
      showSymbolLogo,
      showChart,
      width,
      height,
      supportHost,
      timezone,
      autosize,
      container_id,
    },
    effectiveTheme,
    colors
  );

  const { containerRef, isLoading, error } = useMarketWatch(config);

  // Validate tabs after hooks
  if (!validateMarketWatchTabs(tabs)) {
    console.error('MarketWatch: Invalid tabs provided', tabs);
    return (
      <div
        className={cn(
          'market-watch-error p-4 text-center text-red-500',
          className
        )}
      >
        <p>Error: Invalid market watch tabs provided</p>
        <p className="text-xs text-muted-foreground mt-1">
          Please check that all tabs have valid titles and symbols
        </p>
      </div>
    );
  }

  // Handle errors
  if (error) {
    return (
      <div className={cn('market-watch-error p-4 text-center', className)}>
        <div className="rounded-md bg-destructive/10 p-3">
          <p className="text-sm text-destructive">
            Failed to load market watch: {error}
          </p>
          <p className="text-xs text-muted-foreground mt-1">
            Please check your internet connection and try again.
          </p>
        </div>
      </div>
    );
  }

  return (
    <div
      key={`market-watch-${effectiveTheme}`}
      className={cn('market-watch-wrapper relative w-full', className)}
    >
      {/* Static HTML structure like the TradingView examples */}
      <div
        className="tradingview-widget-container"
        ref={containerRef}
        role="region"
        aria-label="Market overview widget"
        aria-live="polite"
      >
        <div className="tradingview-widget-container__widget"></div>
        <div className="tradingview-widget-copyright hidden">
          {/* TradingView copyright hidden with permission */}
        </div>
      </div>

      {/* Loading skeleton - absolutely positioned overlay */}
      {isLoading && (
        <div
          className={cn(
            'absolute inset-0 rounded-lg flex flex-col items-center justify-center gap-4',
            'min-h-96', // Use Tailwind scale instead of arbitrary value
            smartTransparent ? 'bg-transparent' : 'bg-muted/50'
          )}
        >
          <div className="flex flex-col items-center gap-3">
            {/* Loading animation */}
            <div className="flex gap-2">
              <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce" />
              <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.1s]" />
              <div className="size-2 bg-muted-foreground/50 rounded-full animate-bounce [animation-delay:0.2s]" />
            </div>
            {/* Loading text */}
            <p className="text-sm text-muted-foreground">
              Loading market overview...
            </p>
          </div>

          {/* Skeleton structure for tabs */}
          <div className="w-full space-y-3 px-4">
            {/* Tab headers skeleton */}
            <div className="flex gap-2">
              {Array.from({ length: Math.min(tabs.length, 4) }).map((_, i) => (
                <div
                  key={i}
                  className="h-8 bg-muted-foreground/20 rounded animate-pulse"
                  style={{ width: `${Math.random() * 40 + 60}px` }}
                />
              ))}
            </div>

            {/* Content skeleton */}
            <div className="space-y-2">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="flex justify-between items-center">
                  <div className="h-4 bg-muted-foreground/20 rounded animate-pulse w-32" />
                  <div className="flex gap-2">
                    <div className="h-4 bg-muted-foreground/20 rounded animate-pulse w-16" />
                    <div className="h-4 bg-green-500/20 rounded animate-pulse w-12" />
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
}

export const MarketWatch = memo(MarketWatchComponent);
export default MarketWatch;
