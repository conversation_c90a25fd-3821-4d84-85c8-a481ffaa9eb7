export interface MarketWatchSymbol {
  s: string; // Symbol (e.g., "FOREXCOM:SPXUSD")
  d: string; // Display name (e.g., "S&P 500 Index")
  'base-currency-logoid'?: string; // Optional base currency logo ID (e.g., "crypto/XTVCBTC")
  'currency-logoid'?: string; // Optional currency logo ID (e.g., "country/US")
}

export interface MarketWatchTab {
  title: string;
  symbols: MarketWatchSymbol[];
  originalTitle: string;
}

export interface MarketWatchColorTheme {
  plotLineColorGrowing?: string;
  plotLineColorFalling?: string;
  gridLineColor?: string;
  scaleFontColor?: string;
  belowLineFillColorGrowing?: string;
  belowLineFillColorFalling?: string;
  belowLineFillColorGrowingBottom?: string;
  belowLineFillColorFallingBottom?: string;
  symbolActiveColor?: string;
}

export interface MarketWatchProps {
  tabs: MarketWatchTab[];
  className?: string;
  colorTheme?: 'light' | 'dark';
  dateRange?: '1D' | '1W' | '1M' | '3M' | '6M' | '12M' | 'ALL';
  locale?: string;
  largeChartUrl?: string;
  isTransparent?: boolean;
  showFloatingTooltip?: boolean;
  showSymbolLogo?: boolean;
  showChart?: boolean;
  width?: string | number;
  height?: string | number;
  supportHost?: string;
  colors?: MarketWatchColorTheme;
  // Additional TradingView widget properties
  timezone?: string;
  autosize?: boolean;
  container_id?: string;
}

export interface MarketWatchConfig {
  tabs: MarketWatchTab[];
  colorTheme: 'light' | 'dark';
  dateRange: string;
  locale: string;
  largeChartUrl: string;
  isTransparent: boolean;
  showFloatingTooltip: boolean;
  showSymbolLogo: boolean;
  showChart: boolean;
  width: string | number;
  height: string | number;
  supportHost: string;
  plotLineColorGrowing: string;
  plotLineColorFalling: string;
  gridLineColor: string;
  scaleFontColor: string;
  belowLineFillColorGrowing: string;
  belowLineFillColorFalling: string;
  belowLineFillColorGrowingBottom: string;
  belowLineFillColorFallingBottom: string;
  symbolActiveColor: string;
  // Additional properties
  timezone?: string;
  autosize?: boolean;
  container_id?: string;
}

export interface UseMarketWatchReturn {
  containerRef: React.RefObject<HTMLDivElement | null>;
  isLoading: boolean;
  error: string | null;
}

export interface TabSet {
  name: string;
  tabs: MarketWatchTab[];
}

export interface TabSets {
  [key: string]: TabSet;
}
