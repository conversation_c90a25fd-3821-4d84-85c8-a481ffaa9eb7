import type {
  MarketWatchConfig,
  MarketWatchColorTheme,
} from './MarketWatch.types';

// Default color themes for light and dark modes
export const DEFAULT_LIGHT_COLORS: MarketWatchColorTheme = {
  plotLineColorGrowing: '#686D3A', // Your custom muted green for gains
  plotLineColorFalling: '#89484B', // Your custom muted red for losses
  gridLineColor: '#e5e5e5', // Your custom grid color
  scaleFontColor: '#2e282a', // Your custom text color
  belowLineFillColorGrowing: 'rgba(104, 109, 58, 0.1)', // Light fill based on your green
  belowLineFillColorFalling: 'rgba(137, 72, 75, 0.1)', // Light fill based on your red
  belowLineFillColorGrowingBottom: 'rgba(104, 109, 58, 0.05)', // Bottom gradient based on your green
  belowLineFillColorFallingBottom: 'rgba(137, 72, 75, 0.05)', // Bottom gradient based on your red
  symbolActiveColor: '#fbf9fa', // blue-500 for active symbols in light mode
};

export const DEFAULT_DARK_COLORS: MarketWatchColorTheme = {
  plotLineColorGrowing: '#686D3A', // Your custom muted green for gains
  plotLineColorFalling: '#89484B', // Your custom muted red for losses
  gridLineColor: '##4A4A4A', // Your custom grid color
  scaleFontColor: '#2e282a', // Your custom text color
  belowLineFillColorGrowing: 'rgba(104, 109, 58, 0.1)', // Light fill based on your green
  belowLineFillColorFalling: 'rgba(137, 72, 75, 0.1)', // Light fill based on your red
  belowLineFillColorGrowingBottom: 'rgba(104, 109, 58, 0.05)', // Bottom gradient based on your green
  belowLineFillColorFallingBottom: 'rgba(137, 72, 75, 0.05)', // Bottom gradient based on your red
  symbolActiveColor: '#2E2E2E', // blue-500 for active symbols in light mode
};

export const DEFAULT_CONFIG: Partial<MarketWatchConfig> = {
  dateRange: '12M',
  locale: 'en',
  largeChartUrl: '',
  isTransparent: true,
  showFloatingTooltip: true,
  showSymbolLogo: true,
  showChart: true,
  width: '100%',
  height: '100%',
  supportHost: 'https://www.tradingview.com',
};

/**
 * Get theme-appropriate colors for the market watch widget
 */
export function getMarketWatchColors(
  theme: 'light' | 'dark',
  customColors?: MarketWatchColorTheme
): MarketWatchColorTheme {
  const defaultColors =
    theme === 'dark' ? DEFAULT_DARK_COLORS : DEFAULT_LIGHT_COLORS;

  return {
    ...defaultColors,
    ...customColors,
  };
}

/**
 * Build complete configuration for TradingView Market Watch widget
 */
export function buildMarketWatchConfig(
  config: Partial<MarketWatchConfig>,
  theme: 'light' | 'dark',
  customColors?: MarketWatchColorTheme
): MarketWatchConfig {
  const colors = getMarketWatchColors(theme, customColors);

  return {
    ...DEFAULT_CONFIG,
    ...config,
    colorTheme: theme,
    ...colors,
  } as MarketWatchConfig;
}
