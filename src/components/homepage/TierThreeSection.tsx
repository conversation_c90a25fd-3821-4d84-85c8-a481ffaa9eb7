import { getCachedTier3Articles } from '@/lib/cache/articles';
import TierSection from './TierSection';
import { DEFAULT_HOMEPAGE_CONFIG } from './types';

export default async function TierThreeSection() {
  const config = DEFAULT_HOMEPAGE_CONFIG;

  try {
    const tier3Data = await getCachedTier3Articles();

    return (
      <TierSection
        tier="tier-3"
        articles={tier3Data.docs}
        title="Aktuelle Meldungen"
        variant="title-only"
        locale={config.locale}
        maxArticles={undefined} // No limit - show all tier 3 articles
        emptyStateMessage="Derzeit keine aktuellen Meldungen verfügbar"
        showMoreButton={false} // Disabled since we show all articles
        showDescriptionForFirst={999}
        className="space-y-2 sm:space-y-2.5"
      />
    );
  } catch (error) {
    console.error('Error fetching tier 3 articles:', error);
    // Graceful fallback - render empty state
    return (
      <TierSection
        tier="tier-3"
        articles={[]}
        title="Aktuelle Meldungen"
        variant="title-only"
        locale={config.locale}
        maxArticles={undefined} // No limit - show all tier 3 articles
        emptyStateMessage="Fehler beim Laden der aktuellen Meldungen"
        showMoreButton={false} // Disabled since we show all articles
        showDescriptionForFirst={999}
        className="space-y-2 sm:space-y-2.5"
      />
    );
  }
}
