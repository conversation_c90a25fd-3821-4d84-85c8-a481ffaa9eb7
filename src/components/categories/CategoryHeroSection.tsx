import NewsCard from '@/components/NewsCard';
import type { Article, Category } from '@/payload-types';

interface CategoryHeroSectionProps {
  article: Article;
  categoryInfo: Category;
}

export default function CategoryHeroSection({
  article,
  categoryInfo,
}: CategoryHeroSectionProps) {
  return (
    <section className="mb-6 md:mb-8">
      <NewsCard
        article={article}
        variant="default"
        showDescription={true}
        priority={true}
        locale="de"
        className="shadow-sm border border-border"
      />
    </section>
  );
}
