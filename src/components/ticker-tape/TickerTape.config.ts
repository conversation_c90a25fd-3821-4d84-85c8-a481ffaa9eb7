import type { SymbolSets, TickerSymbol } from './TickerTape.types';
import type {
  TabSets,
  MarketWatchTab,
  MarketWatchSymbol,
} from '../market-watch/MarketWatch.types';

// Investment symbols from client example
const INVESTMENT_SYMBOLS: TickerSymbol[] = [
  { proName: 'FWB:SAP', title: '' },
  { proName: 'FWB:SIE', title: '' },
  { proName: 'FWB:ALV', title: '' },
  { proName: 'FWB:BMW', title: '' },
  { proName: 'FWB:DTE', title: '' },
  { proName: 'FWB:VOW', title: '' },
  { proName: 'FWB:MBG', title: '' },
  { proName: 'FWB:BAS', title: '' },
  { proName: 'FWB:MUV2', title: '' },
  { proName: 'FWB:DPW', title: '' },
  { proName: 'FWB:DBK', title: '' },
  { proName: 'FWB:CBK', title: '' },
  { proName: 'FWB:FRE', title: '' },
  { proName: 'FWB:IFX', title: '' },
  { proName: 'FWB:ADS', title: '' },
  { proName: 'FWB:RWE', title: '' },
  { proName: 'FWB:EOAN', title: '' },
  { proName: 'FWB:BAYN', title: '' },
  { proName: 'FWB:HEN3', title: '' },
  { proName: 'FWB:CON', title: '' },
  { proName: 'FWB:LIN', title: '' },
  { proName: 'FWB:P911', title: '' },
  { proName: 'FWB:QIA', title: '' },
  { proName: 'FWB:SY1', title: '' },
  { proName: 'FWB:ZAL', title: '' },
  { proName: 'FWB:HNR1', title: '' },
  { proName: 'FWB:BEI', title: '' },
  { proName: 'FWB:MTX', title: '' },
  { proName: 'FWB:DB1', title: '' },
];

// Global market indices and cryptocurrencies for homepage
const HOMEPAGE_SYMBOLS: TickerSymbol[] = [
  { proName: 'FX_IDC:EURUSD', title: '' },
  { proName: 'BITSTAMP:BTCUSD', title: '' },
  { proName: 'BITSTAMP:ETHUSD', title: '' },
  { proName: 'IG:NASDAQ', title: '' },
  { proName: 'INDEX:N100', title: '' },
  { proName: 'VANTAGE:NIKKEI225', title: '' },
  { proName: 'HSI:HSI', title: '' },
  { proName: 'GOMARKETS:FTSE100', title: '' },
  { proName: 'TSX:TSX', title: '' },
  { proName: 'MARKETSCOM:SPAIN35', title: '' },
  { proName: 'XETR:DAX', title: '' },
  { proName: 'INDEX:CAC40', title: '' },
  { proName: 'INDEX:FTSEMIB', title: '' },
  { proName: 'SIX:SMI', title: '' },
];

// Technology focused symbols - crypto, tech stocks, and German tech
const TECHNOLOGY_SYMBOLS: TickerSymbol[] = [
  { proName: 'BINANCE:ETHUSDT', title: '' },
  { proName: 'COINBASE:BTCUSD', title: '' },
  { proName: 'BINANCE:LTCUSDT', title: '' },
  { proName: 'BINANCE:SAHARAUSDT', title: '' },
  { proName: 'BINANCE:DOGEUSDT', title: '' },
  { proName: 'COINBASE:PAXUSD', title: '' },
  { proName: 'KUCOIN:HYPEUSDT', title: '' },
  { proName: 'NASDAQ:TSLA', title: '' },
  { proName: 'NASDAQ:NVDA', title: '' },
  { proName: 'NASDAQ:AAPL', title: '' },
  { proName: 'NASDAQ:AMD', title: '' },
  { proName: 'NASDAQ:AMZN', title: '' },
  { proName: 'NASDAQ:PLTR', title: '' },
  { proName: 'NASDAQ:GOOGL', title: '' },
  { proName: 'NASDAQ:META', title: '' },
  { proName: 'NYSE:SAP', title: '' },
  { proName: 'NSE:SIEMENS', title: '' },
  { proName: 'BSE:BOSCHLTD', title: '' },
  { proName: 'GETTEX:DTE', title: '' },
  { proName: 'BET:INFINEON', title: '' },
  { proName: 'XETR:ZAL', title: '' },
  { proName: 'BINANCE:XRPUSDT', title: '' },
];

// International/Forex symbols for international category
const INTERNATIONAL_SYMBOLS: TickerSymbol[] = [
  { proName: 'CMCMARKETS:EURUSD', title: '' },
  { proName: 'FX:GBPUSD', title: '' },
  { proName: 'OANDA:USDJPY', title: '' },
  { proName: 'SAXO:CADEUR', title: '' },
  { proName: 'FX_IDC:CADUSD', title: '' },
  { proName: 'CMCMARKETS:EURGBP', title: '' },
  { proName: 'CMCMARKETS:EURJPY', title: '' },
  { proName: 'OANDA:EURCHF', title: '' },
];

// Economics/Commodities symbols for wirtschaft (economy) category
const ECONOMICS_SYMBOLS: TickerSymbol[] = [
  { proName: 'TVC:GOLD', title: '' },
  { proName: 'MARKETSCOM:OIL', title: '' },
  { proName: 'TVC:SILVER', title: '' },
  { proName: 'CAPITALCOM:COPPER', title: '' },
  { proName: 'PEPPERSTONE:ZINC', title: '' },
  { proName: 'PEPPERSTONE:ALUMINIUM', title: '' },
  { proName: 'CAPITALCOM:NATURALGAS', title: '' },
  { proName: 'FOREXCOM:COTTON', title: '' },
  { proName: 'FOREXCOM:COFFEE', title: '' },
  { proName: 'PEPPERSTONE:SUGAR', title: '' },
  { proName: 'CAPITALCOM:PLATINUM', title: '' },
];

export const SYMBOL_SETS: SymbolSets = {
  homepage: {
    name: 'Homepage Mixed',
    symbols: HOMEPAGE_SYMBOLS,
  },
  investment: {
    name: 'Investment Focus',
    symbols: INVESTMENT_SYMBOLS,
  },
  technology: {
    name: 'Technology Sector',
    symbols: TECHNOLOGY_SYMBOLS,
  },
  international: {
    name: 'International Markets',
    symbols: INTERNATIONAL_SYMBOLS,
  },
  wirtschaft: {
    name: 'Economics & Commodities',
    symbols: ECONOMICS_SYMBOLS,
  },
} as const;

export const DEFAULT_CONFIG = {
  locale: 'en',
  largeChartUrl: '',
  isTransparent: true, // Transparent by default, smart logic handles dark mode
  showSymbolLogo: false, // Hide symbol logos for cleaner appearance
  displayMode: 'compact' as const, // Force compact mode for smaller text/elements
};

/**
 * Get symbol set for a specific category
 */
export function getSymbolSetForCategory(categorySlug: string): TickerSymbol[] {
  // Map category slugs to symbol sets
  const categoryMapping: Record<string, keyof typeof SYMBOL_SETS> = {
    investment: 'investment',
    technology: 'technology',
    technologie: 'technology', // German slug for technology category
    tech: 'technology',
    international: 'international',
    wirtschaft: 'wirtschaft',
    economics: 'wirtschaft', // Alternative mapping
    // Add more mappings as needed
  };

  const symbolSetKey = categoryMapping[categorySlug] || 'homepage';
  return SYMBOL_SETS[symbolSetKey].symbols;
}

/**
 * Validate symbol format
 */
export function validateSymbol(symbol: TickerSymbol): boolean {
  return (
    typeof symbol.proName === 'string' &&
    symbol.proName.length > 0 &&
    symbol.proName.includes(':')
  );
}

/**
 * Validate array of symbols
 */
export function validateSymbols(symbols: TickerSymbol[]): boolean {
  return (
    Array.isArray(symbols) &&
    symbols.length > 0 &&
    symbols.every(validateSymbol)
  );
}

// Market Watch Tab Sets (Client Data)
const HOMEPAGE_MARKET_WATCH_TABS: MarketWatchTab[] = [
  {
    title: 'Indizes',
    originalTitle: 'Indizes',
    symbols: [
      { s: 'FOREXCOM:SPXUSD', d: 'S&P 500 Index' },
      { s: 'FOREXCOM:NSXUSD', d: 'US 100 Cash CFD' },
      { s: 'FOREXCOM:DJI', d: 'Dow Jones Industrial Average Index' },
      { s: 'INDEX:NKY', d: 'Japan 225' },
      { s: 'INDEX:DEU40', d: 'DAX Index' },
      { s: 'FOREXCOM:UKXGBP', d: 'FTSE 100 Index' },
    ],
  },
  {
    title: 'Futures',
    originalTitle: 'Futures',
    symbols: [
      { s: 'BMFBOVESPA:ISP1!', d: 'S&P 500' },
      { s: 'BMFBOVESPA:EUR1!', d: 'Euro' },
      { s: 'CMCMARKETS:GOLD', d: 'Gold' },
      { s: 'PYTH:WTI3!', d: 'WTI Crude Oil' },
      { s: 'BMFBOVESPA:CCM1!', d: 'Corn' },
    ],
  },
  {
    title: 'Forex',
    originalTitle: 'Forex',
    symbols: [
      { s: 'FX:EURUSD', d: 'EUR to USD' },
      { s: 'FX:GBPUSD', d: 'GBP to USD' },
      { s: 'FX:USDJPY', d: 'USD to JPY' },
      { s: 'FX:USDCHF', d: 'USD to CHF' },
      { s: 'FX:AUDUSD', d: 'AUD to USD' },
      { s: 'FX:USDCAD', d: 'USD to CAD' },
    ],
  },
  {
    title: 'Krypto',
    originalTitle: 'Krypto',
    symbols: [
      {
        s: 'OKX:BTCUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCBTC',
        'currency-logoid': 'country/US',
      },
      {
        s: 'BITSTAMP:ETHUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCETH',
        'currency-logoid': 'country/US',
      },
      {
        s: 'COINBASE:XRPUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCXRP',
        'currency-logoid': 'country/US',
      },
      {
        s: 'COINBASE:SOLUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCSOL',
        'currency-logoid': 'country/US',
      },
      {
        s: 'BINANCE:BNBUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCBNB',
        'currency-logoid': 'country/US',
      },
      {
        s: 'COINBASE:DOGEUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCDOGE',
        'currency-logoid': 'country/US',
      },
      {
        s: 'COINBASE:ADAUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCADA',
        'currency-logoid': 'country/US',
      },
      {
        s: 'COINBASE:DOTUSD',
        d: '',
        'base-currency-logoid': 'crypto/XTVCDOT',
        'currency-logoid': 'country/US',
      },
      {
        s: 'CMCMARKETS:EURJPY',
        d: '',
        'base-currency-logoid': 'country/EU',
        'currency-logoid': 'country/JP',
      },
    ],
  },
];

const INVESTMENT_MARKET_WATCH_TABS: MarketWatchTab[] = [
  {
    title: 'German Stocks',
    originalTitle: 'German Stocks',
    symbols: [
      { s: 'FWB:SAP', d: 'SAP' },
      { s: 'FWB:SIE', d: 'Siemens' },
      { s: 'FWB:ALV', d: 'Allianz' },
      { s: 'FWB:BMW', d: 'BMW' },
      { s: 'FWB:DTE', d: 'Deutsche Telekom' },
      { s: 'FWB:VOW', d: 'Volkswagen' },
    ],
  },
  {
    title: 'Indices',
    originalTitle: 'Indices',
    symbols: [
      { s: 'INDEX:DEU40', d: 'DAX Index' },
      { s: 'INDEX:DEU30', d: 'DAX 30' },
      { s: 'FOREXCOM:SPXUSD', d: 'S&P 500' },
      { s: 'INDEX:NKY', d: 'Nikkei 225' },
    ],
  },
];

const TECHNOLOGY_MARKET_WATCH_TABS: MarketWatchTab[] = [
  {
    title: 'Tech Stocks',
    originalTitle: 'Tech Stocks',
    symbols: [
      { s: 'NASDAQ:AAPL', d: 'Apple Inc.' },
      { s: 'NASDAQ:NVDA', d: 'NVIDIA Corporation' },
      { s: 'NASDAQ:TSLA', d: 'Tesla, Inc.' },
      { s: 'NASDAQ:AMD', d: 'Advanced Micro Devices' },
      { s: 'NASDAQ:GOOGL', d: 'Alphabet Inc.' },
      { s: 'NASDAQ:META', d: 'Meta Platforms' },
    ],
  },
  {
    title: 'Crypto',
    originalTitle: 'Crypto',
    symbols: [
      { s: 'BINANCE:BTCUSDT', d: 'Bitcoin' },
      { s: 'BINANCE:ETHUSDT', d: 'Ethereum' },
      { s: 'BINANCE:ADAUSDT', d: 'Cardano' },
      { s: 'BINANCE:SOLUSDT', d: 'Solana' },
    ],
  },
];

export const MARKET_WATCH_TAB_SETS: TabSets = {
  homepage: {
    name: 'Homepage Market Overview',
    tabs: HOMEPAGE_MARKET_WATCH_TABS,
  },
  investment: {
    name: 'Investment Focus',
    tabs: INVESTMENT_MARKET_WATCH_TABS,
  },
  technology: {
    name: 'Technology Sector',
    tabs: TECHNOLOGY_MARKET_WATCH_TABS,
  },
} as const;

/**
 * Get market watch tab set for a specific category
 */
export function getMarketWatchTabSetForCategory(
  categorySlug: string
): MarketWatchTab[] {
  const categoryMapping: Record<string, keyof typeof MARKET_WATCH_TAB_SETS> = {
    investment: 'investment',
    technology: 'technology',
    technologie: 'technology',
    tech: 'technology',
  };

  const tabSetKey = categoryMapping[categorySlug] || 'homepage';
  return MARKET_WATCH_TAB_SETS[tabSetKey].tabs;
}

/**
 * Validate market watch symbol format
 */
export function validateMarketWatchSymbol(symbol: MarketWatchSymbol): boolean {
  return (
    typeof symbol.s === 'string' &&
    symbol.s.length > 0 &&
    typeof symbol.d === 'string' &&
    // Allow empty display name for crypto symbols that have base-currency-logoid
    (!!symbol['base-currency-logoid'] || symbol.d.length > 0)
  );
}

/**
 * Validate market watch tab
 */
export function validateMarketWatchTab(tab: MarketWatchTab): boolean {
  return (
    typeof tab.title === 'string' &&
    tab.title.length > 0 &&
    Array.isArray(tab.symbols) &&
    tab.symbols.length > 0 &&
    tab.symbols.every(validateMarketWatchSymbol)
  );
}

/**
 * Validate array of market watch tabs
 */
export function validateMarketWatchTabs(tabs: MarketWatchTab[]): boolean {
  return (
    Array.isArray(tabs) && tabs.length > 0 && tabs.every(validateMarketWatchTab)
  );
}
