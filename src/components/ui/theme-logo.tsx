'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import { useTheme } from 'next-themes';

interface ThemeLogoProps {
  /** Alt text for the logo */
  alt?: string;
  /** CSS classes to apply to the image */
  className?: string;
  /** Width of the logo */
  width?: number;
  /** Height of the logo */
  height?: number;
  /** Light theme logo path */
  lightLogo?: string;
  /** Dark theme logo path */
  darkLogo?: string;
}

/**
 * Hook for theme-aware logo selection with hydration safety
 */
function useThemeLogo() {
  const { theme, systemTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  return useMemo(() => {
    // Return light theme until mounted to prevent hydration mismatch
    if (!mounted) {
      return 'light';
    }

    // Use resolvedTheme first as it's the most reliable
    if (resolvedTheme) {
      return resolvedTheme === 'dark' ? 'dark' : 'light';
    }

    // Fallback to manual resolution
    const effectiveTheme = theme === 'system' ? systemTheme : theme;
    return effectiveTheme === 'dark' ? 'dark' : 'light';
  }, [mounted, theme, systemTheme, resolvedTheme]);
}

/**
 * Theme-responsive logo component that switches between light and dark variants
 */
export function ThemeLogo({
  alt = 'Logo',
  className = 'h-4 w-auto',
  width = 76,
  height = 11,
  lightLogo = '/brand/borsenblick-logo.svg',
  darkLogo = '/brand/borsenblick-logo-dark-mode.svg',
}: ThemeLogoProps) {
  const detectedTheme = useThemeLogo();

  // Select the appropriate logo based on theme
  const logoSrc = detectedTheme === 'dark' ? darkLogo : lightLogo;

  return (
    <Image
      src={logoSrc}
      alt={alt}
      width={width}
      height={height}
      className={className}
    />
  );
}
