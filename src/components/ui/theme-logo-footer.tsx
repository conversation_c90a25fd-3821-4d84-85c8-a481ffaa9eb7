'use client';

import React, { useState, useEffect, useMemo } from 'react';
import Image from 'next/image';
import { useTheme } from 'next-themes';

interface ThemeLogoFooterProps {
  /** Alt text for the logo */
  alt?: string;
  /** CSS classes to apply to the image container */
  className?: string;
  /** Light theme logo path */
  lightLogo?: string;
  /** Dark theme logo path */
  darkLogo?: string;
}

/**
 * Hook for theme-aware logo selection with hydration safety
 */
function useThemeLogo() {
  const { theme, systemTheme, resolvedTheme } = useTheme();
  const [mounted, setMounted] = useState(false);

  // Handle hydration
  useEffect(() => {
    setMounted(true);
  }, []);

  return useMemo(() => {
    // Return light theme until mounted to prevent hydration mismatch
    if (!mounted) {
      return 'light';
    }

    // Use resolvedTheme first as it's the most reliable
    if (resolvedTheme) {
      return resolvedTheme === 'dark' ? 'dark' : 'light';
    }

    // Fallback to manual resolution
    const effectiveTheme = theme === 'system' ? systemTheme : theme;
    return effectiveTheme === 'dark' ? 'dark' : 'light';
  }, [mounted, theme, systemTheme, resolvedTheme]);
}

/**
 * Theme-responsive footer logo component with footer-specific styling
 */
export function ThemeLogoFooter({
  alt = 'Logo',
  className = 'relative h-12 w-auto min-w-[120px]',
  lightLogo = '/brand/borsenblick-logo.svg',
  darkLogo = '/brand/borsenblick-logo-dark-mode.svg',
}: ThemeLogoFooterProps) {
  const detectedTheme = useThemeLogo();

  // Select the appropriate logo based on theme
  const logoSrc = detectedTheme === 'dark' ? darkLogo : lightLogo;

  return (
    <div className={className}>
      <Image src={logoSrc} alt={alt} fill className="object-contain" />
    </div>
  );
}
