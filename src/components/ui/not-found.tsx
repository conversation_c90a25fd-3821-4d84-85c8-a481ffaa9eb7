import React from 'react';
import Link from 'next/link';
import { TriangleAlert, Home, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card';

interface NotFoundProps {
  /** Custom title for the 404 page */
  title?: string;
  /** Custom description for the 404 page */
  description?: string;
  /** Custom reasons for the error */
  reasons?: string[];
  /** Whether to show category suggestions */
  showCategories?: boolean;
  /** Custom categories to display */
  categories?: Array<{ id: string; slug: string; title: string }>;
  /** Custom action buttons */
  actions?: Array<{
    href: string;
    label: string;
    variant?: 'default' | 'outline';
    icon?: React.ReactNode;
  }>;
  /** Whether this is a full page (with html/body wrapper) */
  fullPage?: boolean;
}

export function NotFoundComponent({
  title = '404 - Seite nicht gefunden',
  description = 'Die angeforderte Seite existiert nicht oder wurde entfernt.',
  reasons = [
    'Die URL wurde falsch eingegeben',
    'Die Seite wurde verschoben oder gelöscht',
    'Der Link ist veraltet',
  ],
  showCategories = true,
  categories = [],
  actions = [
    {
      href: '/',
      label: 'Zur Startseite',
      variant: 'default' as const,
      icon: <Home className="size-4" />,
    },
    {
      href: '/kategorien/economics',
      label: 'Zu den Nachrichten',
      variant: 'outline' as const,
      icon: <ArrowLeft className="size-4" />,
    },
  ],
  fullPage = false,
}: NotFoundProps) {
  const defaultCategories = [
    { id: '1', slug: 'economics', title: 'Economics' },
    { id: '2', slug: 'technology', title: 'Technology' },
    { id: '3', slug: 'international', title: 'International' },
    { id: '4', slug: 'investment', title: 'Investment' },
  ];

  const displayCategories =
    categories.length > 0 ? categories.slice(0, 4) : defaultCategories;

  const content = (
    <div className="h-full bg-background flex items-center justify-center p-4">
      <Card className="max-w-lg w-full">
        <CardHeader className="text-center">
          <div className="flex justify-center mb-4">
            <TriangleAlert className="size-12 text-lion-600" />
          </div>
          <CardTitle className="text-xl font-serif font-normal text-foreground">
            {title}
          </CardTitle>
          <CardDescription className="text-muted-foreground">
            {description}
          </CardDescription>
        </CardHeader>

        <CardContent className="flex flex-col gap-4">
          {/* Possible reasons */}
          <div className="text-center text-sm text-muted-foreground">
            <p>Mögliche Gründe:</p>
            <ul className="mt-2 text-xs space-y-1">
              {reasons.map((reason, index) => (
                <li key={index}>• {reason}</li>
              ))}
            </ul>
          </div>

          {/* Action buttons */}
          <div className="flex flex-col sm:flex-row gap-2">
            {actions.map((action, index) => (
              <Button
                key={index}
                asChild
                className="flex-1 flex items-center gap-2"
                variant={action.variant || 'default'}
              >
                <Link href={action.href}>
                  {action.icon}
                  {action.label}
                </Link>
              </Button>
            ))}
          </div>

          {/* Category suggestions */}
          {showCategories && displayCategories.length > 0 && (
            <div className="text-center text-sm text-muted-foreground border-t pt-4">
              <p className="mb-2">Beliebte Bereiche:</p>
              <div className="flex flex-wrap gap-2 justify-center">
                {displayCategories.map(category => (
                  <Link
                    key={category.id}
                    href={`/kategorien/${category.slug}`}
                    className="text-xs px-2 py-1 bg-gray-100 dark:bg-gray-800 rounded-sm hover:bg-gray-200 dark:hover:bg-gray-700 transition-colors"
                  >
                    {category.title}
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Additional help */}
          <div className="text-center text-xs text-muted-foreground">
            <p>Schauen Sie später noch einmal vorbei</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );

  if (fullPage) {
    return (
      <html lang="de" className="min-h-dvh" suppressHydrationWarning>
        <head>
          <meta name="robots" content="noindex" />
          <title>{title} | BörsenBlick</title>
        </head>
        <body className="min-h-dvh">{content}</body>
      </html>
    );
  }

  return content;
}

export default NotFoundComponent;
