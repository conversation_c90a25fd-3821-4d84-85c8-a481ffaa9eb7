/**
 * Admin Components Index
 *
 * This file exports all custom admin components that integrate shadcn/ui
 * with PayloadCMS admin interface.
 *
 * All components are designed to:
 * - Work safely within Payload's admin environment
 * - Use scoped CSS variables to avoid conflicts
 * - Respect Payload's dark mode theming
 * - Provide consistent styling across the admin interface
 */

// Custom UI Components
export { CustomAdminButton } from './CustomButton';
export { CustomAdminCard } from './CustomCard';

// Shared Components
export { ViewLiveButton } from './shared/ViewLiveButton';
export { ViewLiveMenuItem } from './shared/ViewLiveMenuItem';

// Test Components
export { ShadcnTestComponent } from './ShadcnTestComponent';
export { TailwindShadcnFieldComponent } from './TailwindShadcnFieldComponent';

// Action Components
export { ArticleDocumentControls } from './article-actions/DocumentControls';

// Toast System
export { ToastProvider } from './toast-provider';
export { ToastTestComponent } from './toast-test';

// Admin Bar Components
export { AdminBarProvider, useAdminBar } from './AdminBarContext';
export { AdminBarWrapper } from './AdminBarWrapper';
export { AdminBarClientWrapper } from './AdminBarClientWrapper';
export { AdminBarDataSetter } from './AdminBarDataSetter';

// Type exports for better TypeScript support
// export type { default as CustomAdminButtonProps } from './CustomButton'
// export type { default as CustomAdminCardProps } from './CustomCard'
