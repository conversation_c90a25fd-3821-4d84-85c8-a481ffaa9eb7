/**
 * Generic Translation Controls Component
 *
 * Provides translation functionality for any PayloadCMS collection
 * that supports bilingual English/German content. Extracted from
 * ArticleDocumentControls to enable reuse across collections.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-27
 */

'use client';

import React, { useState, useCallback, useMemo } from 'react';
import {
  useDocumentInfo,
  useAllFormFields,
  useFormFields,
} from '@payloadcms/ui';
import { useArticleNotifications } from '../notifications/ArticleNotifications';

// Standard API response format for translations
interface StandardAPIResponse {
  success: boolean;
  data?: any;
  message?: string;
  error?: string;
}

// Props interface for TranslationControls
interface TranslationControlsProps {
  collection: 'articles' | 'pages';
  apiEndpoint?: string;
  additionalValidation?: (context: any) => {
    isValid: boolean;
    errors: string[];
  };
  onTranslationComplete?: (result?: any) => void;
  customNotifications?: any;
}

export const TranslationControls: React.FC<TranslationControlsProps> = ({
  collection,
  apiEndpoint,
  additionalValidation,
  onTranslationComplete,
  customNotifications,
}) => {
  console.log('🌐 TranslationControls loading for collection:', collection);

  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();

  // Use provided notifications or default to ArticleNotifications
  const defaultNotifications = useArticleNotifications();
  const notifications = customNotifications || defaultNotifications;

  // Simple state management
  const [isTranslating, setIsTranslating] = useState(false);

  // Extract field values using useFormFields (collection-specific)
  const title =
    useFormFields(([fields]) => {
      if (collection === 'pages') {
        return fields['englishTab.title']?.value as string;
      }
      return fields.title?.value as string;
    }) || '';

  const content =
    useFormFields(([fields]) => {
      if (collection === 'pages') {
        return fields['englishTab.content']?.value;
      }
      return fields.content?.value;
    }) || '';

  const summary =
    useFormFields(([fields]) => fields.summary?.value as string) || '';
  const mainTitle =
    useFormFields(([fields]) => fields.title?.value as string) || '';
  const hasGermanTranslation =
    useFormFields(
      ([fields]) => fields.hasGermanTranslation?.value as boolean
    ) || false;
  const germanTitle =
    useFormFields(
      ([fields]) => fields['germanTab.germanTitle']?.value as string
    ) || '';
  const germanContent =
    useFormFields(([fields]) => fields['germanTab.germanContent']?.value) || '';

  // Simple validation with debugging
  const validateForTranslation = useCallback(() => {
    const errors: string[] = [];

    // Debug field values
    if (process.env.NODE_ENV === 'development') {
      console.log('🔍 VALIDATION DEBUG:', {
        collection,
        title: title || 'EMPTY',
        titleLength: title?.length || 0,
        hasContent: !!content,
        contentType: typeof content,
        hasGermanTranslation,
      });
    }

    // Basic validation - just check for presence
    if (!title) {
      errors.push('Title is required');
    }
    if (!content) {
      errors.push('Content is required');
    }

    // Additional validation if provided
    if (additionalValidation) {
      const additionalResult = additionalValidation({
        title,
        content,
        summary,
        mainTitle,
        hasGermanTranslation,
        germanTitle,
        germanContent,
      });
      if (!additionalResult.isValid) {
        errors.push(...additionalResult.errors);
      }
    }

    if (process.env.NODE_ENV === 'development' && errors.length > 0) {
      console.log('❌ VALIDATION ERRORS:', errors);
    }

    return { isValid: errors.length === 0, errors };
  }, [
    collection,
    title,
    content,
    summary,
    mainTitle,
    hasGermanTranslation,
    germanTitle,
    germanContent,
    additionalValidation,
  ]);

  // Update form fields after translation
  const updateFormAfterTranslation = useCallback(
    (result: StandardAPIResponse) => {
      if (!result.data || !dispatchFields) return;

      const updates = [
        { field: 'hasGermanTranslation', value: true },
        { field: 'germanTab.germanTitle', value: result.data.germanTitle },
        { field: 'germanTab.germanContent', value: result.data.germanContent },
      ];

      if (result.data.germanSummary) {
        updates.push({
          field: 'germanTab.germanSummary',
          value: result.data.germanSummary,
        });
      }

      // Apply updates with short delays to ensure proper form state
      updates.forEach((update, index) => {
        setTimeout(() => {
          dispatchFields({
            type: 'UPDATE',
            path: update.field,
            value: update.value,
          });
        }, index * 50);
      });
    },
    [dispatchFields]
  );

  // Simple translation handler
  const handleTranslation = useCallback(async () => {
    if (!id) {
      notifications.showValidationError('Document ID not found');
      return;
    }

    const validation = validateForTranslation();
    if (!validation.isValid) {
      notifications.showValidationError(validation.errors[0]);
      return;
    }

    setIsTranslating(true);
    const controller = new AbortController();

    try {
      const endpoint = apiEndpoint || `/api/${collection}/translate`;
      const response = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ documentId: id }),
        signal: controller.signal,
      });

      const result: StandardAPIResponse = await response.json();

      if (result.success && result.data) {
        notifications.showTranslationSuccess(hasGermanTranslation);
        updateFormAfterTranslation(result);

        if (onTranslationComplete) {
          onTranslationComplete(result);
        }

        // Refresh page to show German tab
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        console.error('Translation API Error:', result);
        notifications.showOperationError(
          'translation',
          result.error || 'Translation failed'
        );
      }
    } catch (error) {
      // Don't show error if request was aborted
      if (error instanceof Error && error.name === 'AbortError') {
        return;
      }
      console.error('Translation Network Error:', error);
      notifications.showOperationError(
        'translation',
        'Failed to communicate with the translation service'
      );
    } finally {
      setIsTranslating(false);
    }
  }, [
    id,
    validateForTranslation,
    collection,
    apiEndpoint,
    hasGermanTranslation,
    updateFormAfterTranslation,
    onTranslationComplete,
    notifications,
  ]);

  // Simple button state
  const canTranslate = useMemo(() => {
    if (!id || isTranslating) return false;
    const validation = validateForTranslation();
    return validation.isValid;
  }, [id, isTranslating, validateForTranslation]);

  return (
    <div className="translation-controls-container">
      <button
        type="button"
        onClick={handleTranslation}
        disabled={!canTranslate}
        className={`translation-button ${canTranslate ? 'primary' : 'disabled'} ${
          isTranslating ? 'processing' : ''
        } ${hasGermanTranslation ? 'retranslate' : ''}`}
      >
        {isTranslating ? (
          <span>
            <span className="spinner">⏳</span>
            <span className="button-text">Translating...</span>
          </span>
        ) : (
          <span className="button-text">
            {hasGermanTranslation
              ? 'Re-translate to German'
              : 'Translate to German'}
          </span>
        )}
      </button>

      {!canTranslate && !isTranslating && (
        <div className="status-indicator">
          {!id ? 'Save document first' : 'Check content requirements'}
        </div>
      )}

      <style jsx>{`
        .translation-controls-container {
          margin-bottom: 1.5rem;
        }

        .translation-button {
          width: 100%;
          min-height: 40px;
          min-width: 160px;
          padding: 0.75rem 1rem;
          border: 1px solid var(--theme-color-border, #d1d5db);
          border-radius: 0.375rem;
          font-size: 0.875rem;
          font-weight: 500;
          font-family: inherit;
          cursor: pointer;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 0.5rem;
          position: relative;
          outline: none;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .translation-button:focus {
          box-shadow:
            0 0 0 2px var(--theme-color-bg, #fff),
            0 0 0 4px var(--theme-color-primary, #3b82f6);
        }

        .translation-button.primary {
          background-color: var(--theme-color-primary, #1f2937);
          color: var(--theme-color-primary-text, #ffffff);
          border-color: var(--theme-color-primary-border, #374151);
        }

        .translation-button.primary:hover:not(:disabled) {
          background-color: var(--theme-color-primary-hover, #111827);
          border-color: var(--theme-color-primary-border-hover, #1f2937);
          transform: translateY(-1px);
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
        }

        .translation-button.retranslate {
          background-color: var(--theme-color-secondary, #374151);
          color: var(--theme-color-secondary-text, #ffffff);
          border-color: var(--theme-color-secondary-border, #4b5563);
        }

        .translation-button.retranslate:hover:not(:disabled) {
          background-color: var(--theme-color-secondary-hover, #1f2937);
          border-color: var(--theme-color-secondary-border-hover, #374151);
          transform: translateY(-1px);
          box-shadow: 0 2px 4px 0 rgba(0, 0, 0, 0.1);
        }

        .translation-button.processing {
          background-color: var(--theme-color-success, #059669);
          color: var(--theme-color-success-text, #ffffff);
          border-color: var(--theme-color-success-border, #047857);
          cursor: not-allowed;
        }

        .translation-button.disabled {
          background-color: var(--theme-color-text-disabled, #f3f4f6);
          color: var(--theme-color-text-muted, #9ca3af);
          border-color: var(--theme-color-border-disabled, #d1d5db);
          cursor: not-allowed;
          opacity: 0.6;
        }

        .translation-button.disabled:hover {
          transform: none;
          box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
        }

        .spinner {
          width: 14px;
          height: 14px;
          border: 2px solid rgba(255, 255, 255, 0.3);
          border-top: 2px solid currentColor;
          border-radius: 50%;
          animation: spin 1s linear infinite;
          flex-shrink: 0;
        }

        .button-text {
          line-height: 1;
          white-space: nowrap;
        }

        @keyframes spin {
          0% {
            transform: rotate(0deg);
          }
          100% {
            transform: rotate(360deg);
          }
        }

        .status-indicator {
          margin-top: 0.5rem;
          text-align: center;
          font-size: 0.75rem;
          color: var(--theme-color-text-muted, #6b7280);
        }

        /* Dark mode support */
        @media (prefers-color-scheme: dark) {
          .translation-button.primary {
            background-color: #1f2937;
            color: #ffffff;
            border-color: #374151;
          }

          .translation-button.disabled {
            background-color: #374151;
            color: #9ca3af;
            border-color: #4b5563;
          }
        }
      `}</style>
    </div>
  );
};
