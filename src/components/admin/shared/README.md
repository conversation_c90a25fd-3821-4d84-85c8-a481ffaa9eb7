# Shared Admin Components

This directory contains reusable admin components that can be used across different PayloadCMS collections.

## ViewLiveMenuItem

A dropdown menu item that integrates with PayloadCMS's native document controls dropdown (the menu next to the Publish button). Provides a "View Live" option that opens published content in a new tab. This is the recommended approach for "View Live" functionality as it integrates seamlessly with PayloadCMS's built-in UI patterns.

### Features

- **Native Integration**: Appears in the dropdown menu next to Publish/Save Draft buttons
- **Conditional Display**: Only shows for published content with slugs
- **Environment-aware**: Automatically detects and uses correct domains
- **Collection-agnostic**: Works with any collection (articles, pages, etc.)

### Usage

Add to your collection's admin configuration:

```typescript
admin: {
  // ... other admin config
  components: {
    edit: {
      editMenuItems: ['@/components/admin/shared/ViewLiveMenuItem#ViewLiveMenuItem'],
    },
  },
}
```

## ViewLiveButton (Legacy)

A sidebar button component for "View Live" functionality. **Note: ViewLiveMenuItem is now preferred** as it integrates better with PayloadCMS's native UI patterns.

### Features

- **Environment-aware**: Automatically detects development, preview, and production environments
- **Collection-specific**: Supports different URL patterns for articles, pages, and custom collections
- **Conditional rendering**: Only shows for published content with slugs
- **Clean integration**: Uses PayloadCMS design patterns and hooks
- **Customisable**: Supports custom domains and URL patterns

### Usage

Add as a UI field in your collection configuration:

```typescript
{
  type: 'ui',
  name: 'viewLive',
  admin: {
    position: 'sidebar',
    components: {
      Field: '@/components/admin/shared/ViewLiveButton#ViewLiveButton',
      clientProps: {
        collection: 'articles', // or 'pages'
        pathPattern: '/custom/[slug]', // optional custom URL pattern
        domain: 'https://custom-domain.com', // optional custom domain
      },
    },
  },
}
```

### URL Patterns

By default, the component uses these URL patterns:

- **Articles**: `/{domain}/artikel/{slug}`
- **Pages**: `/{domain}/{slug}`
- **Custom**: `/{domain}/{collection}/{slug}`

### Environment Detection

The component automatically detects the environment:

- **Development**: `http://localhost:3000` (or `NEXT_PUBLIC_DEV_DOMAIN`)
- **Preview**: Uses current Vercel preview URL
- **Production**: `https://boersenblick.com` (or `NEXT_PUBLIC_PRODUCTION_DOMAIN`)

### Implementation Details

- Uses `useDocumentInfo()` to get document context
- Uses `useFormFields()` to subscribe to `_status` and `slug` fields
- Only renders when `_status === 'published'` and slug exists
- Opens links in new tab with `noopener,noreferrer` for security
