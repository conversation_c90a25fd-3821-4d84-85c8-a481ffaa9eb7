'use client';

import React from 'react';
import { useDocumentInfo, useFormFields } from '@payloadcms/ui';
import { ExternalLink } from 'lucide-react';
import {
  generateLiveUrl,
  getEnvironmentConfig,
} from '@/lib/config/environment';

interface ViewLiveButtonProps {
  /** Collection slug for URL generation */
  collection: 'articles' | 'pages';
  /** Custom URL path pattern (optional) */
  pathPattern?: string;
  /** Custom domain (optional, defaults to environment detection) */
  domain?: string;
}

/**
 * ViewLive Button Component
 *
 * Provides a "View Live" button that opens the published content in a new tab.
 * Only shows when content is published and has a slug.
 *
 * Features:
 * - Environment-aware domain detection
 * - Collection-specific URL patterns
 * - Clean integration with PayloadCMS design system
 * - Conditional rendering based on publication status
 *
 * Usage in collection fields:
 * ```typescript
 * {
 *   type: 'ui',
 *   name: 'viewLive',
 *   admin: {
 *     position: 'sidebar',
 *     components: {
 *       Field: '@/components/admin/shared/ViewLiveButton#ViewLiveButton',
 *       clientProps: {
 *         collection: 'articles'
 *       }
 *     },
 *   },
 * }
 * ```
 */
export const ViewLiveButton: React.FC<ViewLiveButtonProps> = ({
  collection,
  pathPattern,
  domain,
}) => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;

  // Get publication status and slug from form fields
  const status = useFormFields(
    ([fields]) => fields['_status']?.value as string
  );
  const slug = useFormFields(([fields]) => fields.slug?.value as string);

  // Only show for published content with a slug
  const isPublished = status === 'published';
  const hasSlug = Boolean(slug);
  const shouldShow = isPublished && hasSlug && id;

  if (!shouldShow) {
    return null;
  }

  // Generate the live URL using environment configuration
  const getLiveUrl = (): string => {
    if (domain) {
      // Use custom domain if provided
      const baseDomain = domain;
      if (pathPattern) {
        return `${baseDomain}${pathPattern.replace('[slug]', slug)}`;
      }

      switch (collection) {
        case 'articles':
          return `${baseDomain}/artikel/${slug}`;
        case 'pages':
          return `${baseDomain}/${slug}`;
        default:
          return `${baseDomain}/${collection}/${slug}`;
      }
    }

    // Use environment-aware URL generation
    return generateLiveUrl(collection, slug, pathPattern);
  };

  const handleViewLive = () => {
    const url = getLiveUrl();
    window.open(url, '_blank', 'noopener,noreferrer');
  };

  return (
    <div
      style={{
        marginBottom: '16px',
        padding: '12px',
        backgroundColor: '#f0f9ff',
        borderRadius: '6px',
        border: '1px solid #0ea5e9',
      }}
    >
      <button
        onClick={handleViewLive}
        style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          width: '100%',
          padding: '10px 16px',
          backgroundColor: '#0ea5e9',
          color: 'white',
          border: 'none',
          borderRadius: '4px',
          cursor: 'pointer',
          fontSize: '14px',
          fontWeight: '500',
          transition: 'all 0.2s ease',
          boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
        }}
        onMouseEnter={e => {
          e.currentTarget.style.backgroundColor = '#0284c7';
        }}
        onMouseLeave={e => {
          e.currentTarget.style.backgroundColor = '#0ea5e9';
        }}
        title={`Open published ${collection.slice(0, -1)} in new tab`}
      >
        <ExternalLink size={16} />
        <span>View Live</span>
      </button>

      {/* Show the URL for reference */}
      <div
        style={{
          marginTop: '8px',
          fontSize: '12px',
          color: '#64748b',
          wordBreak: 'break-all',
        }}
      >
        {getLiveUrl()}
      </div>
    </div>
  );
};

export default ViewLiveButton;
