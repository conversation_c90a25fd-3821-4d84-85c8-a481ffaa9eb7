'use client';

import React from 'react';
import { PopupList } from '@payloadcms/ui';
import { useDocumentInfo, useFormFields } from '@payloadcms/ui';
import { generateLiveUrl } from '@/lib/config/environment';

import type { EditMenuItemsClientProps } from 'payload';

/**
 * View Live Menu Item Component
 *
 * A PayloadCMS editMenuItems component that adds a "View Live" option to the
 * dropdown menu next to the Publish button. Follows PayloadCMS native patterns
 * for admin UI customization.
 *
 * Features:
 * - Only appears when content is published and has a slug
 * - Opens live URL without draft mode overlay
 * - Uses PayloadCMS UI components and hooks
 * - Type-safe collection detection
 * - Environment-aware URL generation
 *
 * @example
 * // Add to collection admin config:
 * admin: {
 *   components: {
 *     edit: {
 *       editMenuItems: ['@/components/admin/shared/ViewLiveMenuItem#ViewLiveMenuItem'],
 *     },
 *   },
 * }
 */
export const ViewLiveMenuItem: React.FC<EditMenuItemsClientProps> = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;

  // Get collection from URL path since useDocumentInfo doesn't provide it reliably
  const getCollectionFromPath = (): 'articles' | 'pages' | null => {
    if (typeof window === 'undefined') return null;
    const path = window.location.pathname;
    const match = path.match(/\/admin\/collections\/([^\/]+)/);
    const collection = match ? match[1] : null;

    // Type guard to ensure we only support known collections
    if (collection === 'articles' || collection === 'pages') {
      return collection;
    }
    return null;
  };

  const collection = getCollectionFromPath();

  // Get publication status and slug from form fields
  const status = useFormFields(
    ([fields]) => fields['_status']?.value as string
  );
  const slug = useFormFields(([fields]) => fields.slug?.value as string);

  // Only show for published content with a slug
  const isPublished = status === 'published';
  const hasSlug = Boolean(slug);
  const shouldShow = isPublished && hasSlug && id;

  const handleViewLive = () => {
    if (!shouldShow || !collection || !slug) {
      // Only log in development
      if (process.env.NODE_ENV === 'development') {
        console.warn('ViewLive: Cannot open - missing requirements:', {
          collection,
          slug,
          status,
        });
      }
      return;
    }

    try {
      // Use environment-aware URL generation
      const liveUrl = generateLiveUrl(collection, slug);

      // Open via exit-preview endpoint to disable draft mode and show published version
      const exitPreviewUrl = `/api/exit-preview?slug=${encodeURIComponent(liveUrl)}`;

      window.open(exitPreviewUrl, '_blank', 'noopener,noreferrer');
    } catch (error) {
      console.error('ViewLive: Error opening URL:', error);
    }
  };

  // Don't render anything if conditions aren't met
  if (!shouldShow) {
    return null;
  }

  return (
    <PopupList.Button onClick={handleViewLive}>View Live</PopupList.Button>
  );
};

export default ViewLiveMenuItem;
