'use client';
import { useEffect } from 'react';
import { useAdminBar } from './AdminBarContext';

interface AdminBarDataSetterProps {
  collectionSlug?: string;
  collectionLabels?: {
    singular?: string;
    plural?: string;
  };
  documentId?: string;
}

export const AdminBarDataSetter: React.FC<AdminBarDataSetterProps> = ({
  collectionSlug,
  collectionLabels,
  documentId,
}) => {
  const { setAdminBarData } = useAdminBar();

  useEffect(() => {
    setAdminBarData({
      collectionSlug,
      collectionLabels,
      documentId,
      setAdminBarData, // This will be ignored in the state update
    });
  }, [collectionSlug, collectionLabels, documentId, setAdminBarData]);

  return null; // This component doesn't render anything
};
