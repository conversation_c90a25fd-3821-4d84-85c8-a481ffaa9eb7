/**
 * Page Document Controls Component
 *
 * Provides translation functionality specifically for Pages collection
 * with unified styling matching Articles collection.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-27
 * @updated 2025-01-28 - Unified styling with Articles
 */

'use client';

import React, { useState, useCallback } from 'react';
import {
  useDocumentInfo,
  useFormFields,
  useAllFormFields,
} from '@payloadcms/ui';
import { useArticleNotifications } from '../notifications/ArticleNotifications';

/**
 * Standard API Response Interface
 */
interface StandardAPIResponse {
  success: boolean;
  message: string;
  data?: any;
  error?: string;
  errors?: string[];
  metrics?: {
    processingTime?: number;
    [key: string]: any;
  };
}

/**
 * Page Document Controls Component - Unified with Articles styling
 */
export const PageDocumentControls: React.FC = () => {
  const docInfo = useDocumentInfo();
  const { id } = docInfo;
  const [fields, dispatchFields] = useAllFormFields();
  const notifications = useArticleNotifications();

  // State management - matching Articles
  const [isTranslating, setIsTranslating] = useState(false);
  const [translationJustCompleted, setTranslationJustCompleted] =
    useState(false);

  // Extract field values for validation
  const pageTitle =
    useFormFields(([fields]) => fields['englishTab.title']?.value as string) ||
    '';
  const pageContent =
    useFormFields(([fields]) => fields['englishTab.content']?.value) || '';
  const hasGermanTranslation = useFormFields(
    ([fields]) => fields['germanTab.germanTitle']?.value
  )
    ? true
    : false;

  // Simple validation for pages
  const canTranslate =
    !!id && pageTitle.length >= 20 && pageContent && !isTranslating;

  // Translation handler - matching Articles style
  const handleTranslateToGerman = useCallback(async () => {
    if (!canTranslate) {
      notifications.showValidationError(
        'Please ensure title is at least 20 characters and content is provided'
      );
      return;
    }

    setIsTranslating(true);

    try {
      const response = await fetch('/api/pages/translate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ pageId: id }),
      });

      const result: StandardAPIResponse = await response.json();

      if (result.success && result.data) {
        // Update form fields
        const germanData = result.data.germanTab || {};

        if (germanData.germanTitle) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanTitle',
            value: germanData.germanTitle,
          });
        }

        if (germanData.germanContent) {
          dispatchFields({
            type: 'UPDATE',
            path: 'germanTab.germanContent',
            value: germanData.germanContent,
          });
        }

        // Show success feedback
        notifications.showTranslationSuccess(hasGermanTranslation);
        setTranslationJustCompleted(true);

        // Reset completion flag
        setTimeout(() => {
          setTranslationJustCompleted(false);
        }, 2000);

        // Refresh page to show updated content
        setTimeout(() => {
          window.location.reload();
        }, 1000);
      } else {
        notifications.showOperationError(
          'translation',
          result.error || 'Translation failed'
        );
      }
    } catch (error) {
      notifications.showOperationError(
        'translation',
        'Failed to communicate with translation service'
      );
    } finally {
      setIsTranslating(false);
    }
  }, [canTranslate, id, dispatchFields, notifications, hasGermanTranslation]);

  // Button styling - matching Articles exactly
  const getTranslationButtonText = () => {
    if (isTranslating) return 'Translating to German...';
    if (translationJustCompleted) return 'Translation Complete! Refreshing...';
    return hasGermanTranslation
      ? 'Re-translate to German'
      : 'Translate to German';
  };

  const getTranslationButtonColor = () => {
    if (isTranslating) return '#6B7280'; // Gray for loading
    if (translationJustCompleted) return '#10B981'; // Bright green for success
    if (hasGermanTranslation) return '#059669'; // Green for re-translation
    return '#2563EB'; // Blue for first translation
  };

  const isTranslationDisabled =
    !canTranslate || isTranslating || translationJustCompleted;

  // Unified styling matching Articles
  return (
    <div
      style={{
        display: 'flex',
        flexDirection: 'column',
        gap: '12px',
        marginBottom: '20px',
        padding: '16px',
        paddingBottom: '16px',
        backgroundColor: '#f8fafc',
        borderRadius: '8px',
        border: '1px solid #e2e8f0',
        maxWidth: '300px',
      }}
    >
      {!id ? (
        <div
          style={{
            padding: '12px',
            color: '#9ca3af',
            fontSize: '14px',
            textAlign: 'center',
            fontStyle: 'italic',
          }}
        >
          Save the page first to enable translation
        </div>
      ) : (
        <button
          onClick={handleTranslateToGerman}
          disabled={isTranslationDisabled}
          title={
            !canTranslate
              ? 'Please ensure title is at least 20 characters and content is provided'
              : undefined
          }
          style={{
            backgroundColor: getTranslationButtonColor(),
            color: 'white',
            border: 'none',
            borderRadius: '6px',
            padding: '12px 20px',
            cursor: isTranslationDisabled ? 'not-allowed' : 'pointer',
            opacity: isTranslationDisabled ? 0.6 : 1,
            fontSize: '14px',
            fontWeight: '500',
            minWidth: '160px',
            minHeight: '40px',
            transition: 'all 0.2s ease',
            boxShadow: '0 1px 2px 0 rgba(0, 0, 0, 0.05)',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            gap: '8px',
            margin: '0',
          }}
        >
          {isTranslating && (
            <div
              style={{
                width: '14px',
                height: '14px',
                border: '2px solid rgba(255, 255, 255, 0.3)',
                borderTop: '2px solid white',
                borderRadius: '50%',
                animation: 'spin 1s linear infinite',
                flexShrink: 0,
              }}
            />
          )}
          {translationJustCompleted && (
            <div
              style={{
                width: '14px',
                height: '14px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                flexShrink: 0,
              }}
            >
              ✓
            </div>
          )}
          <span style={{ lineHeight: '1' }}>{getTranslationButtonText()}</span>
        </button>
      )}
    </div>
  );
};

export default PageDocumentControls;
