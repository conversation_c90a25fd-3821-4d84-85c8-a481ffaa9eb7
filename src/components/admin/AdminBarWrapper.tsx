'use client';
import React from 'react';
import { PayloadAdminBar } from '@payloadcms/admin-bar';
import { useAdminBar } from './AdminBarContext';
import { getClientSideURL } from '@/utilities/getURL';

interface AdminBarWrapperProps {
  devMode?: boolean;
  preview?: boolean;
  onPreviewExit?: () => void;
}

export const AdminBarWrapper: React.FC<AdminBarWrapperProps> = ({
  devMode = false,
  preview = false,
  onPreviewExit,
}) => {
  const { collectionSlug, collectionLabels, documentId } = useAdminBar();

  // Get the CMS URL (same domain as the frontend in this setup)
  const cmsURL = getClientSideURL();

  return (
    <PayloadAdminBar
      cmsURL={cmsURL}
      adminPath="/admin"
      apiPath="/api"
      authCollectionSlug="users"
      collectionSlug={collectionSlug}
      collectionLabels={collectionLabels}
      id={documentId}
      devMode={devMode}
      preview={preview}
      onPreviewExit={onPreviewExit}
      className="payload-admin-bar"
      style={{
        // Override default fixed positioning to make it part of document flow
        position: 'relative',
        top: 'auto',
        left: 'auto',
        width: '100%',
        zIndex: 'auto',
      }}
    />
  );
};
