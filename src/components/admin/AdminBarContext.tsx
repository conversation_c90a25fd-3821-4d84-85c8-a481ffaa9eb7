'use client';
import React, { createContext, useContext, useState, useCallback } from 'react';

interface AdminBarContextType {
  collectionSlug?: string;
  collectionLabels?: {
    singular?: string;
    plural?: string;
  };
  documentId?: string;
  setAdminBarData: (data: AdminBarContextType) => void;
}

const AdminBarContext = createContext<AdminBarContextType | undefined>(
  undefined
);

export const useAdminBar = () => {
  const context = useContext(AdminBarContext);
  if (!context) {
    throw new Error('useAdminBar must be used within an AdminBarProvider');
  }
  return context;
};

interface AdminBarProviderProps {
  children: React.ReactNode;
}

export const AdminBarProvider: React.FC<AdminBarProviderProps> = ({
  children,
}) => {
  const [adminBarData, setAdminBarDataState] = useState<
    Omit<AdminBarContextType, 'setAdminBarData'>
  >({});

  const setAdminBarData = useCallback((data: AdminBarContextType) => {
    setAdminBarDataState({
      collectionSlug: data.collectionSlug,
      collectionLabels: data.collectionLabels,
      documentId: data.documentId,
    });
  }, []);

  return (
    <AdminBarContext.Provider
      value={{
        ...adminBarData,
        setAdminBarData,
      }}
    >
      {children}
    </AdminBarContext.Provider>
  );
};
