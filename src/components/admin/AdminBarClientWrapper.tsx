'use client';
import React from 'react';
import { AdminBarWrapper } from './AdminBarWrapper';

interface AdminBarClientWrapperProps {
  preview?: boolean;
  devMode?: boolean;
}

export const AdminBarClientWrapper: React.FC<AdminBarClientWrapperProps> = ({
  preview = false,
  devMode = false,
}) => {
  const handlePreviewExit = () => {
    // Client-side navigation to exit preview
    window.location.href = '/api/exit-preview';
  };

  return (
    <AdminBarWrapper
      preview={preview}
      onPreviewExit={handlePreviewExit}
      devMode={devMode}
    />
  );
};
