/**
 * Cache Debugging Panel Component
 *
 * Provides admin interface for cache debugging, health checks, and emergency clearing.
 * Critical tool for diagnosing and fixing publishing cache issues.
 *
 * <AUTHOR> Development Team
 * @created 2025-01-28
 * @priority CRITICAL - Cache debugging tools
 */
'use client';

import React, { useState, useCallback } from 'react';

interface CacheHealthStatus {
  status: 'healthy' | 'degraded' | 'unhealthy';
  issues: string[];
  recommendations: string[];
}

interface ApiResponse {
  success: boolean;
  message?: string;
  error?: string;
  data?: CacheHealthStatus;
  timestamp?: string;
  action?: string;
}

export const CacheDebugPanel: React.FC = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [lastResult, setLastResult] = useState<ApiResponse | null>(null);
  const [healthStatus, setHealthStatus] = useState<CacheHealthStatus | null>(
    null
  );
  const [testArticleId, setTestArticleId] = useState<string>('');

  const executeAction = useCallback(
    async (action: string, actionName: string) => {
      setIsLoading(true);
      setLastResult(null);

      const controller = new AbortController();

      try {
        console.log(`🚀 Executing cache action: ${actionName}`);

        const method = action === 'health-check' ? 'GET' : 'POST';
        const url =
          action === 'health-check'
            ? '/api/admin/nuclear-cache-clear'
            : `/api/admin/nuclear-cache-clear?action=${action}`;

        const response = await fetch(url, {
          method,
          signal: controller.signal,
        });
        const result: ApiResponse = await response.json();

        setLastResult(result);

        if (result.data && action === 'health-check') {
          setHealthStatus(result.data);
        }

        console.log(
          `${result.success ? '✅' : '❌'} ${actionName} result:`,
          result
        );
      } catch (error) {
        // Don't show error if request was aborted
        if (error instanceof Error && error.name === 'AbortError') {
          return;
        }

        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        console.error(`❌ ${actionName} failed:`, error);

        setLastResult({
          success: false,
          error: errorMessage,
          timestamp: new Date().toISOString(),
        });
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const testPublishWorkflow = useCallback(
    async (articleId: string, action: string = 'full-test') => {
      if (!articleId.trim()) {
        setLastResult({
          success: false,
          error: 'Please enter an article ID',
          timestamp: new Date().toISOString(),
        });
        return;
      }

      setIsLoading(true);
      setLastResult(null);

      const controller = new AbortController();

      try {
        console.log(`🧪 Testing publish workflow for article ${articleId}`);

        const response = await fetch(
          `/api/debug/test-publish-workflow?articleId=${articleId}&action=${action}`,
          {
            method: 'POST',
            signal: controller.signal,
          }
        );
        const result: ApiResponse = await response.json();

        setLastResult(result);
        console.log(
          `${result.success ? '✅' : '❌'} Publish workflow test result:`,
          result
        );
      } catch (error) {
        // Don't show error if request was aborted
        if (error instanceof Error && error.name === 'AbortError') {
          return;
        }

        const errorMessage =
          error instanceof Error ? error.message : 'Unknown error';
        console.error(`❌ Publish workflow test failed:`, error);

        setLastResult({
          success: false,
          error: errorMessage,
          timestamp: new Date().toISOString(),
        });
      } finally {
        setIsLoading(false);
      }
    },
    []
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'healthy':
        return 'text-green-600 bg-green-50 border-green-200';
      case 'degraded':
        return 'text-yellow-600 bg-yellow-50 border-yellow-200';
      case 'unhealthy':
        return 'text-red-600 bg-red-50 border-red-200';
      default:
        return 'text-gray-600 bg-gray-50 border-gray-200';
    }
  };

  return (
    <div className="p-6 bg-white border border-gray-200 rounded-lg shadow-sm">
      <div className="mb-6">
        <h2 className="text-xl font-semibold text-gray-900 mb-2">
          🚨 Cache Debugging Panel
        </h2>
        <p className="text-sm text-gray-600">
          Emergency tools for diagnosing and fixing cache issues. Use when
          articles aren&apos;t updating on the frontend.
        </p>
      </div>

      {/* Health Status */}
      {healthStatus && (
        <div
          className={`mb-6 p-4 border rounded-lg ${getStatusColor(healthStatus.status)}`}
        >
          <div className="flex items-center mb-2">
            <span className="font-semibold">Cache Health Status: </span>
            <span className="ml-2 capitalize font-bold">
              {healthStatus.status}
            </span>
          </div>

          {healthStatus.issues.length > 0 && (
            <div className="mt-3">
              <p className="font-medium mb-1">Issues Found:</p>
              <ul className="list-disc list-inside text-sm space-y-1">
                {healthStatus.issues.map((issue, index) => (
                  <li key={index}>{issue}</li>
                ))}
              </ul>
            </div>
          )}

          {healthStatus.recommendations.length > 0 && (
            <div className="mt-3">
              <p className="font-medium mb-1">Recommendations:</p>
              <ul className="list-disc list-inside text-sm space-y-1">
                {healthStatus.recommendations.map((rec, index) => (
                  <li key={index}>{rec}</li>
                ))}
              </ul>
            </div>
          )}
        </div>
      )}

      {/* Action Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <button
          onClick={() => executeAction('health-check', 'Cache Health Check')}
          disabled={isLoading}
          className="px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {isLoading ? '⏳ Checking...' : '🔍 Check Cache Health'}
        </button>

        <button
          onClick={() =>
            executeAction('vercel-edge', 'Vercel Edge Cache Clear')
          }
          disabled={isLoading}
          className="px-4 py-3 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {isLoading ? '⏳ Clearing...' : '🔥 Clear Vercel Edge Cache'}
        </button>

        <button
          onClick={() => executeAction('nuclear', 'Nuclear Cache Clear')}
          disabled={isLoading}
          className="px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
        >
          {isLoading ? '⏳ Clearing...' : '☢️ Nuclear Clear All'}
        </button>
      </div>

      {/* Publish Workflow Testing */}
      <div className="mb-6 p-4 bg-purple-50 border border-purple-200 rounded-lg">
        <h3 className="font-semibold text-purple-900 mb-3">
          🧪 Publish Workflow Testing
        </h3>
        <p className="text-sm text-purple-800 mb-4">
          Test the complete publish workflow: database persistence, cache
          invalidation, and frontend retrieval
        </p>

        <div className="flex flex-col sm:flex-row gap-3">
          <input
            type="text"
            value={testArticleId}
            onChange={e => setTestArticleId(e.target.value)}
            placeholder="Enter Article ID (e.g., 673be9f26a84b8b77d0a6473)"
            className="flex-1 px-3 py-2 border border-purple-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
            disabled={isLoading}
          />

          <div className="flex gap-2">
            <button
              onClick={() => testPublishWorkflow(testArticleId, 'full-test')}
              disabled={isLoading || !testArticleId.trim()}
              className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {isLoading ? '⏳ Testing...' : '🧪 Test Workflow'}
            </button>

            <button
              onClick={() =>
                testPublishWorkflow(testArticleId, 'simulate-publish')
              }
              disabled={isLoading || !testArticleId.trim()}
              className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50 disabled:cursor-not-allowed font-medium"
            >
              {isLoading ? '⏳ Simulating...' : '🚀 Simulate Publish'}
            </button>
          </div>
        </div>

        <p className="text-xs text-purple-600 mt-2">
          💡 Find article IDs in PayloadCMS admin URL or database. Test Workflow
          checks data persistence, Simulate Publish triggers cache invalidation.
        </p>
      </div>

      {/* Last Result */}
      {lastResult && (
        <div className="mb-4">
          <h3 className="font-semibold mb-2">Last Action Result:</h3>
          <div
            className={`p-4 rounded-lg border ${
              lastResult.success
                ? 'bg-green-50 border-green-200 text-green-800'
                : 'bg-red-50 border-red-200 text-red-800'
            }`}
          >
            <div className="flex items-center mb-2">
              <span className="font-medium text-lg">
                {lastResult.success ? '✅ Success' : '❌ Failed'}
              </span>
              {lastResult.timestamp && (
                <span className="ml-auto text-sm opacity-75">
                  {new Date(lastResult.timestamp).toLocaleString()}
                </span>
              )}
            </div>

            {lastResult.message && (
              <p className="text-sm mb-2">{lastResult.message}</p>
            )}

            {lastResult.error && (
              <p className="text-sm font-mono bg-black bg-opacity-10 p-2 rounded">
                {lastResult.error}
              </p>
            )}

            {lastResult.action && (
              <p className="text-xs opacity-75 mt-2">
                Action: {lastResult.action}
              </p>
            )}
          </div>
        </div>
      )}

      {/* Instructions */}
      <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h4 className="font-semibold text-blue-900 mb-2">
          📋 Troubleshooting Guide:
        </h4>
        <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
          <li>
            <strong>Test Publish Workflow</strong> - Verify database persistence
            and cache invalidation for specific articles
          </li>
          <li>
            <strong>Check Cache Health</strong> - Diagnose current cache status
          </li>
          <li>
            <strong>Clear Vercel Edge Cache</strong> - Fix most publishing
            issues (recommended first)
          </li>
          <li>
            <strong>Nuclear Clear</strong> - Last resort when edge clear
            doesn&apos;t work
          </li>
          <li>
            <strong>Wait 2-3 minutes</strong> after clearing for changes to
            propagate
          </li>
          <li>
            <strong>Test in incognito mode</strong> to bypass browser cache
          </li>
        </ol>
      </div>

      {/* Warning */}
      <div className="mt-4 bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <p className="text-sm text-yellow-800">
          <strong>⚠️ Warning:</strong> Cache clearing affects all users. Use
          during low-traffic periods when possible. Nuclear clear may
          temporarily slow down the site as caches rebuild.
        </p>
      </div>
    </div>
  );
};
