'use client';

import Link from 'next/link';
import Image from 'next/image';
import { memo, useState, useMemo } from 'react';
import {
  <PERSON>,
  CardHeader,
  CardFooter as U<PERSON><PERSON><PERSON>ooter,
  CardTitle,
  CardDescription,
} from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { Article } from '@/payload-types';
import { getRelativeTime } from '@/lib/utils/relative-time';
import {
  formatExactDate,
  getPublishedDateAriaLabel,
} from '@/lib/utils/date-formatting';
import { formatReadingTime } from '@/lib/utils/readtime'; // ✅ PHASE 2: Use readtime utility
import { useRelativeTimeOptimized } from '@/hooks/use-relative-time';

interface NewsCardProps {
  article: Article;
  variant?: 'default' | 'title-only' | 'horizontal-left' | 'horizontal-right';
  showDescription?: boolean;
  className?: string;
  priority?: boolean;
  locale?: 'en' | 'de';
  hideCategory?: boolean;
}

// Helper function to extract text from Lexical content
const extractTextFromLexical = (lexicalContent: any): string => {
  const extractText = (node: any): string => {
    if (!node) return '';
    if (node.type === 'text') return node.text || '';
    if (node.children && Array.isArray(node.children)) {
      return node.children.map(extractText).join(' ');
    }
    return '';
  };

  try {
    return extractText(lexicalContent?.root || {});
  } catch (error) {
    console.warn('Failed to extract text from Lexical content:', error);
    return '';
  }
};

// Helper function to get first sentence
const getFirstSentence = (text: string): string => {
  if (!text || text.trim() === '') return '';
  const sentences = text.split(/[.!?]+/);
  return sentences[0]?.trim() + (sentences.length > 1 ? '...' : '');
};

// Helper function to truncate text
const truncateText = (text: string, maxLength: number): string => {
  if (text.length <= maxLength) return text;
  return text.slice(0, maxLength).replace(/\s+\S*$/, '...');
};

// Helper function to format trending text
const getTrendingText = (locale: 'en' | 'de' = 'en'): string => {
  const translations = {
    en: 'Trending',
    de: 'Trending',
  };
  return translations[locale] || translations.en;
};

// Image component with error handling
const CardImage = memo(
  ({
    featuredImage,
    title,
    variant,
    priority = false,
  }: {
    featuredImage: any | undefined;
    title: string;
    variant: string;
    priority?: boolean;
  }) => {
    const [imageError, setImageError] = useState(false);
    const [imageLoading, setImageLoading] = useState(true);

    const isHorizontal =
      variant === 'horizontal-left' || variant === 'horizontal-right';
    const fallbackImage = '/images/mountain-illustration.webp';

    // Get the appropriate image URL based on variant
    const imageUrl = useMemo(() => {
      if (!featuredImage) return undefined;

      if (isHorizontal) {
        // Use horizontal size (288x288) for horizontal variants
        return featuredImage.sizes?.horizontal?.url || featuredImage.url;
      } else {
        // Use card size (1024x576) for default variants
        return featuredImage.sizes?.card?.url || featuredImage.url;
      }
    }, [featuredImage, isHorizontal]);

    const handleError = () => {
      setImageError(true);
      setImageLoading(false);
    };

    const handleLoad = () => {
      setImageLoading(false);
    };

    // Horizontal layout [Marker]
    if (isHorizontal) {
      return (
        <div className="shrink-0 aspect-square w-24 sm:w-28 md:w-32 lg:w-36">
          <div className="overflow-hidden rounded-lg size-full relative">
            {imageLoading && (
              <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse rounded-lg" />
            )}
            <Image
              src={imageError ? fallbackImage : imageUrl || fallbackImage}
              alt={title}
              fill
              className="object-cover transition-transform duration-300 ease-out group-hover:scale-105"
              onError={handleError}
              onLoad={handleLoad}
              priority={priority}
              sizes="(max-width: 640px) 35vw, (max-width: 768px) 30vw, 25vw"
            />
          </div>
        </div>
      );
    }

    return (
      <div className="overflow-hidden rounded-lg mb-4 relative">
        {imageLoading && (
          <div className="absolute inset-0 bg-gray-200 dark:bg-gray-800 animate-pulse rounded-lg aspect-[16/9]" />
        )}
        <Image
          src={imageError ? fallbackImage : imageUrl || fallbackImage}
          alt={title}
          width={1024}
          height={576}
          className="aspect-[16/9] w-full object-cover transition-transform duration-300 ease-out group-hover:scale-105"
          onError={handleError}
          onLoad={handleLoad}
          priority={priority}
          sizes="(max-width: 640px) 100vw, (max-width: 768px) 50vw, 33vw"
        />
      </div>
    );
  }
);

CardImage.displayName = 'CardImage';

// Content component
const CardContent = memo(
  ({
    title,
    description,
    variant,
    showDescription,
    articleId,
  }: {
    title: string;
    description: string | undefined;
    variant: string;
    showDescription: boolean;
    articleId: number;
  }) => {
    const titleId = `title-${articleId}`;
    // Main Card Header [Marker]
    return (
      <CardHeader className="px-0 my-0 pb-0 pt-0">
        <CardTitle
          id={titleId}
          className="font-serif text-sm/5 @xs:text-base/6 @sm:text-lg/7 font-normal text-pretty text-foreground dark:text-gray-100"
        >
          {truncateText(title, 120)}
        </CardTitle>

        {/* Show description for title-only variant when showDescription is true */}
        {variant === 'title-only' && showDescription && description && (
          <CardDescription className="mt-0 font-sans text-xs @sm:text-sm text-muted-foreground dark:text-gray-400 text-pretty hyphens-auto break-words antialiased tracking-normal">
            {truncateText(getFirstSentence(description), 100)}
          </CardDescription>
        )}

        {/* Show description for default variant */}
        {variant === 'default' && description && (
          <CardDescription className="mt-0 font-sans text-sm/5 @lg:text-base/6 text-muted-foreground dark:text-gray-400 text-pretty hyphens-auto break-words antialiased tracking-normal">
            {truncateText(description, 150)}
          </CardDescription>
        )}

        {/* Show description for horizontal variants */}
        {(variant === 'horizontal-left' || variant === 'horizontal-right') &&
          description && (
            <CardDescription className="mt-0 font-sans text-sm/5 @lg:text-base/6 text-muted-foreground dark:text-gray-400 text-pretty hyphens-auto break-words antialiased tracking-wide">
              {truncateText(description, 120)}
            </CardDescription>
          )}
      </CardHeader>
    );
  }
);

CardContent.displayName = 'CardContent';

// Footer component [Marker]
const CardFooterContent = memo(
  ({
    category,
    readTime,
    publishedDate,
    trending,
    locale,
    variant,
    hideCategory,
  }: {
    category: string | undefined;
    readTime: string | undefined;
    publishedDate: string | null;
    trending: boolean;
    locale: 'en' | 'de';
    variant?: 'default' | 'title-only' | 'horizontal-left' | 'horizontal-right';
    hideCategory?: boolean;
  }) => {
    // Calculate relative time for published date with auto-refresh
    // Always call the hook, but pass null handling internally
    const relativeTime = useRelativeTimeOptimized(
      publishedDate || new Date().toISOString(), // Fallback to current date
      locale
    );

    // Only show relative time if we have a valid published date
    const displayRelativeTime = publishedDate ? relativeTime : null;

    if (!category && !readTime && !displayRelativeTime) return null;

    return (
      <UICardFooter
        className={`px-0 pb-0 ${
          variant === 'horizontal-left' || variant === 'horizontal-right'
            ? 'pt-1 mt-1'
            : 'pt-2 mt-auto'
        }`}
      >
        <div className="flex items-center gap-4 text-xs/4 font-sans">
          {category && !hideCategory && (
            <Badge
              variant="text"
              className="text-[#B08D57] dark:text-[#D4AF37] [a&]:hover:bg-[#B08D57]/10 dark:[a&]:hover:bg-[#D4AF37]/10"
            >
              {category}
            </Badge>
          )}
          <div className="flex items-center gap-2">
            {readTime && !trending && (
              <span className="text-gray-500 dark:text-gray-400">
                {readTime}
              </span>
            )}
            {displayRelativeTime && readTime && !trending && (
              <span className="text-gray-400 dark:text-gray-500">•</span>
            )}
            {displayRelativeTime && (
              <span
                className="text-gray-500 dark:text-gray-400 cursor-help"
                title={formatExactDate(publishedDate!, locale)}
                aria-label={getPublishedDateAriaLabel(publishedDate!, locale)}
              >
                {displayRelativeTime}
              </span>
            )}
            {trending && (
              <div
                className="flex items-center gap-1"
                role="status"
                aria-label="Trending article"
              >
                <div className="relative flex size-3 items-center justify-center">
                  <span className="animate-ping absolute inline-flex size-2 rounded-full bg-[#89484B] opacity-75"></span>
                  <span className="relative inline-flex size-1.5 rounded-full bg-[#89484B]"></span>
                </div>
                <span className="text-[#89484B] font-medium text-[10px]/3 uppercase tracking-wide">
                  {getTrendingText(locale)}
                </span>
              </div>
            )}
          </div>
        </div>
      </UICardFooter>
    );
  }
);

CardFooterContent.displayName = 'CardFooterContent';

// Skeleton loading component
export const CardSkeleton = ({ variant = 'default' }: { variant?: string }) => {
  const isHorizontal =
    variant === 'horizontal-left' || variant === 'horizontal-right';

  return (
    <Card className="@container overflow-hidden rounded-lg border-none p-4">
      <div className="animate-pulse">
        {isHorizontal ? (
          <div className="flex gap-4">
            <div className="shrink-0 aspect-square w-24 sm:w-28 md:w-32 lg:w-36">
              <div className="bg-gray-200 dark:bg-gray-800 size-full rounded-lg" />
            </div>
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded-sm" />
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded-sm" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded-sm max-w-[60%]" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded-sm max-w-[40%]" />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {variant === 'default' && (
              <div className="bg-gray-200 dark:bg-gray-800 aspect-[16/9] rounded-lg" />
            )}
            <div className="space-y-2">
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded-sm" />
              <div className="h-4 bg-gray-200 dark:bg-gray-800 rounded-sm" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded-sm max-w-[60%]" />
              <div className="h-3 bg-gray-200 dark:bg-gray-800 rounded-sm max-w-[40%]" />
            </div>
          </div>
        )}
      </div>
    </Card>
  );
};

// Main NewsCard component
const NewsCard = memo(
  ({
    article,
    variant = 'default',
    showDescription = false,
    className = '',
    priority = false,
    locale = 'en',
    hideCategory = false,
  }: NewsCardProps) => {
    // Extract data from PayloadCMS article - memoized for performance
    const title = useMemo(() => {
      return (
        article.germanTab?.germanTitle ||
        article.englishTab?.enhancedTitle ||
        article.title
      );
    }, [
      article.germanTab?.germanTitle,
      article.englishTab?.enhancedTitle,
      article.title,
    ]);

    const description = useMemo(() => {
      return (
        article.germanTab?.germanSummary ||
        article.englishTab?.enhancedSummary ||
        undefined
      );
    }, [article.germanTab?.germanSummary, article.englishTab?.enhancedSummary]);

    const category = useMemo(() => {
      return article.categories?.[0] &&
        typeof article.categories[0] === 'object'
        ? article.categories[0].title
        : undefined;
    }, [article.categories]);

    const featuredImage = useMemo(() => {
      return article.featuredImage && typeof article.featuredImage === 'object'
        ? article.featuredImage
        : undefined;
    }, [article.featuredImage]);

    // Calculate read time from content with proper text extraction - memoized
    const content = useMemo(() => {
      return (
        article.germanTab?.germanContent || article.englishTab?.enhancedContent
      );
    }, [article.germanTab?.germanContent, article.englishTab?.enhancedContent]);

    // ✅ PHASE 2: Use pre-computed readTimeMinutes field instead of calculating from content
    const readTime = useMemo(() => {
      const readTimeMinutes = (article as any).readTimeMinutes; // Temp: until dev server restart
      return readTimeMinutes
        ? formatReadingTime(readTimeMinutes, locale)
        : undefined;
    }, [(article as any).readTimeMinutes, locale]);

    // Extract published date - use publishedAt if available, fallback to createdAt - memoized
    const publishedDate = useMemo(() => {
      return article.publishedAt || article.createdAt;
    }, [article.publishedAt, article.createdAt]);

    // Check if article is trending (manual override from admin)
    const trending = useMemo(() => {
      return (article as any).trending || false;
    }, [article]);

    // Computed values - memoized for performance
    const isHorizontal = useMemo(() => {
      return variant === 'horizontal-left' || variant === 'horizontal-right';
    }, [variant]);

    const imageOnRight = useMemo(() => {
      return variant === 'horizontal-right';
    }, [variant]);

    const href = useMemo(() => {
      return `/artikel/${article.slug || article.id}`;
    }, [article.slug, article.id]);

    // Determine if this should be prefetched - memoized
    const shouldPrefetch = useMemo(() => {
      return variant === 'default' || priority;
    }, [variant, priority]);

    return (
      <Card
        className={`@container group overflow-hidden rounded-lg border-none p-4 !shadow-none transition-all duration-300 ease-out hover:bg-gray-50 dark:hover:bg-gray-900/50 ${className}`}
      >
        <Link
          href={href}
          className="block size-full"
          prefetch={shouldPrefetch}
          aria-label={`Read article: ${title}`}
        >
          <article role="article" aria-labelledby={`title-${article.id}`}>
            {isHorizontal ? (
              // Horizontal layout
              <div
                className={`flex size-full gap-4 ${imageOnRight ? 'flex-row' : 'flex-row-reverse'}`}
              >
                <CardImage
                  featuredImage={featuredImage}
                  title={title}
                  variant={variant}
                  priority={priority}
                />

                {/* Content section */}
                <div className="flex flex-col flex-1 min-w-0">
                  <CardContent
                    title={title}
                    description={description}
                    variant={variant}
                    showDescription={showDescription}
                    articleId={article.id}
                  />

                  <CardFooterContent
                    category={category}
                    readTime={readTime}
                    publishedDate={publishedDate}
                    trending={trending}
                    locale={locale}
                    variant={variant}
                    hideCategory={hideCategory}
                  />
                </div>
              </div>
            ) : (
              // Vertical layout (default and title-only)
              <div className="flex flex-col size-full justify-between">
                <div>
                  {/* Image - only show for default variant */}
                  {variant === 'default' && (
                    <CardImage
                      featuredImage={featuredImage}
                      title={title}
                      variant={variant}
                      priority={priority}
                    />
                  )}

                  <CardContent
                    title={title}
                    description={description}
                    variant={variant}
                    showDescription={showDescription}
                    articleId={article.id}
                  />
                </div>

                <CardFooterContent
                  category={category}
                  readTime={readTime}
                  publishedDate={publishedDate}
                  trending={trending}
                  locale={locale}
                  variant={variant}
                  hideCategory={hideCategory}
                />
              </div>
            )}
          </article>
        </Link>
      </Card>
    );
  },
  (prevProps, nextProps) => {
    // Memoization comparison
    return (
      prevProps.article.id === nextProps.article.id &&
      prevProps.variant === nextProps.variant &&
      prevProps.showDescription === nextProps.showDescription &&
      prevProps.className === nextProps.className &&
      prevProps.priority === nextProps.priority &&
      prevProps.locale === nextProps.locale
    );
  }
);

NewsCard.displayName = 'NewsCard';

export default NewsCard;
