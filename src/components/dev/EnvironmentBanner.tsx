'use client';

import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Database, Globe, X, ChevronUp, ChevronDown } from 'lucide-react';
import { useState, useEffect } from 'react';

interface EnvironmentInfo {
  environment: 'local' | 'preview' | 'production' | 'unknown';
  database: 'local' | 'preview' | 'production' | 'unknown';
  serverUrl: string;
}

export function EnvironmentBanner() {
  const [isMinimized, setIsMinimized] = useState(false);
  const [isVisible, setIsVisible] = useState(true);
  const [envInfo, setEnvInfo] = useState<EnvironmentInfo>({
    environment: 'unknown',
    database: 'unknown',
    serverUrl: '',
  });

  useEffect(() => {
    // Only show in development (local development environment)
    if (process.env.NODE_ENV !== 'development') {
      setIsVisible(false);
      return;
    }

    // Get the environment you're pointing to from PROJECT_ENV
    const projectEnv = process.env.NEXT_PUBLIC_PROJECT_ENV?.toLowerCase() || '';
    const serverUrl = process.env.NEXT_PUBLIC_SERVER_URL || '';

    let environment: EnvironmentInfo['environment'] = 'unknown';
    let database: EnvironmentInfo['database'] = 'unknown';

    // First try to use PROJECT_ENV for reliable detection
    if (projectEnv) {
      switch (projectEnv) {
        case 'local':
          environment = 'local';
          database = 'local';
          break;
        case 'preview':
          environment = 'preview';
          database = 'preview';
          break;
        case 'production':
          environment = 'production';
          database = 'production';
          break;
      }
    }

    // Fallback to URL-based detection if PROJECT_ENV isn't available
    if (environment === 'unknown') {
      if (serverUrl.includes('localhost')) {
        environment = 'local';
        database = 'local';
      } else if (
        serverUrl.includes('preview') ||
        serverUrl.includes('vercel.app')
      ) {
        environment = 'preview';
        database = 'preview';
      } else if (serverUrl.includes('boersenblick.com')) {
        environment = 'production';
        database = 'production';
      }
    }

    setEnvInfo({
      environment,
      database,
      serverUrl,
    });
  }, []);

  if (!isVisible) return null;

  const getEnvironmentColor = (env: string) => {
    switch (env) {
      case 'local':
        return 'bg-green-500';
      case 'preview':
        return 'bg-orange-500';
      case 'production':
        return 'bg-red-500';
      default:
        return 'bg-gray-500';
    }
  };

  const getDatabaseColor = (db: string) => {
    switch (db) {
      case 'local':
        return 'text-green-600';
      case 'preview':
        return 'text-orange-600';
      case 'production':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  const minimizedView = (
    <div className="flex items-center justify-center px-6 py-1.5 relative">
      <div className="flex items-center gap-3">
        <div className="flex items-center gap-1.5">
          <div
            className={`w-1.5 h-1.5 rounded-full ${getEnvironmentColor(envInfo.environment)} animate-pulse`}
          />
          <span className="text-xs font-medium text-muted-foreground">
            {envInfo.environment.toUpperCase()}
          </span>
        </div>
        <div className="h-3 w-px bg-border/50" />
        <div className="flex items-center gap-1.5">
          <Database
            className={`w-3 h-3 ${getDatabaseColor(envInfo.database)}`}
          />
          <span className="text-xs text-muted-foreground">
            {envInfo.database}
          </span>
        </div>
      </div>
      <div className="absolute right-6 flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMinimized(false)}
          className="h-5 w-5 p-0 hover:bg-muted/30"
        >
          <ChevronUp className="w-2.5 h-2.5" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(false)}
          className="h-5 w-5 p-0 hover:bg-muted/30 text-muted-foreground hover:text-foreground"
        >
          <X className="w-2.5 h-2.5" />
        </Button>
      </div>
    </div>
  );

  const expandedView = (
    <div className="flex items-center justify-center px-6 py-2 relative">
      <div className="flex items-center gap-6">
        <div className="flex items-center gap-2">
          <div
            className={`w-1.5 h-1.5 rounded-full ${getEnvironmentColor(envInfo.environment)} animate-pulse`}
          />
          <span className="text-xs font-medium text-muted-foreground">
            Development Environment
          </span>
        </div>

        <div className="flex items-center gap-4 text-xs">
          <div className="flex items-center gap-1.5">
            <Globe className="w-3 h-3 text-muted-foreground" />
            <span className="text-muted-foreground">ENV:</span>
            <Badge
              variant="outline"
              className="h-4 text-xs font-mono bg-background/50"
            >
              {envInfo.environment}
            </Badge>
          </div>

          <div className="flex items-center gap-1.5">
            <Database
              className={`w-3 h-3 ${getDatabaseColor(envInfo.database)}`}
            />
            <span className="text-muted-foreground">DB:</span>
            <Badge
              variant="outline"
              className="h-4 text-xs font-mono bg-background/50"
            >
              {envInfo.database}
            </Badge>
          </div>

          {envInfo.serverUrl && (
            <div className="flex items-center gap-1.5">
              <span className="text-muted-foreground">URL:</span>
              <code className="text-xs bg-muted/50 px-1.5 py-0.5 rounded font-mono">
                {envInfo.serverUrl
                  .replace('https://', '')
                  .replace('http://', '')
                  .substring(0, 25)}
                {envInfo.serverUrl.length > 25 ? '...' : ''}
              </code>
            </div>
          )}
        </div>
      </div>

      <div className="absolute right-6 flex items-center gap-1">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsMinimized(true)}
          className="h-5 w-5 p-0 hover:bg-muted/30"
        >
          <ChevronDown className="w-2.5 h-2.5" />
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsVisible(false)}
          className="h-5 w-5 p-0 hover:bg-muted/30 text-muted-foreground hover:text-foreground"
        >
          <X className="w-2.5 h-2.5" />
        </Button>
      </div>
    </div>
  );

  return (
    <div className="fixed bottom-0 left-0 right-0 z-50">
      <div className="border-t border-border/30 bg-background/60 backdrop-blur-sm supports-[backdrop-filter]:bg-background/40">
        {isMinimized ? minimizedView : expandedView}
      </div>
    </div>
  );
}
