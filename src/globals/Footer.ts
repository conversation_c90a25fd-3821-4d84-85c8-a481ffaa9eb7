import type { GlobalConfig } from 'payload';
import { invalidateGlobalCaches } from '../lib/cache/invalidation';
import { link } from '@/fields/link';

export const Footer: GlobalConfig = {
  slug: 'footer',
  access: {
    read: () => true,
  },
  fields: [
    // Logo Configuration
    {
      name: 'logo',
      type: 'group',
      fields: [
        {
          name: 'image',
          type: 'relationship',
          relationTo: 'media',
          required: false,
          admin: {
            description: 'Upload logo image (optional)',
          },
        },
        {
          name: 'title',
          type: 'text',
          required: true,
          defaultValue: 'BörsenBlick',
          admin: {
            description: 'Logo text/title',
          },
        },
        {
          name: 'url',
          type: 'text',
          defaultValue: '/',
          admin: {
            description: 'URL when logo is clicked',
          },
        },
      ],
      admin: {
        description: 'Configure the footer logo and branding',
      },
    },

    // Description/Tagline
    {
      name: 'description',
      type: 'textarea',
      required: false,
      admin: {
        description: 'Brief description or tagline for the footer',
      },
    },

    // Navigation Sections (3 columns)
    {
      name: 'navigationSections',
      type: 'array',
      fields: [
        {
          name: 'title',
          type: 'text',
          required: true,
          admin: {
            description:
              'Section title (e.g., "Product", "Company", "Resources")',
          },
        },
        {
          name: 'links',
          type: 'array',
          fields: [
            link({
              appearances: false,
            }),
          ],
          maxRows: 8,
          admin: {
            initCollapsed: true,
            components: {
              RowLabel:
                '@/components/admin/footer/SectionRowLabel#SectionRowLabel',
            },
          },
        },
      ],
      maxRows: 3,
      admin: {
        initCollapsed: true,
        description: 'Main footer navigation sections (max 3 columns)',
        components: {
          RowLabel:
            '@/components/admin/footer/NavigationRowLabel#NavigationRowLabel',
        },
      },
    },

    // Social Links
    {
      name: 'socialLinks',
      type: 'array',
      fields: [
        {
          name: 'platform',
          type: 'select',
          required: true,
          options: [
            { label: 'Instagram', value: 'instagram' },
            { label: 'Facebook', value: 'facebook' },
            { label: 'Twitter/X', value: 'twitter' },
            { label: 'LinkedIn', value: 'linkedin' },
            { label: 'YouTube', value: 'youtube' },
            { label: 'GitHub', value: 'github' },
            { label: 'Threads', value: 'threads' },
            { label: 'Email', value: 'email' },
          ],
          admin: {
            description: 'Choose the social media platform',
          },
        },
        {
          name: 'url',
          type: 'text',
          required: true,
          admin: {
            description: 'Full URL to your social media profile',
          },
        },
        {
          name: 'label',
          type: 'text',
          required: true,
          admin: {
            description: 'Accessibility label (e.g., "Follow us on Instagram")',
          },
        },
      ],
      maxRows: 12,
      admin: {
        initCollapsed: true,
        description: 'Social media links and icons',
        components: {
          RowLabel: '@/components/admin/footer/SocialRowLabel#SocialRowLabel',
        },
      },
    },

    // Legal/Privacy Links (bottom row)
    {
      name: 'legalLinks',
      type: 'array',
      fields: [
        link({
          appearances: false,
        }),
      ],
      maxRows: 5,
      admin: {
        initCollapsed: true,
        description: 'Legal and privacy links displayed at the bottom',
        components: {
          RowLabel: '@/components/admin/footer/LegalRowLabel#LegalRowLabel',
        },
      },
    },

    // Copyright Configuration
    {
      name: 'copyright',
      type: 'group',
      fields: [
        {
          name: 'companyName',
          type: 'text',
          defaultValue: 'boersenblick.com',
          required: true,
          admin: {
            description: 'Company name for copyright',
          },
        },
        {
          name: 'customText',
          type: 'text',
          admin: {
            description:
              'Additional text after "All rights reserved" (optional)',
          },
        },
      ],
      admin: {
        description:
          'Copyright information (year is automatically current year)',
      },
    },
  ],
  hooks: {
    afterChange: [
      async ({ doc, req: { payload, context } }) => {
        if (!context?.disableRevalidate) {
          payload.logger.info('Revalidating footer');
          await invalidateGlobalCaches('footer');
        }
        return doc;
      },
    ],
  },
};
