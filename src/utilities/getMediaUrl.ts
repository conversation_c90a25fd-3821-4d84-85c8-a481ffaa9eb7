import { getClientSideURL } from '@/utilities/getURL';

/**
 * Processes media resource URL to ensure proper formatting with enhanced cache busting
 * @param url The original URL from the resource
 * @param cacheTag Optional cache tag to append to the URL
 * @returns Properly formatted URL with enhanced cache tag if provided
 */
export const getMediaUrl = (
  url: string | null | undefined,
  cacheTag?: string | null
): string => {
  if (!url) return '';

  // Enhanced cache busting: add timestamp if no cache tag provided
  let effectiveCacheTag = cacheTag;
  if (!effectiveCacheTag && url.includes('/media/')) {
    // Add current timestamp for better cache busting on media files
    effectiveCacheTag = `v=${Date.now()}`;
  }

  // Check if U<PERSON> already has http/https protocol
  if (url.startsWith('http://') || url.startsWith('https://')) {
    if (effectiveCacheTag) {
      const separator = url.includes('?') ? '&' : '?';
      return `${url}${separator}${effectiveCacheTag}`;
    }
    return url;
  }

  // Otherwise prepend client-side URL
  const baseUrl = getClientSideURL();
  if (effectiveCacheTag) {
    const separator = url.includes('?') ? '&' : '?';
    return `${baseUrl}${url}${separator}${effectiveCacheTag}`;
  }
  return `${baseUrl}${url}`;
};
