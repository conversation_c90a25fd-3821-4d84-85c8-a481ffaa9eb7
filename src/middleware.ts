import { NextRequest, NextResponse } from 'next/server';

// Simple in-memory rate limiting (use Redis in production for scale)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Security configuration - reasonable limits for a news website
// Development mode gets much more generous limits to support active development
const RATE_LIMITS = {
  public: {
    requests: process.env.NODE_ENV === 'development' ? 5000 : 1000,
    windowMs: 60 * 60 * 1000,
  },
  protected: {
    requests: process.env.NODE_ENV === 'development' ? 2000 : 200,
    windowMs: 60 * 60 * 1000,
  },
  admin: {
    requests: process.env.NODE_ENV === 'development' ? 10000 : 2000,
    windowMs: 60 * 60 * 1000,
  },
  health: {
    requests: process.env.NODE_ENV === 'development' ? 2000 : 500,
    windowMs: 60 * 60 * 1000,
  },
};

// Public routes that don't require authentication
const PUBLIC_ROUTES = [
  '/api/health', // System monitoring for uptime checks
  '/api/firecrawl-alerts', // Service monitoring (read-only)
  // PayloadCMS routes have their own auth
  '/api/graphql',
  '/api/preview',
  '/api/disable-draft',
];

// Utility functions
function getClientIP(request: NextRequest): string {
  return (
    request.headers.get('x-forwarded-for')?.split(',')[0] ||
    request.headers.get('x-real-ip') ||
    'unknown'
  );
}

function isAuthenticated(request: NextRequest): boolean {
  // Check for PayloadCMS authentication token
  const token = request.cookies.get('payload-token')?.value;

  // Debug logging to help troubleshoot authentication issues
  if (process.env.NODE_ENV === 'development') {
    console.log(
      `🔐 [Auth Debug] Token present: ${!!token}, Path: ${request.nextUrl.pathname}`
    );
  }

  return !!token;
}

function isPublicRoute(pathname: string): boolean {
  return PUBLIC_ROUTES.some(
    route => pathname === route || pathname.startsWith(route + '/')
  );
}

function checkRateLimit(
  key: string,
  limit: { requests: number; windowMs: number }
): boolean {
  const now = Date.now();
  const record = rateLimitStore.get(key);

  if (!record || now > record.resetTime) {
    // Reset or create new record
    rateLimitStore.set(key, {
      count: 1,
      resetTime: now + limit.windowMs,
    });
    return true;
  }

  if (record.count >= limit.requests) {
    return false; // Rate limit exceeded
  }

  record.count++;
  return true;
}

// Clean up old rate limit entries periodically
setInterval(
  () => {
    const now = Date.now();
    for (const [key, record] of rateLimitStore.entries()) {
      if (now > record.resetTime) {
        rateLimitStore.delete(key);
      }
    }
  },
  15 * 60 * 1000
); // Clean up every 15 minutes

// Export function to clear rate limits during development
export function clearRateLimits(clientIP?: string) {
  if (process.env.NODE_ENV === 'development') {
    if (clientIP) {
      // Clear rate limits for specific IP
      for (const [key] of rateLimitStore.entries()) {
        if (key.includes(clientIP)) {
          rateLimitStore.delete(key);
        }
      }
      console.log(`🧹 Cleared rate limits for IP: ${clientIP}`);
    } else {
      // Clear all rate limits
      rateLimitStore.clear();
      console.log('🧹 Cleared all rate limits');
    }
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;
  const clientIP = getClientIP(request);
  const now = new Date().toISOString();

  // Rate limiting logic
  let rateLimitKey: string;
  let rateLimit: { requests: number; windowMs: number };
  const authenticated = isAuthenticated(request);

  if (pathname.startsWith('/api/health')) {
    rateLimitKey = `health_${clientIP}`;
    rateLimit = RATE_LIMITS.health;
  } else if (isPublicRoute(pathname)) {
    rateLimitKey = `public_${clientIP}`;
    rateLimit = RATE_LIMITS.public;
  } else if (
    authenticated &&
    (pathname.startsWith('/admin') || pathname.startsWith('/api/'))
  ) {
    // Authenticated admin users get generous limits for admin panel and API usage
    rateLimitKey = `admin_${clientIP}`;
    rateLimit = RATE_LIMITS.admin;
  } else {
    rateLimitKey = `protected_${clientIP}`;
    rateLimit = RATE_LIMITS.protected;
  }

  // Check rate limit
  if (!checkRateLimit(rateLimitKey, rateLimit)) {
    const userType = authenticated ? 'authenticated admin' : 'unauthenticated';
    const isAdminRoute =
      pathname.startsWith('/admin') || pathname.startsWith('/api/');
    const actualCategory = rateLimitKey.split('_')[0];

    console.warn(
      `Rate limit exceeded for ${userType} user ${clientIP} on ${pathname} at ${now} (limit: ${rateLimit.requests}/hour, category: ${actualCategory})`
    );

    // In development, provide more helpful debugging information
    if (process.env.NODE_ENV === 'development') {
      console.warn(
        `🚨 [Rate Limit Debug] Authentication: ${authenticated}, Admin route: ${isAdminRoute}, Category: ${actualCategory}`
      );
      console.warn(
        `🚨 [Rate Limit Debug] Expected admin category but got: ${actualCategory}`
      );
    }

    return new NextResponse(
      authenticated
        ? `${actualCategory === 'admin' ? 'Admin' : 'User'} rate limit exceeded. You've made too many requests (${rateLimit.requests}/hour limit). Please wait before continuing your ${actualCategory === 'admin' ? 'admin' : ''} work.`
        : 'Too Many Requests',
      {
        status: 429,
        headers: {
          'Retry-After': '3600', // 1 hour
          'X-RateLimit-Limit': rateLimit.requests.toString(),
          'X-RateLimit-Remaining': '0',
          'X-RateLimit-Reset': (Date.now() + rateLimit.windowMs).toString(),
          'X-RateLimit-Category': actualCategory,
        },
      }
    );
  }

  // Basic security logging only for admin routes (not every access)
  if (pathname.startsWith('/admin') && !pathname.includes('/api/')) {
    const authStatus = authenticated ? 'authenticated' : 'unauthenticated';
    console.log(
      `Admin panel access: ${authStatus} user ${clientIP} -> ${pathname} at ${now}`
    );
  }

  // Enhanced security for preview routes (existing functionality)
  if (pathname.startsWith('/api/preview')) {
    console.log(`Preview access attempt from ${clientIP} at ${now}`);

    const response = NextResponse.next();
    response.headers.set('X-Robots-Tag', 'noindex, nofollow');
    response.headers.set(
      'Cache-Control',
      'no-cache, no-store, must-revalidate'
    );

    return response;
  }

  // Create response with security enhancements
  const response = NextResponse.next();

  // Add rate limit headers
  const record = rateLimitStore.get(rateLimitKey);
  const remaining = record
    ? Math.max(0, rateLimit.requests - record.count)
    : rateLimit.requests - 1;

  response.headers.set('X-RateLimit-Limit', rateLimit.requests.toString());
  response.headers.set('X-RateLimit-Remaining', remaining.toString());
  response.headers.set('X-RateLimit-Category', rateLimitKey.split('_')[0]); // admin, public, protected, health
  if (record) {
    response.headers.set('X-RateLimit-Reset', record.resetTime.toString());
  }

  // CSP is handled by next.config.js - no need for nonce complexity in middleware

  // Additional security for API routes
  if (pathname.startsWith('/api/')) {
    // Prevent caching of API responses
    response.headers.set(
      'Cache-Control',
      'no-cache, no-store, must-revalidate'
    );
    response.headers.set('Pragma', 'no-cache');
    response.headers.set('Expires', '0');

    // Security headers for API
    response.headers.set('X-Content-Type-Options', 'nosniff');
    response.headers.set('X-Frame-Options', 'DENY');
  }

  return response;
}

export const config = {
  matcher: [
    // Apply to all routes except static files
    '/((?!_next/static|_next/image|favicon.ico|robots.txt|sitemap.xml).*)',
  ],
};
