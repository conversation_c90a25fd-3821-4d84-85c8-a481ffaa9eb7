{"npm.packageManager": "pnpm", "editor.defaultFormatter": "esbenp.prettier-vscode", "prettier.enable": true, "editor.formatOnSave": true, "editor.formatOnSaveMode": "file", "editor.insertSpaces": true, "editor.tabSize": 2, "[typescript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[typescriptreact]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[javascript]": {"editor.defaultFormatter": "esbenp.prettier-vscode", "editor.codeActionsOnSave": {"source.fixAll.eslint": "explicit"}}, "[json]": {"editor.defaultFormatter": "esbenp.prettier-vscode"}, "typescript.tsdk": "node_modules/typescript/lib", "cSpell.words": ["payloadcms", "b<PERSON><PERSON><PERSON><PERSON>"], "css.validate": true, "css.lint.unknownAtRules": "ignore", "scss.validate": true, "scss.lint.unknownAtRules": "ignore", "less.validate": true, "less.lint.unknownAtRules": "ignore", "tailwindCSS.experimental.configFile": {"css": "src/app/(frontend)/global.css"}, "tailwindCSS.includeLanguages": {"plaintext": "css"}, "workbench.colorCustomizations": {"activityBar.activeBackground": "#1A1A1A", "activityBar.background": "#121212", "activityBar.foreground": "#CAA870", "activityBar.inactiveForeground": "#CAA87099", "activityBarBadge.background": "#B08D57", "activityBarBadge.foreground": "#121212", "commandCenter.border": "#CAA87080", "sash.hoverBorder": "#B08D57", "statusBar.background": "#121212", "statusBar.foreground": "#CAA870", "statusBarItem.hoverBackground": "#1A1A1A", "statusBarItem.remoteBackground": "#1A1A1A", "statusBarItem.remoteForeground": "#CAA870", "titleBar.activeBackground": "#121212", "titleBar.activeForeground": "#CAA870", "titleBar.inactiveBackground": "#12121299", "titleBar.inactiveForeground": "#CAA87099"}}