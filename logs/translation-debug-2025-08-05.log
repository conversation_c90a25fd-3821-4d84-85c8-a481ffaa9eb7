2025-08-05T12:04:56.829Z - ⏱️ TIMING DEBUG: Starting enhanced database freshness validation... 
2025-08-05T12:04:56.829Z - ⏱️ TIMING DEBUG: Translation triggered at: 2025-08-05T12:04:56.829Z 
2025-08-05T12:04:56.829Z - ⏱️ TIMING DEBUG: Will retry up to 5 times with increasing delays if document is stale 
2025-08-05T12:04:56.829Z - ⏱️ TIMING DEBUG: Initial attempt - waiting 1000ms for database consistency... 
2025-08-05T12:04:57.830Z - 📊 DATABASE FETCH: Starting document retrieval... 
2025-08-05T12:04:57.830Z - 📊 DATABASE FETCH: Collection: articles 
2025-08-05T12:04:57.830Z - 📊 DATABASE FETCH: ID: 153 
2025-08-05T12:04:57.830Z - 📊 DATABASE FETCH: Attempt: 1/5 
2025-08-05T12:04:57.830Z - 📊 DATABASE FETCH: Time: 2025-08-05T12:04:57.830Z 
2025-08-05T12:04:57.842Z - 📊 DATABASE FETCH: Document retrieved successfully 
2025-08-05T12:04:57.843Z - 📊 DATABASE FETCH: Document updatedAt: 2025-07-28T11:55:55.540Z 
2025-08-05T12:04:57.843Z - 📊 DATABASE FETCH: Fetch completed at: 2025-08-05T12:04:57.843Z 
2025-08-05T12:04:57.843Z - 📊 FRESHNESS CHECK: Document age: 11529 minutes 
2025-08-05T12:04:57.843Z - 📊 FRESHNESS CHECK: Document updated: 2025-07-28T11:55:55.540Z 
2025-08-05T12:04:57.843Z - 📊 FRESHNESS CHECK: Current time: 2025-08-05T12:04:57.843Z 
2025-08-05T12:04:57.843Z - ⚠️ FRESHNESS CHECK: Document is stale (11529 minutes old) - retrying... 
2025-08-05T12:04:57.843Z - ⏱️ TIMING DEBUG: Retry 1/5 - waiting 2000ms... 
2025-08-05T12:04:59.845Z - 📊 DATABASE FETCH: Starting document retrieval... 
2025-08-05T12:04:59.845Z - 📊 DATABASE FETCH: Collection: articles 
2025-08-05T12:04:59.845Z - 📊 DATABASE FETCH: ID: 153 
2025-08-05T12:04:59.845Z - 📊 DATABASE FETCH: Attempt: 2/5 
2025-08-05T12:04:59.846Z - 📊 DATABASE FETCH: Time: 2025-08-05T12:04:59.846Z 
2025-08-05T12:04:59.854Z - 📊 DATABASE FETCH: Document retrieved successfully 
2025-08-05T12:04:59.854Z - 📊 DATABASE FETCH: Document updatedAt: 2025-07-28T11:55:55.540Z 
2025-08-05T12:04:59.854Z - 📊 DATABASE FETCH: Fetch completed at: 2025-08-05T12:04:59.854Z 
2025-08-05T12:04:59.854Z - 📊 FRESHNESS CHECK: Document age: 11529 minutes 
2025-08-05T12:04:59.854Z - 📊 FRESHNESS CHECK: Document updated: 2025-07-28T11:55:55.540Z 
2025-08-05T12:04:59.855Z - 📊 FRESHNESS CHECK: Current time: 2025-08-05T12:04:59.854Z 
2025-08-05T12:04:59.855Z - ⚠️ FRESHNESS CHECK: Document is stale (11529 minutes old) - retrying... 
2025-08-05T12:04:59.855Z - ⏱️ TIMING DEBUG: Retry 2/5 - waiting 3000ms... 
2025-08-05T12:05:02.856Z - 📊 DATABASE FETCH: Starting document retrieval... 
2025-08-05T12:05:02.857Z - 📊 DATABASE FETCH: Collection: articles 
2025-08-05T12:05:02.857Z - 📊 DATABASE FETCH: ID: 153 
2025-08-05T12:05:02.857Z - 📊 DATABASE FETCH: Attempt: 3/5 
2025-08-05T12:05:02.857Z - 📊 DATABASE FETCH: Time: 2025-08-05T12:05:02.857Z 
2025-08-05T12:05:02.867Z - 📊 DATABASE FETCH: Document retrieved successfully 
2025-08-05T12:05:02.867Z - 📊 DATABASE FETCH: Document updatedAt: 2025-07-28T11:55:55.540Z 
2025-08-05T12:05:02.867Z - 📊 DATABASE FETCH: Fetch completed at: 2025-08-05T12:05:02.867Z 
2025-08-05T12:05:02.867Z - 📊 FRESHNESS CHECK: Document age: 11529 minutes 
2025-08-05T12:05:02.867Z - 📊 FRESHNESS CHECK: Document updated: 2025-07-28T11:55:55.540Z 
2025-08-05T12:05:02.868Z - 📊 FRESHNESS CHECK: Current time: 2025-08-05T12:05:02.867Z 
2025-08-05T12:05:02.868Z - ⚠️ FRESHNESS CHECK: Document is stale (11529 minutes old) - retrying... 
2025-08-05T12:05:02.868Z - ⏱️ TIMING DEBUG: Retry 3/5 - waiting 4000ms... 
2025-08-05T12:05:06.870Z - 📊 DATABASE FETCH: Starting document retrieval... 
2025-08-05T12:05:06.870Z - 📊 DATABASE FETCH: Collection: articles 
2025-08-05T12:05:06.871Z - 📊 DATABASE FETCH: ID: 153 
2025-08-05T12:05:06.872Z - 📊 DATABASE FETCH: Attempt: 4/5 
2025-08-05T12:05:06.872Z - 📊 DATABASE FETCH: Time: 2025-08-05T12:05:06.872Z 
2025-08-05T12:05:06.889Z - 📊 DATABASE FETCH: Document retrieved successfully 
2025-08-05T12:05:06.889Z - 📊 DATABASE FETCH: Document updatedAt: 2025-07-28T11:55:55.540Z 
2025-08-05T12:05:06.889Z - 📊 DATABASE FETCH: Fetch completed at: 2025-08-05T12:05:06.889Z 
2025-08-05T12:05:06.889Z - 📊 FRESHNESS CHECK: Document age: 11529 minutes 
2025-08-05T12:05:06.889Z - 📊 FRESHNESS CHECK: Document updated: 2025-07-28T11:55:55.540Z 
2025-08-05T12:05:06.890Z - 📊 FRESHNESS CHECK: Current time: 2025-08-05T12:05:06.889Z 
2025-08-05T12:05:06.890Z - ⚠️ FRESHNESS CHECK: Document is stale (11529 minutes old) - retrying... 
2025-08-05T12:05:06.890Z - ⏱️ TIMING DEBUG: Retry 4/5 - waiting 5000ms... 
2025-08-05T12:05:11.891Z - 📊 DATABASE FETCH: Starting document retrieval... 
2025-08-05T12:05:11.891Z - 📊 DATABASE FETCH: Collection: articles 
2025-08-05T12:05:11.892Z - 📊 DATABASE FETCH: ID: 153 
2025-08-05T12:05:11.892Z - 📊 DATABASE FETCH: Attempt: 5/5 
2025-08-05T12:05:11.892Z - 📊 DATABASE FETCH: Time: 2025-08-05T12:05:11.892Z 
2025-08-05T12:05:11.905Z - 📊 DATABASE FETCH: Document retrieved successfully 
2025-08-05T12:05:11.905Z - 📊 DATABASE FETCH: Document updatedAt: 2025-07-28T11:55:55.540Z 
2025-08-05T12:05:11.905Z - 📊 DATABASE FETCH: Fetch completed at: 2025-08-05T12:05:11.905Z 
2025-08-05T12:05:11.905Z - 📊 FRESHNESS CHECK: Document age: 11529 minutes 
2025-08-05T12:05:11.905Z - 📊 FRESHNESS CHECK: Document updated: 2025-07-28T11:55:55.540Z 
2025-08-05T12:05:11.905Z - 📊 FRESHNESS CHECK: Current time: 2025-08-05T12:05:11.905Z 
2025-08-05T12:05:11.905Z - ⚠️ FRESHNESS CHECK: Document is stale (11529 minutes old) - retrying... 
2025-08-05T12:05:11.905Z - ❌ TIMEOUT: Could not get fresh document after 5 attempts (15076ms total) 
2025-08-05T12:13:41.718Z - ⏱️ TIMING DEBUG: Starting enhanced database freshness validation... 
2025-08-05T12:13:41.718Z - ⏱️ TIMING DEBUG: Translation triggered at: 2025-08-05T12:13:41.718Z 
2025-08-05T12:13:41.718Z - ⏱️ TIMING DEBUG: Will retry up to 5 times with increasing delays if document is stale 
2025-08-05T12:13:41.718Z - ⏱️ TIMING DEBUG: Initial attempt - waiting 1000ms for database consistency... 
2025-08-05T12:13:42.719Z - 📊 DATABASE FETCH: Starting document retrieval... 
2025-08-05T12:13:42.719Z - 📊 DATABASE FETCH: Collection: articles 
2025-08-05T12:13:42.719Z - 📊 DATABASE FETCH: ID: 268 
2025-08-05T12:13:42.719Z - 📊 DATABASE FETCH: Attempt: 1/5 
2025-08-05T12:13:42.719Z - 📊 DATABASE FETCH: Time: 2025-08-05T12:13:42.719Z 
2025-08-05T12:13:42.731Z - 📊 DATABASE FETCH: Document retrieved successfully 
2025-08-05T12:13:42.731Z - 📊 DATABASE FETCH: Document updatedAt: 2025-08-05T12:13:20.599Z 
2025-08-05T12:13:42.731Z - 📊 DATABASE FETCH: Fetch completed at: 2025-08-05T12:13:42.731Z 
2025-08-05T12:13:42.732Z - 📊 FRESHNESS CHECK: Document age: 0 minutes (0h 0d) 
2025-08-05T12:13:42.732Z - 📊 FRESHNESS CHECK: Document updated: 2025-08-05T12:13:20.599Z 
2025-08-05T12:13:42.732Z - 📊 FRESHNESS CHECK: Current time: 2025-08-05T12:13:42.731Z 
2025-08-05T12:13:42.732Z - ✅ FRESHNESS CHECK: Document is recent (within 24h) - proceeding with translation 
2025-08-05T12:13:52.035Z - 🚨 COMPREHENSIVE FIELD DEBUG: Starting complete document analysis... 
2025-08-05T12:13:52.035Z - 🔍 CRITICAL FIELDS STATUS: 
2025-08-05T12:13:52.036Z -   🔹 featuredImage: 
2025-08-05T12:13:52.036Z -      - Exists: true 
2025-08-05T12:13:52.036Z -      - Type: object 
2025-08-05T12:13:52.036Z -      - Value: {"id":16,"alt":"City Scape in Germany","updatedAt":"2025-07-28T14:14:35.413Z","createdAt":"2025-07-28T14:14:34.263Z","url":"/api/media/file/0_3_640_N-3.webp","thumbnailURL":"/api/media/file/0_3_640_N-3-400x225.webp","filename":"0_3_640_N-3.webp","mimeType":"image/webp","filesize":77640,"width":640,"height":358,"focalX":null,"focalY":null,"sizes":{"thumbnail":{"url":"/api/media/file/0_3_640_N-3-400x225.webp","width":400,"height":225,"mimeType":"image/webp","filesize":30656,"filename":"0_3_640_N-3-400x225.webp"},"horizontal":{"url":"/api/media/file/0_3_640_N-3-288x288.webp","width":288,"height":288,"mimeType":"image/webp","filesize":28648,"filename":"0_3_640_N-3-288x288.webp"},"card":{"url":null,"width":null,"height":null,"mimeType":null,"filesize":null,"filename":null},"social":{"url":null,"width":null,"height":null,"mimeType":null,"filesize":null,"filename":null},"hero":{"url":null,"width":null,"height":null,"mimeType":null,"filesize":null,"filename":null}}} 
2025-08-05T12:13:52.036Z -   🔹 categories: 
2025-08-05T12:13:52.036Z -      - Exists: true 
2025-08-05T12:13:52.036Z -      - Type: object 
2025-08-05T12:13:52.036Z -      - Value: [{"id":1,"title":"International","english":"International","slug":"international","slugLock":true,"updatedAt":"2025-07-24T10:27:30.310Z","createdAt":"2025-07-24T10:27:30.310Z"}] 
2025-08-05T12:13:52.036Z -      - Length: 1 
2025-08-05T12:13:52.036Z -   🔹 placement: 
2025-08-05T12:13:52.036Z -      - Exists: true 
2025-08-05T12:13:52.036Z -      - Type: string 
2025-08-05T12:13:52.036Z -      - Value: "tier-1" 
2025-08-05T12:13:52.036Z -   🔹 featured: 
2025-08-05T12:13:52.036Z -      - Exists: true 
2025-08-05T12:13:52.037Z -      - Type: boolean 
2025-08-05T12:13:52.037Z -      - Value: true 
2025-08-05T12:13:52.037Z -   🔹 pinned: 
2025-08-05T12:13:52.037Z -      - Exists: true 
2025-08-05T12:13:52.037Z -      - Type: boolean 
2025-08-05T12:13:52.037Z -      - Value: true 
2025-08-05T12:13:52.037Z -   🔹 trending: 
2025-08-05T12:13:52.037Z -      - Exists: true 
2025-08-05T12:13:52.037Z -      - Type: boolean 
2025-08-05T12:13:52.037Z -      - Value: true 
2025-08-05T12:13:52.037Z -   🔹 relatedCompanies: 
2025-08-05T12:13:52.037Z -      - Exists: true 
2025-08-05T12:13:52.037Z -      - Type: object 
2025-08-05T12:13:52.037Z -      - Value: [{"id":"6891f555e76386bea8423e1f","name":"test","ticker":"test","exchange":"test","relevance":"medium","confidence":100,"featured":false}] 
2025-08-05T12:13:52.037Z -      - Length: 1 
2025-08-05T12:13:52.037Z -   🔹 publishedBy: 
2025-08-05T12:13:52.037Z -      - Exists: true 
2025-08-05T12:13:52.037Z -      - Type: object 
2025-08-05T12:13:52.037Z -      - Value: null 
2025-08-05T12:13:52.037Z -   🔹 publishedAt: 
2025-08-05T12:13:52.037Z -      - Exists: true 
2025-08-05T12:13:52.038Z -      - Type: object 
2025-08-05T12:13:52.038Z -      - Value: null 
2025-08-05T12:13:52.038Z - 🔍 ALL DOCUMENT FIELDS: 
2025-08-05T12:13:52.038Z -    Total fields: 25 
2025-08-05T12:13:52.038Z -    Fields: id, title, slug, featuredImage, articleType, workflowStage, categories, placement, featured, pinned, trending, readTimeMinutes, relatedCompanies, publishedBy, publishedAt, hasBeenEnhanced, hasGermanTranslation, hasOriginalSource, englishTab, sourcesTab, germanTab, meta, updatedAt, createdAt, _status 
2025-08-05T12:13:52.038Z - 🔍 DOCUMENT INFO: 
2025-08-05T12:13:52.038Z -    - ID: 268 
2025-08-05T12:13:52.038Z -    - Title: Sample Article - The joys of debugging timing issue 
2025-08-05T12:13:52.038Z -    - Updated At: 2025-08-05T12:13:20.599Z 
2025-08-05T12:13:52.038Z -    - Created At: 2025-08-05T12:13:20.536Z 
2025-08-05T12:13:52.038Z - ✅ FIELD PRESERVATION: All critical fields processed and preserved 
