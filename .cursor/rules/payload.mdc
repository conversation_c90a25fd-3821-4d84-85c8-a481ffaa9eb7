## PayloadCMS Framework Integration Principles

### Core Principle: Work WITH Frameworks, Not Around Them

**CRITICAL**: Always work within PayloadCMS patterns. Never create custom functions to bypass type errors or framework patterns. When encountering TypeScript errors or framework resistance, the solution is to understand the framework better, not to create workarounds.

```typescript
// ✅ Correct - Use PayloadCMS native patterns
import { getPayload } from 'payload';
import config from '@payload-config';
import type { Header } from '../../../payload-types';

export default async function HeaderNavigation() {
  const payload = await getPayload({ config });
  const header: Header = await payload.findGlobal({
    slug: 'header',
    depth: 1,
  });
  
  return <NavigationComponent items={header.navItems} />;
}

// ❌ Wrong - Custom bypass functions
const getHeader = getCachedGlobal('header', 1); // Custom wrapper
const header = await getHeader() as any; // Type casting
```

### PayloadCMS Local API Patterns

```typescript
// Initialize PayloadCMS instance
import { getPayload } from 'payload';
import config from '@payload-config';

const payload = await getPayload({ config });

// Collections operations
const posts = await payload.find({
  collection: 'posts',
  depth: 2,
  limit: 10,
  where: {
    status: { equals: 'published' }
  }
});

const post = await payload.findByID({
  collection: 'posts',
  id: '123',
  depth: 1
});

// Globals operations  
const header = await payload.findGlobal({
  slug: 'header',
  depth: 1
});

const updatedHeader = await payload.updateGlobal({
  slug: 'header',
  data: {
    navItems: [...]
  }
});
```

### React Server Component Integration

```typescript
// Server Component data fetching
import { getPayload } from 'payload';
import config from '@payload-config';

export default async function Page() {
  const payload = await getPayload({ config });
  
  const page = await payload.findByID({
    collection: 'pages',
    id: '123',
    draft: true, // Include drafts if needed
  });

  return <PageContent page={page} />;
}

// With proper error handling
export default async function Page({ params }: { params: { slug: string } }) {
  try {
    const payload = await getPayload({ config });
    
    const pages = await payload.find({
      collection: 'pages',
      where: {
        slug: { equals: params.slug }
      },
      limit: 1
    });

    if (!pages.docs.length) {
      notFound();
    }

    return <PageContent page={pages.docs[0]} />;
  } catch (error) {
    console.error('Failed to fetch page:', error);
    throw error;
  }
}
```

### Caching Strategies

```typescript
import { unstable_cache } from 'next/cache';
import { getPayload } from 'payload';
import config from '@payload-config';

// Cached global data fetching
async function getGlobal(slug: string, depth = 0) {
  const payload = await getPayload({ config });
  return await payload.findGlobal({ slug, depth });
}

export const getCachedGlobal = (slug: string, depth = 0) =>
  unstable_cache(
    async () => getGlobal(slug, depth),
    [slug],
    {
      tags: [`global_${slug}`],
      revalidate: 3600 // 1 hour
    }
  );

// Usage in components
export default async function Navigation() {
  const getHeader = getCachedGlobal('header', 1);
  const header = await getHeader();
  
  return <NavigationClient items={header.navItems} />;
}
```

### Type Import and Usage Patterns

```typescript
// Correct type imports
import type { 
  Header, 
  Article, 
  Config,
  CollectionBeforeChangeHook,
  GlobalAfterChangeHook 
} from '../../../payload-types';

// Proper hook typing
const beforeChangeHook: CollectionBeforeChangeHook<'articles'> = async ({
  data,
  operation,
  req
}) => {
  // TypeScript knows data is Article type
  if (data.workflowStage === 'published') {
    // Type-safe operations
  }
  return data;
};

// Global hook typing
const afterChangeHook: GlobalAfterChangeHook<'header'> = async ({
  doc,
  previousDoc,
  req
}) => {
  // TypeScript knows doc is Header type
  return doc;
};
```

### Client-Side Hook Patterns

```typescript
'use client';
import { usePayloadAPI } from '@payloadcms/ui';

// Fetch data reactively
export function ArticleList() {
  const [{ data, isError, isLoading }, { setParams }] = usePayloadAPI(
    '/api/articles',
    {
      initialParams: { 
        depth: 1,
        limit: 10 
      },
    }
  );

  if (isLoading) return <LoadingSpinner />;
  if (isError) return <ErrorMessage />;

  return (
    <div>
      {data.docs.map(article => (
        <ArticleCard key={article.id} article={article} />
      ))}
      <button onClick={() => setParams({ cacheBust: Date.now() })}>
        Refresh
      </button>
    </div>
  );
}

// Access current document info
import { useDocumentInfo } from '@payloadcms/ui';

export function DocumentTitle() {
  const { title, collectionSlug, globalSlug } = useDocumentInfo();
  
  return <h1>{title} ({collectionSlug || globalSlug})</h1>;
}
```

### Admin UI Component Integration

```typescript
// Custom admin components with PayloadCMS UI
import { useConfig } from '@payloadcms/ui';

export function CustomAdminComponent() {
  const { getEntityConfig } = useConfig();
  const articleConfig = getEntityConfig({ collectionSlug: 'articles' });
  
  return (
    <div>
      <p>Articles collection has {articleConfig.fields.length} fields</p>
    </div>
  );
}

// Global admin component configuration
import type { SanitizedGlobalConfig } from 'payload';

export const MyGlobal: SanitizedGlobalConfig = {
  slug: 'header',
  fields: [...],
  admin: {
    components: {
      views: {
        edit: {
          default: {
            Component: '/path/to/CustomGlobalEditView'
          }
        }
      }
    }
  }
};
```

### Field Configuration Patterns

```typescript
// PayloadCMS conditional field patterns
{
  name: 'publishedAt',
  type: 'date',
  admin: {
    condition: (data, siblingData) => {
      // Use PayloadCMS condition patterns
      return data?.workflowStage === 'published'
    },
    date: {
      pickerAppearance: 'dayAndTime'
    }
  }
},
{
  name: 'internalNotes',
  type: 'textarea',
  // PayloadCMS native access control
  access: {
    update: () => false // Prevent updates via access control
  }
}
```

## PayloadCMS Best Practices for BörsenBlick

### When to Use PayloadCMS Native vs Custom Solutions

**Use PayloadCMS Native Features For:**
- Rich text editing (Lexical editor with features)
- Field validation and admin configuration
- Access control and user management
- Collection and field hooks
- Type generation and API endpoints
- Admin UI customization

**Use Custom Solutions For:**
- External service integrations (Firecrawl, OpenAI)
- Complex business logic that spans multiple collections
- Custom API endpoints for specific workflows
- External data processing pipelines

### PayloadCMS Integration Patterns

```typescript
// Integrate external services with PayloadCMS hooks
const beforeChangeHook: CollectionBeforeChangeHook = async ({
  data,
  operation,
  req
}) => {
  // Use PayloadCMS patterns for external service integration
  if (operation === 'update' && data.workflowStage === 'enhanced') {
    try {
      // External service call
      const enhancementResult = await enhanceContent(data.originalContent)

      // Use PayloadCMS native HTML to Lexical conversion
      data.enhancedContent = await convertHTMLToLexical({
        editorConfig: await editorConfigFactory.default({ config }),
        html: enhancementResult.html,
        JSDOM
      })
    } catch (error) {
      // PayloadCMS error handling patterns
      req.payload.logger.error('Enhancement failed:', error)
      throw new Error('Content enhancement failed')
    }
  }

  return data
}
```

## AI Reasoning for BörsenBlick Development

When developing with PayloadCMS for BörsenBlick, consider:

- **PayloadCMS Native First**: Always check if PayloadCMS has built-in functionality before creating custom solutions

- **Content Processing Pipeline**: Design collections to support the Firecrawl → OpenAI → Lexical workflow using PayloadCMS native conversion utilities

- **Dual-Language Architecture**: Structure fields to support original German + enhanced German + English content using PayloadCMS conditional field patterns

- **Editorial Workflow**: Implement workflow states using PayloadCMS select fields and conditional visibility

- **External Service Integration**: Use PayloadCMS hooks for external service integration with proper error handling

- **Rich Text Consistency**: Use PayloadCMS native Lexical configuration patterns for consistent editing experience

- **Performance**: Leverage PayloadCMS caching and population control (depth, select) for efficient queries

- **Type Safety**: Use PayloadCMS generated types with proper hook argument types for end-to-end type safety

- **Admin Experience**: Leverage PayloadCMS admin configuration features for optimal editor experience

- **Error Recovery**: Use PayloadCMS error handling patterns with proper logging and user feedback

- **Content Quality**: Integrate AI enhancement workflows using PayloadCMS hooks and native content conversion utilities

## Package Management & Dependencies

### Core Principle: Always Use Package Managers

**NEVER manually edit package configuration files.** Always use appropriate package managers:

```bash
# ✅ Correct - Use package managers
pnpm add @payloadcms/richtext-lexical
pnpm add -D @types/node
pnpm remove unused-package

# ❌ Wrong - Never manually edit package.json
# Editing package.json directly can lead to:
# - Version conflicts
# - Missing transitive dependencies
# - Broken lock files
# - Inconsistent environments
```

### BörsenBlick Package Management

```bash
# Essential commands for this project
pnpm dev                    # Start development server
pnpm build                  # Build for production
pnpm generate:types         # Generate PayloadCMS types (run after schema changes)
pnpm type-check            # Run TypeScript checks
pnpm lint                  # Run Biome linting
pnpm format                # Format code with Biome

# After significant PayloadCMS changes
rm -rf .next               # Clear Next.js build cache
pnpm dev                   # Restart development server
```

### Dependency Categories

```typescript
// Core PayloadCMS dependencies
"@payloadcms/db-postgres"     // Database adapter
"@payloadcms/richtext-lexical" // Rich text editor
"@payloadcms/ui"              // Admin UI components

// External service integrations
"@mendable/firecrawl-js"      // Content scraping
"openai"                      // AI enhancement
"rss-parser"                  // RSS feed processing

// Utilities and validation
"zod"                         // Runtime type checking
"@webiny/lexical-converter"   // Lexical format conversion
```

## Testing Patterns

### API Endpoint Testing

```typescript
// Test content processing pipeline
describe('Content Processing API', () => {
  it('should process RSS feeds successfully', async () => {
    const response = await fetch('/api/test-content-pipeline', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        urls: ['https://example.com/test-article']
      })
    });

    const result = await response.json();
    expect(result.results[0].success).toBe(true);
  });

  it('should handle Firecrawl errors gracefully', async () => {
    // Test error handling for external service failures
    const response = await fetch('/api/test-content-pipeline', {
      method: 'POST',
      body: JSON.stringify({ urls: ['invalid-url'] })
    });

    expect(response.status).toBe(500);
    const result = await response.json();
    expect(result.error).toBeDefined();
  });
});
```

### PayloadCMS Collection Testing

```typescript
import { getPayload } from 'payload';
import config from '@payload-config';

describe('Articles Collection', () => {
  let payload: any;

  beforeAll(async () => {
    payload = await getPayload({ config });
  });

  it('should create candidate article with proper workflow state', async () => {
    const article = await payload.create({
      collection: 'articles',
      data: {
        title: 'Test Article',
        workflowStage: 'candidate-article',
        articleType: 'ai-generated'
      }
    });

    expect(article.workflowStage).toBe('candidate-article');
    expect(article.articleType).toBe('ai-generated');
  });

  it('should auto-populate title from content tabs', async () => {
    const article = await payload.create({
      collection: 'articles',
      data: {
        // No title provided
        germanTab: {
          germanTitle: 'German Test Title'
        }
      }
    });

    expect(article.title).toBe('German Test Title');
  });
});
```

### External Service Integration Testing

```typescript
describe('Firecrawl Integration', () => {
  it('should scrape content with domain-specific config', async () => {
    const result = await enhancedFirecrawlClient.scrapeUrl(
      'https://finanzen.net/test-article',
      {
        formats: ['html'],
        excludeTags: ['#ov-instrument-chart--full-screen']
      }
    );

    expect(result.success).toBe(true);
    expect(result.data.html).toBeDefined();
  });
});

describe('OpenAI Integration', () => {
  it('should enhance German content and provide English translation', async () => {
    const result = await enhanceAndTranslateContent(
      'Test German content',
      ['keyword1', 'keyword2']
    );

    expect(result.success).toBe(true);
    expect(result.enhancedGerman).toBeDefined();
    expect(result.enhancedEnglish).toBeDefined();
  });
});
```
