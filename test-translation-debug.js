#!/usr/bin/env node

/**
 * Test Translation Debug Script
 *
 * This script tests the translation functionality with debug logging
 * to analyze field preservation issues.
 */

const config = require('./dist/payload.config.js').default;
const { getPayload } = require('payload');
const { TranslationHandler } = require('./dist/lib/api/translation-handler.js');

async function testTranslation() {
  try {
    console.log('🧪 Starting translation debug test...');

    // Initialize PayloadCMS
    const payload = await getPayload({ config });

    // Get an existing article for testing
    const articles = await payload.find({
      collection: 'articles',
      limit: 1,
      where: {
        'englishTab.enhancedTitle': {
          not_equals: null,
        },
        categories: {
          not_equals: null,
        },
      },
    });

    if (articles.docs.length === 0) {
      console.log('❌ No suitable articles found for testing');
      return;
    }

    const testArticle = articles.docs[0];
    console.log(
      `📄 Testing with article: ${testArticle.title || testArticle.id}`
    );
    console.log(`   Categories: ${testArticle.categories?.length || 0}`);
    console.log(`   Featured: ${testArticle.featured}`);
    console.log(`   Placement: ${testArticle.placement}`);

    // Create translation handler
    const handler = new TranslationHandler('articles');

    // Test translation with debug logging
    const result = await handler.handleTranslation({
      articleId: testArticle.id,
    });

    console.log('🎯 Translation test completed');
    console.log('Result:', result.status, result.headers.get('content-type'));

    // Get the response body
    const responseBody = await result.json();
    console.log('Response:', JSON.stringify(responseBody, null, 2));
  } catch (error) {
    console.error('❌ Test failed:', error);
  } finally {
    process.exit(0);
  }
}

testTranslation();
