# BörsenBlick

A sophisticated financial news platform that automatically processes German financial content into multi-language articles using AI enhancement. The system operates as an intelligent content pipeline from RSS feeds to publication-ready articles.

## 🏗️ Architecture Overview

**BörsenBlick** is an automated financial news processing system that:

- Fetches content from German financial RSS feeds
- Processes and enhances articles using AI
- Provides multi-language support (German/English)
- Offers a comprehensive content management system

### Core Workflow

```
RSS Feeds → Content Extraction → AI Enhancement and Translation to English → Article Creation → Translation to German → Publication
```

## 🛠️ Tech Stack

- **Frontend**: Next.js 15 (App Router), React 19, Tailwind CSS v4, ShadcnUI
- **Backend**: PayloadCMS 3.0 with PostgreSQL adapter
- **Database**: Supabase PostgreSQL with custom migrations
- **AI Processing**: OpenAI GPT-4 for content enhancement and translation
- **Web Scraping**: Firecrawl for intelligent content extraction
- **Testing**: Vitest for unit and integration tests
- **Linting/Formatting**: ESLint + Prettier

## 📋 Prerequisites

- **Node.js**: 18.20.2+ or 20.9.0+
- **pnpm**: Package manager (required)
- **PostgreSQL**: Database (Supabase recommended)
- **OpenAI API Key**: For AI content processing
- **Firecrawl API Key**: For web scraping

## 🚀 Getting Started

### 1. Clone and Install

```bash
git clone https://github.com/your-username/borsenblick.git
cd borsenblick
pnpm install
```

### 2. Environment Setup

```bash
cp .env.example .env
```

Required environment variables:

```env
# Database
DATABASE_URI=postgresql://username:password@host:port/database
PAYLOAD_SECRET=your-payload-secret-key

# AI Processing
OPENAI_API_KEY=your-openai-api-key

# Web Scraping
FIRECRAWL_API_KEY=your-firecrawl-api-key

# App Configuration
NEXT_PUBLIC_SERVER_URL=http://localhost:3000
```

### 3. Database Setup

```bash
# Run database migrations
pnpm migrate

# Create admin user (optional)
node scripts/create-admin-user.js
```

### 4. Development Server

```bash
pnpm dev
```

The application will be available at:

- **Frontend**: http://localhost:3000
- **Admin Panel**: http://localhost:3000/admin

### 5. Generate Types (After Schema Changes)

```bash
pnpm generate:types
```

## 📁 Project Structure

```
src/
├── app/                    # Next.js App Router
│   ├── (frontend)/        # Public-facing pages
│   ├── (payload)/         # PayloadCMS admin
│   └── api/               # API routes
├── collections/           # PayloadCMS collections (data models)
├── components/            # React components
│   ├── admin/            # Admin-specific components
│   └── ui/               # Reusable UI components
├── lib/                   # Core business logic
│   ├── integrations/     # External service integrations
│   ├── server/           # Server-side services
│   └── utils/            # Utility functions
├── fields/               # Custom PayloadCMS fields
├── hooks/                # React hooks
└── migrations/           # Database migrations
```

## 🔧 Key Collections (Data Models)

### Articles

The core content model with multi-tab structure:

- **English Tab**: AI-enhanced English content
- **German Tab**: German translations (conditional)
- **Sources Tab**: Original content preservation
- **SEO Tab**: Meta information and optimization

### Categories

Content organization and filtering system.

### Keywords

Financial terms used for content filtering and relevance scoring.

### ProcessedUrls

URL deduplication and processing tracking to prevent duplicate content.

### RSSFeeds

Source feed management with priority levels and processing configuration.

## 🤖 Content Processing Pipeline

### Phase 1: RSS Discovery

1. System fetches from prioritized RSS feeds
2. Parses articles and matches against financial keywords
3. Filters duplicates using ProcessedUrls collection
4. Performs language detection

### Phase 2: Content Extraction

1. **Enhanced Firecrawl Processing**: Site-specific configurations
2. **Intelligent Filtering**: Removes ads, navigation, charts
3. **Multi-format Extraction**: HTML, Markdown, JSON with fallbacks
4. **Quality Validation**: Ensures content meets standards

### Phase 3: AI Enhancement

1. **Content Enhancement**: Improves readability and SEO
2. **Company Extraction**: Identifies financial entities and tickers
3. **Keyword Generation**: Creates relevant financial keywords
4. **Translation**: German translation service when needed

### Phase 4: Article Management

1. **Workflow Stages**: candidate-article → enhanced → ready-for-review → published
2. **Multi-tab Structure**: Organized content presentation
3. **Company Relations**: Financial entities with relevance scoring

## 🔌 API Endpoints

### Content Processing

- `POST /api/run-content-pipeline` - Main production pipeline
- `POST /api/articles/enhance` - English content enhancement
- `POST /api/articles/translate` - German translation service
- `POST /api/load-content` - Populate database with RSS feeds

### Health & Monitoring

- `GET /api/health` - System health check
- `GET /api/openai-status` - OpenAI API monitoring
- `GET /api/firecrawl-alerts` - Firecrawl service monitoring

### Testing & Development

- `POST /api/test-*` - Various testing endpoints

## 🛠️ Development Commands

```bash
# Development
pnpm dev              # Start development server
pnpm devsafe          # Clean build and start dev server

# Building
pnpm build            # Build for production
pnpm start            # Start production server

# Code Quality
pnpm lint             # Run ESLint
pnpm lint:fix         # Fix ESLint issues
pnpm format           # Format code with Prettier
pnpm format:check     # Check code formatting
pnpm lint:all         # Run all linting checks
pnpm fix:all          # Fix all linting issues

# Database
pnpm migrate          # Run database migrations
pnpm migrate:create   # Create new migration
pnpm migrate:status   # Check migration status
pnpm migrate:fresh    # Reset and run all migrations

# PayloadCMS
pnpm generate:types   # Generate TypeScript types
pnpm generate:importmap  # Generate import map

# Monitoring
pnpm check:openai     # Check OpenAI API status
```

## 🧪 Testing

```bash
# Run all tests
pnpm test

# Run tests in watch mode
pnpm test:watch

# Run tests with coverage
pnpm test:coverage
```

## 📊 Monitoring & Debugging

### OpenAI Status Widget

- Real-time API monitoring in admin panel
- Quota alerts and usage tracking
- Error logging and recommendations

### Performance Monitoring

- Content processing metrics
- Cost tracking and optimization
- Error recovery mechanisms

### Health Checks

- System status endpoints
- Service availability monitoring
- Comprehensive error logging

## 🔒 Security & Best Practices

### Environment Variables

- Never commit API keys or secrets
- Use environment variable substitution
- Separate development and production configs

### Database Security

- Use migrations for schema changes
- Implement proper access controls
- Regular backup procedures

### Content Security

- Input validation and sanitization
- Rate limiting on API endpoints
- Comprehensive error handling

## 🚀 Deployment

### Vercel Deployment

```bash
# Install Vercel CLI
npm i -g vercel

# Deploy to Vercel
vercel --prod
```

### Environment Variables (Production)

Ensure all required environment variables are set in your deployment platform:

- Database connections
- API keys (OpenAI, Firecrawl)
- Application secrets

### Database Migrations

```bash
# Run migrations in production
pnpm migrate
```

## 🐛 Troubleshooting

### Common Issues

**Database Connection Issues**

```bash
# Check database connection
pnpm migrate:status
```

**OpenAI API Quota Exceeded**

- Check `/api/openai-status` endpoint
- Monitor usage in admin panel
- Consider upgrading OpenAI plan

**Content Processing Failures**

- Check Firecrawl API status
- Review error logs in admin panel
- Verify RSS feed accessibility

**TypeScript Errors**

```bash
# Regenerate types after schema changes
pnpm generate:types
```

## 📚 Additional Resources

### Documentation

- [PayloadCMS Documentation](https://payloadcms.com/docs)
- [Next.js Documentation](https://nextjs.org/docs)
- [Supabase Documentation](https://supabase.com/docs)

### Key Files

- `src/payload.config.ts` - PayloadCMS configuration
- `src/collections/Articles.ts` - Main article data model
- `src/lib/integrations/` - External service integrations
- `docs/` - Additional project documentation

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Run tests and linting
5. Submit a pull request

### Code Style

- Follow existing patterns and conventions
- Use TypeScript for type safety
- Write tests for new features
- Update documentation as needed

## 🆘 Support

For issues and questions:

1. Check the troubleshooting section
2. Review existing issues in the repository
3. Create a new issue with detailed information
4. Include error logs and reproduction steps

---

**Note**: This project is designed for processing German financial content and requires appropriate API keys and database setup for full functionality.
