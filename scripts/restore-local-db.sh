#!/bin/bash

# IMPROVED Local Database Restore Script
# Usage: ./restore-local-db-improved.sh [timestamp]
# This script ONLY affects your LOCAL development database

set -e

# Colors for clear output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m' # No Color

# Configuration
BACKUP_BASE_DIR="supabase/backups"
LOCAL_DB_URL="postgresql://postgres:postgres@127.0.0.1:54322/postgres"

# Safety header
echo -e "${BOLD}${BLUE}🏠 LOCAL Database Restore${NC}"
echo -e "${GREEN}✅ This ONLY affects your LOCAL development database${NC}"
echo -e "${GREEN}✅ Your PRODUCTION data is completely safe${NC}"
echo -e "${GREEN}✅ This will NOT touch any remote/production systems${NC}"
echo ""

# Function to validate migrations before applying
validate_migrations() {
    echo -e "${BLUE}🔍 Validating migrations...${NC}"
    
    # Check for common SQL syntax issues
    for migration_file in supabase/migrations/*.sql; do
        if [ -f "$migration_file" ]; then
            echo "  Checking $(basename "$migration_file")..."
            
            # Check for problematic patterns
            if grep -q "FOREIGN KEY using index" "$migration_file"; then
                echo -e "${RED}❌ Invalid syntax found in $migration_file${NC}"
                echo -e "${RED}   'FOREIGN KEY using index' is not valid PostgreSQL syntax${NC}"
                echo -e "${YELLOW}   Use proper FOREIGN KEY constraint syntax instead${NC}"
                return 1
            fi
        fi
    done
    
    echo -e "${GREEN}✅ All migrations look valid${NC}"
    return 0
}

# Function to show available backups
show_available_backups() {
    echo -e "${BLUE}📋 Available LOCAL restore options:${NC}"
    echo ""
    
    if [ ! -d "$BACKUP_BASE_DIR" ]; then
        echo -e "${RED}❌ No backup directory found at $BACKUP_BASE_DIR${NC}"
        return 1
    fi
    
    found_backups=false
    
    # Look for all backup types
    for backup_dir in $(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d \( -name "local_db_*" -o -name "remote_*" \) 2>/dev/null | sort -r); do
        if [ "$backup_dir" = "$BACKUP_BASE_DIR" ]; then
            continue
        fi
        
        found_backups=true
        backup_name=$(basename "$backup_dir")
        
        # Extract clean timestamp for user input
        if [[ "$backup_name" == local_db_* ]]; then
            timestamp=$(echo "$backup_name" | sed 's/local_db_//')
            backup_type="Local"
        elif [[ "$backup_name" == remote_* ]]; then
            timestamp=$(echo "$backup_name" | sed 's/.*_\([0-9]\{8\}_[0-9]\{6\}\)$/\1/')
            backup_type="Production"
        fi
        
        info_file="$backup_dir/${backup_name}.info"
        
        echo -e "${GREEN}🗄️  $timestamp${NC} ${YELLOW}($backup_type backup)${NC}"
        
        if [ -f "$info_file" ]; then
            # Show creation date and branch info
            created_date=$(grep "Created:" "$info_file" 2>/dev/null | cut -d: -f2- | xargs || echo "Unknown")
            branch_info=$(grep "Branch:" "$info_file" 2>/dev/null | cut -d: -f2- | xargs || echo "Unknown")
            
            echo "     📅 $created_date"
            echo "     🌿 Branch: $branch_info"
            
            # Validate backup completeness
            roles_file="$backup_dir/${backup_name}_roles.sql"
            schema_file="$backup_dir/${backup_name}_schema.sql"
            data_file="$backup_dir/${backup_name}_data.sql"
            
            if [ -f "$roles_file" ] && [ -f "$schema_file" ] && [ -f "$data_file" ]; then
                echo -e "     ${GREEN}✅ Complete backup${NC}"
            else
                echo -e "     ${RED}⚠️  Incomplete backup${NC}"
            fi
        fi
        echo ""
    done
    
    if [ "$found_backups" = false ]; then
        echo -e "${YELLOW}📭 No backups found${NC}"
        echo "   Create a backup first with: pnpm backup:local"
        return 1
    fi
    
    return 0
}

# Function to safely restore database
restore_database() {
    local timestamp="$1"
    local backup_dir=""
    local backup_name=""
    
    # Find the backup directory
    for potential_dir in $(find "$BACKUP_BASE_DIR" -maxdepth 1 -type d \( -name "local_db_*" -o -name "remote_*" \) 2>/dev/null); do
        dir_name=$(basename "$potential_dir")
        if [[ "$dir_name" == local_db_* ]] && [[ "$dir_name" =~ $timestamp ]]; then
            backup_dir="$potential_dir"
            backup_name="$dir_name"
            break
        elif [[ "$dir_name" == remote_* ]] && [[ "$dir_name" =~ $timestamp ]]; then
            backup_dir="$potential_dir"
            backup_name="$dir_name"
            break
        fi
    done
    
    if [ -z "$backup_dir" ]; then
        echo -e "${RED}❌ Backup not found for timestamp: $timestamp${NC}"
        show_available_backups
        exit 1
    fi
    
    # Validate backup files exist
    data_file="$backup_dir/${backup_name}_data.sql"
    
    if [ ! -f "$data_file" ]; then
        echo -e "${RED}❌ Backup data file not found: $data_file${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🔄 Restoring LOCAL database from backup: ${BOLD}$timestamp${NC}"
    echo -e "${GREEN}   Source: $(basename "$backup_dir")${NC}"
    echo ""
    
    # Final safety confirmation
    echo -e "${YELLOW}⚠️  This will REPLACE your current LOCAL development database${NC}"
    echo -e "${GREEN}✅ Your PRODUCTION database will NOT be affected${NC}"
    echo ""
    read -p "Continue with LOCAL database restore? (y/N): " -n 1 -r
    echo
    
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo -e "${YELLOW}🚫 Restore cancelled${NC}"
        exit 0
    fi
    
    echo ""
    echo -e "${BLUE}🔄 Step 1: Validating migrations...${NC}"
    if ! validate_migrations; then
        echo -e "${RED}❌ Migration validation failed. Please fix migrations first.${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🔄 Step 2: Resetting LOCAL database...${NC}"
    if ! pnpm supabase db reset --local --no-seed; then
        echo -e "${RED}❌ Database reset failed${NC}"
        exit 1
    fi
    
    echo -e "${BLUE}🔄 Step 3: Restoring data...${NC}"
    if ! psql "$LOCAL_DB_URL" -f "$data_file"; then
        echo -e "${RED}❌ Data restoration failed${NC}"
        exit 1
    fi
    
    echo ""
    echo -e "${GREEN}✅ LOCAL database restore completed successfully!${NC}"
    echo -e "${GREEN}✅ Your development environment now has the restored data${NC}"
    echo -e "${BLUE}ℹ️  You can now start your development server with: pnpm dev${NC}"
}

# Main script logic
TIMESTAMP=$1

if [ -z "$TIMESTAMP" ]; then
    show_available_backups
    echo ""
    echo -e "${BLUE}Usage: $0 <timestamp>${NC}"
    echo -e "${BLUE}Example: $0 20250801_104536${NC}"
    exit 0
fi

restore_database "$TIMESTAMP"
