#!/bin/bash

# Migration Validation Script
# Validates SQL migrations before deployment to catch syntax errors early

set -e

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
BOLD='\033[1m'
NC='\033[0m'

echo -e "${BOLD}${BLUE}🔍 Migration Validation${NC}"
echo ""

MIGRATIONS_DIR="supabase/migrations"
ERRORS_FOUND=false

if [ ! -d "$MIGRATIONS_DIR" ]; then
    echo -e "${RED}❌ Migrations directory not found: $MIGRATIONS_DIR${NC}"
    exit 1
fi

# Common problematic patterns to check for
declare -A PROBLEMATIC_PATTERNS
PROBLEMATIC_PATTERNS=(
    ["FOREIGN KEY using index"]="Use proper FOREIGN KEY (column) REFERENCES table(column) syntax instead"
    ["DROP TABLE.*CASCADE"]="Dropping tables with CASCADE is dangerous - consider safer alternatives"
    ["TRUNCATE.*CASCADE"]="Truncating with CASCADE can cause data loss"
    ["ALTER TABLE.*DROP COLUMN"]="Dropping columns destroys data - use incremental approach instead"
    ["CREATE OR REPLACE FUNCTION.*\$\$.*\$\$"]="Function definitions with \$\$ might have escaping issues"
)

echo -e "${BLUE}Checking migrations in $MIGRATIONS_DIR...${NC}"
echo ""

for migration_file in "$MIGRATIONS_DIR"/*.sql; do
    if [ -f "$migration_file" ]; then
        filename=$(basename "$migration_file")
        echo -e "${BLUE}📄 Checking $filename...${NC}"
        
        file_has_errors=false
        
        # Check for problematic patterns
        for pattern in "${!PROBLEMATIC_PATTERNS[@]}"; do
            if grep -n -i "$pattern" "$migration_file" > /dev/null; then
                if [ "$file_has_errors" = false ]; then
                    echo -e "${RED}❌ Issues found in $filename:${NC}"
                    file_has_errors=true
                    ERRORS_FOUND=true
                fi
                
                # Show line numbers where pattern was found
                while IFS= read -r line; do
                    line_num=$(echo "$line" | cut -d: -f1)
                    line_content=$(echo "$line" | cut -d: -f2-)
                    echo -e "${RED}   Line $line_num: $line_content${NC}"
                done < <(grep -n -i "$pattern" "$migration_file")
                
                echo -e "${YELLOW}   💡 ${PROBLEMATIC_PATTERNS[$pattern]}${NC}"
                echo ""
            fi
        done
        
        # Basic SQL syntax validation  
        # Skip quote validation as PostgreSQL legitimately uses many quotes
        # Future: Could add more sophisticated SQL parsing if needed
        
        # Check file size (warn if suspiciously large)
        file_size=$(wc -c < "$migration_file")
        if [ "$file_size" -gt 1048576 ]; then  # 1MB
            echo -e "${YELLOW}⚠️  Warning: $filename is very large ($(echo "scale=1; $file_size/1024/1024" | bc)MB)${NC}"
            echo -e "${YELLOW}   Consider breaking into smaller migrations${NC}"
            echo ""
        fi
        
        if [ "$file_has_errors" = false ]; then
            echo -e "${GREEN}   ✅ No issues detected${NC}"
        fi
        echo ""
    fi
done

# Summary
echo -e "${BOLD}📋 Validation Summary:${NC}"
if [ "$ERRORS_FOUND" = true ]; then
    echo -e "${RED}❌ Issues found in one or more migrations${NC}"
    echo -e "${YELLOW}💡 Please fix these issues before deploying to production${NC}"
    exit 1
else
    echo -e "${GREEN}✅ All migrations passed validation${NC}"
    echo -e "${GREEN}🚀 Safe to deploy to production${NC}"
    exit 0
fi
