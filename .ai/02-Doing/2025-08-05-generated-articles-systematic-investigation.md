# Generated Articles Save Issue - Systematic Investigation

**Date:** 2025-08-05  
**Issue:** Generated articles not saving changes to database  
**Status:** FRESH INVESTIGATION - Starting from current facts  
**Priority:** HIGH

---

## 🎯 Current Understanding

**Problem Statement:** When editing certain articles, changes appear to save successfully (200 OK response) but don't persist to the database.

**Key Questions to Resolve:**

1. Which specific articles are affected?
2. What constitutes a "generated" vs "curated" article in the current system?
3. What is the exact save behavior we're trying to fix?

---

## 📊 Current Database State

**Total Articles:** 93 (all marked as `curated`)
**Articles with Source Data:** Multiple articles have `sources_tab_source_url` and `sources_tab_source_feed_id`

**Observation:** Previous documents mentioned 90 generated articles, but database shows all as curated. This suggests either:

- Articles were converted during previous debugging
- There's confusion about article classification
- The issue is different than initially understood

---

## 🔍 Investigation Plan

### Phase 1: Establish Facts

- [x] Identify specific problematic article(s) - **Article 217**
- [x] Verify current article_type and characteristics - **Curated with source data**
- [ ] Test simple save operation (e.g., title change)
- [ ] Document exact behavior observed

**Article 217 Details:**

- ID: 217
- Current articleType: `curated` (but has source data - originally generated)
- Title: "Expert Insights: Rie on Unprecedented Market Conditions"
- Last Updated: 2025-07-28 (8 days ago)
- Has Source URL: Yes

### Phase 2: Add Targeted Logging

- [x] Add save lifecycle logging to Articles collection - **COMPLETED**
- [x] Track before/after database states - **COMPLETED**
- [x] Monitor for errors, exceptions, or transaction issues - **COMPLETED**

**Logging Added:**

- `beforeChange` hook: Tracks save attempts, completion, and failures
- `afterChange` hook: Verifies database persistence with timestamp/title comparison
- Database verification: Compares PayloadCMS doc vs actual database state

### Phase 3: Compare Working vs Non-Working

- [ ] Test same operation on confirmed working article
- [ ] Identify key differences in data or behavior
- [ ] Document patterns

### Phase 4: Root Cause & Fix

- [ ] Based on evidence, identify failure point
- [ ] Implement targeted solution
- [ ] Verify fix works for all affected articles

---

## 🧪 Test Protocol

### Step 1: Reproduce Issue

1. User identifies specific problematic article
2. Make simple change (title edit)
3. Save and verify behavior
4. Check database for persistence

### Step 2: Add Logging

```typescript
// Add to Articles.ts beforeChange hook
console.log('🔍 SAVE ATTEMPT:', {
  articleId: originalDoc?.id,
  operation,
  changes: Object.keys(data),
  timestamp: new Date().toISOString(),
});
```

### Step 3: Database Verification

```sql
-- Check article state before/after save
SELECT id, title, updated_at, article_type
FROM articles
WHERE id = [ARTICLE_ID];
```

---

## 📝 Next Steps

**Immediate Actions:**

1. Get specific article ID from user
2. Reproduce the issue with minimal test case
3. Add targeted logging
4. Document findings

**Success Criteria:**

- Clear reproduction of the issue
- Understanding of root cause
- Targeted fix that resolves the problem
- No regression for working articles

---

## 🚨 Key Principles

1. **Start Simple:** Focus on one specific article and one simple change
2. **Verify Everything:** Don't assume previous investigations were correct
3. **Add Minimal Logging:** Only log what's needed to understand the issue
4. **Test Incrementally:** Make small changes and verify each step

---

_This investigation starts fresh with systematic debugging approach._
