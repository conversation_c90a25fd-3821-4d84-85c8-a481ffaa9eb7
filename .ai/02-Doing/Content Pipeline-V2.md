# Content Pipeline V2

The goal is to massively simplify and improve the content processing pipeline, removing any technical debt from previous edits and additional API into a single, pipeline api that has a number of parameters.


1. Delete the /run-single-pipeline and /run-test-pipelines. There is now just run called run-content-pipeline.
2. Logging is enable and generated a NEW log file after each run.
3. We have all the correct field mappings in place. The prompt is dedicated to enhancement and there’s a separate prompt file for translation (we’ll cover translation improvements later)
4. There are parameters / flags etc (not sure how best to do this) where we can set controls
5. 	For example: `--extract-5` (where 5 is the max number of successful articles created per rss feed could be any number)
6. ``--test` which processes a list of predefined URLs and skips over duplication checks and keyword filtering. Its goal is to test the enhancement functionality and posting to the database.
7. RSS items should be processed oldest first
8. Titles, summary, keywords, insights, related companies all need to be extracted.
9. Source fields needs to be correctly extracted and added to the cms
10. Eventually this needs to run directly off of the production server so the ability to either run as a chronic or via a dashboard button (we’ll get to that later).
11. Adhere to best security, next.js and payload best practices.
12. Email notifications when completed with detailed summary including errors
13. How do we create robust tests around the end to end functionality. I want to know the pipeline works before every PR
14. Summary metrics dont work, lets get this all working correctly:
15. The enhancement process needs to create smaller paragraphs.
16. Gather all the files we need to change and build this into the plan. A lot of the changes will be within here: src/lib/integrations (but there will be more).
17. Dont affect other parts of the codebase. We’re focusing on the processing pipeline.

```
📊 PIPELINE PERFORMANCE METRICS
════════════════════════════════
⏱️  Duration: 3134.36s
📈 Success Rate: 0.0%
🔄 Throughput: 0.0 articles/min
💰 Total Cost: $0.1780

📡 FEEDS PROCESSED: 0
📄 Articles Scraped: 0
✅ Articles Accepted: 0
❌ Articles Rejected: 0

🌐 FIRECRAWL API:
   Requests: 89
   Success: 89
   Rate Limited: 0
   Avg Response: 2000ms
   Cost: $0.1780

🤖 OPENAI API:
   Requests: 0
   Tokens: 0
   Success Rate: 0.0%
   Avg Response: 0ms
   Cost: $0.0000

💾 SYSTEM:
   Memory: 1247MB
   Parallel Efficiency: 99.8%

🎯 QUALITY:
   Content Quality: 0.0%
   Duplicate Filter: 0.0%
   Keyword Accuracy: 0.0%
════════════════════════════════
```
You can see that a lot of the metrics are zero (not true as this just ran a huge scrape)


