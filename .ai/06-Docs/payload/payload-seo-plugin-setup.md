---
description: How to install and configure PayloadCMS SEO Plugin with proper field integration
---

# How to Install PayloadCMS SEO Plugin

## Overview

This guide shows how to properly configure the PayloadCMS SEO plugin with manual field integration to maintain existing tab structures while adding SEO functionality.

## 1. Plugin Configuration in payload.config.ts

```typescript
import { seoPlugin } from '@payloadcms/plugin-seo';

export default buildConfig({
  plugins: [
    seoPlugin({
      generateTitle: ({ doc }) => doc.title,
      generateImage: ({ doc }) => doc.meta.image,
      generateDescription: ({ doc }) => doc.summary,
      generateURL: ({ doc, collectionSlug }) =>
        `https://www.boersenblick.com/${collectionSlug}/${doc?.slug}`,
    }),
  ],
});
```

## 2. Collection Setup - Articles.ts

### Required Imports

```typescript
import {
  MetaTitleField,
  MetaDescriptionField,
  MetaImageField,
  PreviewField,
  OverviewField,
} from '@payloadcms/plugin-seo/fields';
```

### SEO Tab Configuration

```typescript
{
  type: 'tabs',
  tabs: [
    // ... existing tabs
    {
      label: 'SEO',
      name: 'meta', // Important: use 'meta' as the name
      fields: [
        MetaTitleField({
          hasGenerateFn: true, // Enables auto-generate button
        }),
        MetaDescriptionField({
          hasGenerateFn: true,
        }),
        MetaImageField({
          relationTo: 'media',
        }),
        PreviewField({
          hasGenerateFn: true,
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
        }),
        OverviewField({
          titlePath: 'meta.title',
          descriptionPath: 'meta.description',
          imagePath: 'meta.image',
        }),
      ],
    },
  ],
}
```

## Key Points

### ✅ Do:

- Use `meta` as the tab name for proper plugin integration
- Import individual field components for manual control
- Keep generation functions simple and direct
- Use `hasGenerateFn: true` to enable auto-generate buttons
- Place SEO tab within existing tab structure

### ❌ Don't:

- Use `tabbedUI: true` in plugin config (conflicts with existing tabs)
- Overcomplicate generation functions with complex nested path logic
- Use `group` type instead of proper tab structure
- Mix automated and manual field configurations

## Result

This setup provides:

- Auto-generate buttons for title, description, and image
- Validation checks with character counts
- Search engine preview functionality
- Proper integration with existing tab structure
- All SEO plugin features without breaking existing UI

## Related Files

- [payload.config.ts](mdc:src/payload.config.ts) - Main plugin configuration
- [Articles.ts](mdc:src/collections/Articles.ts) - Collection with SEO fields
